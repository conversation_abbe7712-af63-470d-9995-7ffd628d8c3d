import React, { useState } from 'react';
import { Layout as Ant<PERSON>ay<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Drawer } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  HomeOutlined,
  MessageOutlined,
  ShareAltOutlined,
  SettingOutlined,
  BuildOutlined,
  MenuOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';

const { Header, Sider, Content } = AntLayout;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '首页',
    },
    {
      key: '/chat',
      icon: <MessageOutlined />,
      label: '智能对话',
    },
    {
      key: '/knowledge-graph',
      icon: <ShareAltOutlined />,
      label: '知识图谱',
    },
    {
      key: '/schema',
      icon: <SettingOutlined />,
      label: '模板管理',
    },
    {
      key: '/build',
      icon: <BuildOutlined />,
      label: '构建图谱',
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
    setMobileMenuVisible(false);
  };

  const siderContent = (
    <div className="h-full flex flex-col">
      <div className="p-4 text-center border-b border-white/10">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="text-white"
        >
          <h2 className="text-xl font-bold gradient-text mb-0">
            GraphRAG
          </h2>
          <p className="text-sm text-white/70 mb-0">智能知识图谱系统</p>
        </motion.div>
      </div>
      
      <Menu
        theme="dark"
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        className="flex-1 bg-transparent border-r-0"
        style={{
          backgroundColor: 'transparent',
        }}
      />
    </div>
  );

  return (
    <AntLayout className="min-h-screen">
      {/* 桌面端侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        className="hidden lg:block glass-effect border-r border-white/10"
        style={{
          background: 'rgba(15, 23, 42, 0.8)',
          backdropFilter: 'blur(10px)',
        }}
        width={250}
        collapsedWidth={80}
      >
        {siderContent}
      </Sider>

      {/* 移动端抽屉菜单 */}
      <Drawer
        title={
          <div className="text-white">
            <h2 className="text-lg font-bold gradient-text mb-0">GraphRAG</h2>
            <p className="text-sm text-white/70 mb-0">智能知识图谱系统</p>
          </div>
        }
        placement="left"
        onClose={() => setMobileMenuVisible(false)}
        open={mobileMenuVisible}
        className="lg:hidden"
        styles={{
          body: { padding: 0, background: 'rgba(15, 23, 42, 0.95)' },
          header: { background: 'rgba(15, 23, 42, 0.95)', borderBottom: '1px solid rgba(255,255,255,0.1)' }
        }}
      >
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ background: 'transparent', border: 'none' }}
        />
      </Drawer>

      <AntLayout>
        {/* 顶部导航栏 */}
        <Header className="glass-effect border-b border-white/10 px-4 flex items-center justify-between lg:justify-end">
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={() => setMobileMenuVisible(true)}
            className="lg:hidden text-white hover:bg-white/10"
          />
          
          <div className="hidden lg:flex items-center">
            <Button
              type="text"
              icon={collapsed ? <MenuOutlined /> : <MenuOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="text-white hover:bg-white/10"
            />
          </div>
        </Header>

        {/* 主内容区域 */}
        <Content className="p-6 overflow-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="h-full"
          >
            {children}
          </motion.div>
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
