import React from 'react';
import { Card, Row, Col, Button, Typography, Statistic } from 'antd';
import { useNavigate } from 'react-router-dom';
import {
  MessageOutlined,
  ShareAltOutlined,
  SettingOutlined,
  BuildOutlined,
  RobotOutlined,
  DatabaseOutlined,
  ThunderboltOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';

const { Title, Paragraph } = Typography;

const HomePage: React.FC = () => {
  const navigate = useNavigate();

  const features = [
    {
      key: 'chat',
      title: '智能对话',
      description: '与AI助手进行自然语言对话，获取基于知识图谱的智能回答',
      icon: <MessageOutlined className="text-4xl text-blue-500" />,
      path: '/chat',
      color: 'from-blue-500 to-cyan-500',
    },
    {
      key: 'knowledge-graph',
      title: '知识图谱可视化',
      description: '交互式查看和探索知识图谱，发现实体间的复杂关系',
      icon: <ShareAltOutlined className="text-4xl text-green-500" />,
      path: '/knowledge-graph',
      color: 'from-green-500 to-emerald-500',
    },
    {
      key: 'schema',
      title: '模板管理',
      description: '创建和管理知识图谱模板，定义实体类型和关系类型',
      icon: <SettingOutlined className="text-4xl text-purple-500" />,
      path: '/schema',
      color: 'from-purple-500 to-pink-500',
    },
    {
      key: 'build',
      title: '构建图谱',
      description: '上传文档并构建知识图谱，将非结构化数据转化为结构化知识',
      icon: <BuildOutlined className="text-4xl text-orange-500" />,
      path: '/build',
      color: 'from-orange-500 to-red-500',
    },
  ];

  const stats = [
    {
      title: '智能Agent',
      value: 5,
      suffix: '种',
      icon: <RobotOutlined className="text-blue-500" />,
    },
    {
      title: '知识实体',
      value: 1000,
      suffix: '+',
      icon: <DatabaseOutlined className="text-green-500" />,
    },
    {
      title: '响应速度',
      value: 0.5,
      suffix: 's',
      precision: 1,
      icon: <ThunderboltOutlined className="text-yellow-500" />,
    },
    {
      title: '可视化节点',
      value: 500,
      suffix: '+',
      icon: <EyeOutlined className="text-purple-500" />,
    },
  ];

  return (
    <div className="min-h-full">
      {/* 英雄区域 */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="text-center mb-12"
      >
        <div className="glass-effect rounded-2xl p-8 mb-8">
          <Title level={1} className="text-white mb-4">
            <span className="gradient-text">GraphRAG</span> 智能知识图谱系统
          </Title>
          <Paragraph className="text-xl text-white/80 mb-6 max-w-3xl mx-auto">
            基于大语言模型的智能知识图谱构建与问答系统，支持多种Agent策略，
            提供强大的知识抽取、图谱可视化和智能问答能力。
          </Paragraph>
          <div className="flex flex-wrap justify-center gap-4">
            <Button
              type="primary"
              size="large"
              icon={<MessageOutlined />}
              onClick={() => navigate('/chat')}
              className="bg-gradient-to-r from-blue-500 to-cyan-500 border-0 hover:shadow-lg"
            >
              开始对话
            </Button>
            <Button
              size="large"
              icon={<ShareAltOutlined />}
              onClick={() => navigate('/knowledge-graph')}
              className="glass-effect text-white border-white/20 hover:bg-white/10"
            >
              查看图谱
            </Button>
          </div>
        </div>
      </motion.div>

      {/* 统计数据 */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.2 }}
        className="mb-12"
      >
        <Row gutter={[24, 24]}>
          {stats.map((stat, index) => (
            <Col xs={12} sm={6} key={index}>
              <Card className="glass-effect border-white/20 text-center hover:shadow-lg transition-all duration-300">
                <div className="mb-2">{stat.icon}</div>
                <Statistic
                  title={<span className="text-white/80">{stat.title}</span>}
                  value={stat.value}
                  suffix={stat.suffix}
                  precision={stat.precision}
                  valueStyle={{ color: '#fff', fontSize: '24px', fontWeight: 'bold' }}
                />
              </Card>
            </Col>
          ))}
        </Row>
      </motion.div>

      {/* 功能卡片 */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
      >
        <Title level={2} className="text-white text-center mb-8">
          核心功能
        </Title>
        <Row gutter={[24, 24]}>
          {features.map((feature, index) => (
            <Col xs={24} sm={12} lg={6} key={feature.key}>
              <motion.div
                whileHover={{ scale: 1.05, y: -5 }}
                transition={{ duration: 0.3 }}
              >
                <Card
                  className="glass-effect border-white/20 h-full hover:shadow-xl transition-all duration-300 cursor-pointer"
                  onClick={() => navigate(feature.path)}
                  bodyStyle={{ padding: '24px' }}
                >
                  <div className="text-center">
                    <div className="mb-4 float-animation">
                      {feature.icon}
                    </div>
                    <Title level={4} className="text-white mb-3">
                      {feature.title}
                    </Title>
                    <Paragraph className="text-white/70 mb-4">
                      {feature.description}
                    </Paragraph>
                    <Button
                      type="primary"
                      block
                      className={`bg-gradient-to-r ${feature.color} border-0 hover:shadow-lg`}
                    >
                      立即体验
                    </Button>
                  </div>
                </Card>
              </motion.div>
            </Col>
          ))}
        </Row>
      </motion.div>
    </div>
  );
};

export default HomePage;
