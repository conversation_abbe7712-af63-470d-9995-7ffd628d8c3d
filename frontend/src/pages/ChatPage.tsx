import React, { useState, useRef, useEffect } from 'react';
import {
  Card,
  Input,
  Button,
  Select,
  Switch,
  Space,
  Typography,
  Divider,
  message,
  Spin,
  Tag,
  Collapse,
} from 'antd';
import {
  SendOutlined,
  ClearOutlined,
  RobotOutlined,
  UserOutlined,
  ThunderboltOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { motion, AnimatePresence } from 'framer-motion';
import { chatAPI } from '../services/api';
import type { ChatMessage, AgentType } from '../types';

const { TextArea } = Input;
const { Title, Paragraph, Text } = Typography;
const { Panel } = Collapse;

const ChatPage: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [agentType, setAgentType] = useState<AgentType>('graph_agent');
  const [showThinking, setShowThinking] = useState(false);
  const [debugMode, setDebugMode] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const agentOptions = [
    { value: 'graph_agent', label: '图谱Agent', description: '基于知识图谱的智能问答' },
    { value: 'hybrid_agent', label: '混合Agent', description: '结合多种检索策略' },
    { value: 'naive_rag_agent', label: '朴素RAG', description: '传统RAG检索问答' },
    { value: 'deep_research_agent', label: '深度研究Agent', description: '深度分析和研究' },
    { value: 'fusion_agent', label: '融合Agent', description: '多Agent协作' },
  ];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim() || loading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputValue,
      role: 'user',
      timestamp: Date.now(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setLoading(true);

    try {
      let assistantContent = '';
      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: '',
        role: 'assistant',
        timestamp: Date.now(),
      };

      setMessages(prev => [...prev, assistantMessage]);

      await chatAPI.sendMessageStream(
        {
          message: inputValue,
          agent_type: agentType,
          debug: debugMode,
          show_thinking: showThinking,
        },
        (token) => {
          assistantContent += token;
          setMessages(prev => 
            prev.map(msg => 
              msg.id === assistantMessage.id 
                ? { ...msg, content: assistantContent }
                : msg
            )
          );
        },
        (response) => {
          setMessages(prev => 
            prev.map(msg => 
              msg.id === assistantMessage.id 
                ? { 
                    ...msg, 
                    content: response.response,
                    sources: response.sources,
                    thinking: response.thinking,
                  }
                : msg
            )
          );
        }
      );
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败，请重试');
      setMessages(prev => prev.slice(0, -1));
    } finally {
      setLoading(false);
    }
  };

  const handleClearChat = async () => {
    try {
      await chatAPI.clearChat();
      setMessages([]);
      message.success('聊天记录已清除');
    } catch (error) {
      console.error('清除聊天记录失败:', error);
      message.error('清除聊天记录失败');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="mb-6">
        <Title level={2} className="text-white mb-4">
          <RobotOutlined className="mr-2" />
          智能对话
        </Title>
        
        {/* 控制面板 */}
        <Card className="glass-effect border-white/20 mb-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <Text className="text-white">Agent类型:</Text>
              <Select
                value={agentType}
                onChange={setAgentType}
                style={{ width: 200 }}
                options={agentOptions}
              />
            </div>
            
            <div className="flex items-center gap-2">
              <Text className="text-white">显示推理过程:</Text>
              <Switch
                checked={showThinking}
                onChange={setShowThinking}
                disabled={agentType !== 'deep_research_agent'}
              />
            </div>
            
            <div className="flex items-center gap-2">
              <Text className="text-white">调试模式:</Text>
              <Switch checked={debugMode} onChange={setDebugMode} />
            </div>
            
            <Button
              icon={<ClearOutlined />}
              onClick={handleClearChat}
              className="glass-effect text-white border-white/20"
            >
              清除记录
            </Button>
          </div>
        </Card>
      </div>

      {/* 消息列表 */}
      <div className="flex-1 overflow-hidden">
        <Card className="glass-effect border-white/20 h-full">
          <div className="h-full flex flex-col">
            <div className="flex-1 overflow-y-auto pr-2" style={{ maxHeight: 'calc(100vh - 300px)' }}>
              <AnimatePresence>
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                    className={`mb-4 flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`max-w-[80%] ${message.role === 'user' ? 'order-2' : 'order-1'}`}>
                      <div className="flex items-center gap-2 mb-2">
                        {message.role === 'user' ? (
                          <UserOutlined className="text-blue-400" />
                        ) : (
                          <RobotOutlined className="text-green-400" />
                        )}
                        <Text className="text-white/70 text-sm">
                          {message.role === 'user' ? '用户' : 'AI助手'}
                        </Text>
                        <Text className="text-white/50 text-xs">
                          {new Date(message.timestamp).toLocaleTimeString()}
                        </Text>
                      </div>
                      
                      <div
                        className={`p-4 rounded-lg ${
                          message.role === 'user'
                            ? 'bg-blue-500/20 border border-blue-500/30'
                            : 'bg-gray-700/30 border border-gray-600/30'
                        }`}
                      >
                        <Paragraph className="text-white mb-0 whitespace-pre-wrap">
                          {message.content}
                        </Paragraph>
                        
                        {/* 思考过程 */}
                        {message.thinking && (
                          <Collapse className="mt-3" ghost>
                            <Panel
                              header={
                                <span className="text-yellow-400">
                                  <ThunderboltOutlined className="mr-1" />
                                  推理过程
                                </span>
                              }
                              key="thinking"
                            >
                              <Paragraph className="text-white/80 whitespace-pre-wrap">
                                {message.thinking}
                              </Paragraph>
                            </Panel>
                          </Collapse>
                        )}
                        
                        {/* 来源信息 */}
                        {message.sources && message.sources.length > 0 && (
                          <div className="mt-3">
                            <Text className="text-white/70 text-sm">
                              <EyeOutlined className="mr-1" />
                              参考来源:
                            </Text>
                            <div className="flex flex-wrap gap-1 mt-1">
                              {message.sources.map((source, index) => (
                                <Tag key={index} color="blue" className="text-xs">
                                  {source.title || source.source}
                                </Tag>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
              
              {loading && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="flex justify-start mb-4"
                >
                  <div className="flex items-center gap-2 p-4 bg-gray-700/30 border border-gray-600/30 rounded-lg">
                    <Spin size="small" />
                    <Text className="text-white/70">AI正在思考...</Text>
                  </div>
                </motion.div>
              )}
              
              <div ref={messagesEndRef} />
            </div>
            
            <Divider className="border-white/20 my-4" />
            
            {/* 输入区域 */}
            <div className="flex gap-2">
              <TextArea
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="输入您的问题..."
                autoSize={{ minRows: 1, maxRows: 4 }}
                className="flex-1"
                disabled={loading}
              />
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={handleSendMessage}
                loading={loading}
                disabled={!inputValue.trim()}
                className="bg-gradient-to-r from-blue-500 to-cyan-500 border-0"
              >
                发送
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ChatPage;
