import React, { useState, useEffect } from 'react';
import {
  Card,
  Steps,
  Button,
  Select,
  Upload,
  Typography,
  message,
  Progress,
  Result,
  Space,
  List,
  Tag,
} from 'antd';
import {
  BuildOutlined,
  UploadOutlined,
  FileTextOutlined,
  CheckCircleOutlined,
  LoadingOutlined,
  SettingOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { buildAPI, schemaAPI } from '../services/api';
import type { Schema, BuildStatus } from '../types';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;
const { Dragger } = Upload;

const BuildKGPage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [schemas, setSchemas] = useState<Schema[]>([]);
  const [selectedSchema, setSelectedSchema] = useState<string>('');
  const [fileList, setFileList] = useState<any[]>([]);
  const [building, setBuilding] = useState(false);
  const [buildProgress, setBuildProgress] = useState(0);
  const [buildStatus, setBuildStatus] = useState<BuildStatus | null>(null);
  const [buildResult, setBuildResult] = useState<any>(null);

  const loadSchemas = async () => {
    try {
      const data = await schemaAPI.getSchemas();
      setSchemas(data);
    } catch (error) {
      console.error('加载模板失败:', error);
      message.error('加载模板失败');
    }
  };

  const loadBuildStatus = async () => {
    try {
      const status = await buildAPI.getStatus();
      setBuildStatus(status);
    } catch (error) {
      console.error('获取构建状态失败:', error);
    }
  };

  const handleFileChange = ({ fileList: newFileList }: any) => {
    setFileList(newFileList);
  };

  const handleBuild = async () => {
    if (!selectedSchema) {
      message.error('请选择一个模板');
      return;
    }
    
    if (fileList.length === 0) {
      message.error('请上传至少一个文件');
      return;
    }

    setBuilding(true);
    setBuildProgress(0);
    
    try {
      const files = fileList.map(file => file.originFileObj);
      const result = await buildAPI.buildKG(selectedSchema, files);
      
      // 模拟构建进度
      const progressInterval = setInterval(() => {
        setBuildProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 1000);

      setBuildResult(result);
      setBuildProgress(100);
      setCurrentStep(3);
      message.success('知识图谱构建完成！');
      
      // 重新加载构建状态
      setTimeout(() => {
        loadBuildStatus();
      }, 2000);
      
    } catch (error) {
      console.error('构建失败:', error);
      message.error('构建失败，请重试');
    } finally {
      setBuilding(false);
    }
  };

  const steps = [
    {
      title: '选择模板',
      description: '选择知识图谱构建模板',
      icon: <SettingOutlined />,
    },
    {
      title: '上传文档',
      description: '上传要处理的文档文件',
      icon: <UploadOutlined />,
    },
    {
      title: '开始构建',
      description: '执行知识图谱构建过程',
      icon: <BuildOutlined />,
    },
    {
      title: '构建完成',
      description: '查看构建结果',
      icon: <CheckCircleOutlined />,
    },
  ];

  const uploadProps = {
    name: 'file',
    multiple: true,
    accept: '.txt,.pdf,.doc,.docx,.md',
    beforeUpload: () => false, // 阻止自动上传
    onChange: handleFileChange,
    onDrop(e: any) {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };

  useEffect(() => {
    loadSchemas();
    loadBuildStatus();
  }, []);

  return (
    <div className="h-full">
      <div className="mb-6">
        <Title level={2} className="text-white mb-4">
          <BuildOutlined className="mr-2" />
          构建知识图谱
        </Title>
        <Paragraph className="text-white/80">
          通过上传文档并选择合适的模板来构建知识图谱
        </Paragraph>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="glass-effect border-white/20 mb-6">
          <Steps current={currentStep} className="mb-8">
            {steps.map((step, index) => (
              <Step
                key={index}
                title={<span className="text-white">{step.title}</span>}
                description={<span className="text-white/70">{step.description}</span>}
                icon={step.icon}
              />
            ))}
          </Steps>

          {/* 步骤内容 */}
          <div className="min-h-[400px]">
            {currentStep === 0 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Title level={4} className="text-white mb-4">选择构建模板</Title>
                <Select
                  placeholder="请选择一个模板"
                  style={{ width: '100%', marginBottom: 16 }}
                  value={selectedSchema}
                  onChange={setSelectedSchema}
                  size="large"
                >
                  {schemas.map(schema => (
                    <Select.Option key={schema.id} value={schema.theme}>
                      <div>
                        <div className="font-medium">{schema.theme}</div>
                        <div className="text-sm text-gray-500">
                          实体类型: {schema.entity_types.slice(0, 3).join(', ')}
                          {schema.entity_types.length > 3 && '...'}
                        </div>
                      </div>
                    </Select.Option>
                  ))}
                </Select>
                
                {selectedSchema && (
                  <Card className="bg-blue-50/10 border-blue-200/20 mt-4">
                    <Text strong className="text-white">已选择模板: {selectedSchema}</Text>
                    {schemas.find(s => s.theme === selectedSchema) && (
                      <div className="mt-2">
                        <div className="mb-2">
                          <Text className="text-sm text-white/80">实体类型: </Text>
                          {schemas.find(s => s.theme === selectedSchema)!.entity_types.map((type, index) => (
                            <Tag key={index} color="blue" className="text-xs">{type}</Tag>
                          ))}
                        </div>
                        <div>
                          <Text className="text-sm text-white/80">关系类型: </Text>
                          {schemas.find(s => s.theme === selectedSchema)!.relationship_types.map((type, index) => (
                            <Tag key={index} color="green" className="text-xs">{type}</Tag>
                          ))}
                        </div>
                      </div>
                    )}
                  </Card>
                )}
                
                <div className="text-right mt-6">
                  <Button
                    type="primary"
                    onClick={() => setCurrentStep(1)}
                    disabled={!selectedSchema}
                    className="bg-gradient-to-r from-blue-500 to-cyan-500 border-0"
                  >
                    下一步
                  </Button>
                </div>
              </motion.div>
            )}

            {currentStep === 1 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Title level={4} className="text-white mb-4">上传文档</Title>
                <Dragger {...uploadProps} className="mb-4">
                  <p className="ant-upload-drag-icon">
                    <FileTextOutlined className="text-4xl text-blue-400" />
                  </p>
                  <p className="ant-upload-text text-white">
                    点击或拖拽文件到此区域上传
                  </p>
                  <p className="ant-upload-hint text-white/70">
                    支持 .txt, .pdf, .doc, .docx, .md 格式的文件
                  </p>
                </Dragger>
                
                {fileList.length > 0 && (
                  <List
                    className="mt-4"
                    dataSource={fileList}
                    renderItem={(file: any) => (
                      <List.Item className="text-white">
                        <FileTextOutlined className="mr-2" />
                        {file.name}
                        <Tag color="green" className="ml-2">
                          {(file.size / 1024).toFixed(1)} KB
                        </Tag>
                      </List.Item>
                    )}
                  />
                )}
                
                <div className="flex justify-between mt-6">
                  <Button onClick={() => setCurrentStep(0)}>
                    上一步
                  </Button>
                  <Button
                    type="primary"
                    onClick={() => setCurrentStep(2)}
                    disabled={fileList.length === 0}
                    className="bg-gradient-to-r from-blue-500 to-cyan-500 border-0"
                  >
                    下一步
                  </Button>
                </div>
              </motion.div>
            )}

            {currentStep === 2 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
                className="text-center"
              >
                <Title level={4} className="text-white mb-4">开始构建</Title>
                
                {!building && !buildResult && (
                  <div>
                    <Paragraph className="text-white/80 mb-6">
                      准备构建知识图谱，请确认以下信息：
                    </Paragraph>
                    <Card className="bg-gray-50/10 border-gray-200/20 mb-6">
                      <div className="text-left">
                        <div className="mb-2">
                          <Text strong className="text-white">模板: </Text>
                          <Text className="text-white/80">{selectedSchema}</Text>
                        </div>
                        <div>
                          <Text strong className="text-white">文件数量: </Text>
                          <Text className="text-white/80">{fileList.length} 个文件</Text>
                        </div>
                      </div>
                    </Card>
                    <Space>
                      <Button onClick={() => setCurrentStep(1)}>
                        返回修改
                      </Button>
                      <Button
                        type="primary"
                        icon={<BuildOutlined />}
                        onClick={handleBuild}
                        className="bg-gradient-to-r from-green-500 to-emerald-500 border-0"
                      >
                        开始构建
                      </Button>
                    </Space>
                  </div>
                )}
                
                {building && (
                  <div>
                    <LoadingOutlined className="text-4xl text-blue-400 mb-4" />
                    <Title level={4} className="text-white mb-4">正在构建知识图谱...</Title>
                    <Progress
                      percent={buildProgress}
                      status="active"
                      strokeColor={{
                        '0%': '#108ee9',
                        '100%': '#87d068',
                      }}
                      className="mb-4"
                    />
                    <Paragraph className="text-white/70">
                      这可能需要几分钟时间，请耐心等待...
                    </Paragraph>
                  </div>
                )}
              </motion.div>
            )}

            {currentStep === 3 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3 }}
              >
                <Result
                  status="success"
                  title={<span className="text-white">知识图谱构建完成！</span>}
                  subTitle={<span className="text-white/70">您可以现在查看构建的知识图谱</span>}
                  extra={[
                    <Button
                      type="primary"
                      key="view"
                      onClick={() => window.open('/knowledge-graph', '_blank')}
                      className="bg-gradient-to-r from-blue-500 to-cyan-500 border-0"
                    >
                      查看知识图谱
                    </Button>,
                    <Button key="restart" onClick={() => {
                      setCurrentStep(0);
                      setFileList([]);
                      setBuildResult(null);
                      setBuildProgress(0);
                    }}>
                      重新构建
                    </Button>,
                  ]}
                />
                
                {buildStatus && (
                  <Card className="glass-effect border-white/20 mt-4">
                    <Title level={5} className="text-white mb-3">构建状态</Title>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center">
                        <Text className="text-white/70">输出状态</Text>
                        <div className="text-white font-medium">
                          {buildStatus.has_output ? '✅ 已生成' : '❌ 未生成'}
                        </div>
                      </div>
                      <div className="text-center">
                        <Text className="text-white/70">缓存状态</Text>
                        <div className="text-white font-medium">
                          {buildStatus.has_cache ? '✅ 已缓存' : '❌ 未缓存'}
                        </div>
                      </div>
                      <div className="text-center">
                        <Text className="text-white/70">文件数量</Text>
                        <div className="text-white font-medium">{buildStatus.files_count}</div>
                      </div>
                      <div className="text-center">
                        <Text className="text-white/70">就绪状态</Text>
                        <div className="text-white font-medium">
                          {buildStatus.is_ready ? '✅ 就绪' : '❌ 未就绪'}
                        </div>
                      </div>
                    </div>
                  </Card>
                )}
              </motion.div>
            )}
          </div>
        </Card>
      </motion.div>
    </div>
  );
};

export default BuildKGPage;
