import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Space,
  Typography,
  message,
  Popconfirm,
  Tag,
  Divider,
} from 'antd';
import {
  SettingOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { schemaAPI } from '../services/api';
import type { Schema } from '../types';

const { Title, Text } = Typography;
const { TextArea } = Input;

const SchemaManagePage: React.FC = () => {
  const [schemas, setSchemas] = useState<Schema[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingSchema, setEditingSchema] = useState<Schema | null>(null);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [viewingSchema, setViewingSchema] = useState<Schema | null>(null);
  const [form] = Form.useForm();

  const loadSchemas = async () => {
    setLoading(true);
    try {
      const data = await schemaAPI.getSchemas();
      setSchemas(data);
    } catch (error) {
      console.error('加载模板失败:', error);
      message.error('加载模板失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateOrUpdate = async (values: any) => {
    try {
      const schemaData = {
        theme: values.theme,
        entity_types: values.entity_types.split(',').map((s: string) => s.trim()).filter(Boolean),
        relationship_types: values.relationship_types.split(',').map((s: string) => s.trim()).filter(Boolean),
      };

      if (editingSchema) {
        await schemaAPI.updateSchema(editingSchema.id!, schemaData);
        message.success('模板更新成功');
      } else {
        await schemaAPI.createSchema(schemaData);
        message.success('模板创建成功');
      }

      setModalVisible(false);
      setEditingSchema(null);
      form.resetFields();
      loadSchemas();
    } catch (error) {
      console.error('保存模板失败:', error);
      message.error('保存模板失败');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await schemaAPI.deleteSchema(id);
      message.success('模板删除成功');
      loadSchemas();
    } catch (error) {
      console.error('删除模板失败:', error);
      message.error('删除模板失败');
    }
  };

  const handleEdit = (schema: Schema) => {
    setEditingSchema(schema);
    form.setFieldsValue({
      theme: schema.theme,
      entity_types: schema.entity_types.join(', '),
      relationship_types: schema.relationship_types.join(', '),
    });
    setModalVisible(true);
  };

  const handleView = (schema: Schema) => {
    setViewingSchema(schema);
    setViewModalVisible(true);
  };

  const columns = [
    {
      title: '主题',
      dataIndex: 'theme',
      key: 'theme',
      render: (text: string) => (
        <Text className="text-white font-medium">{text}</Text>
      ),
    },
    {
      title: '实体类型',
      dataIndex: 'entity_types',
      key: 'entity_types',
      render: (types: string[]) => (
        <div className="flex flex-wrap gap-1">
          {types.slice(0, 3).map((type, index) => (
            <Tag key={index} color="blue" className="text-xs">
              {type}
            </Tag>
          ))}
          {types.length > 3 && (
            <Tag color="default" className="text-xs">
              +{types.length - 3}
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: '关系类型',
      dataIndex: 'relationship_types',
      key: 'relationship_types',
      render: (types: string[]) => (
        <div className="flex flex-wrap gap-1">
          {types.slice(0, 3).map((type, index) => (
            <Tag key={index} color="green" className="text-xs">
              {type}
            </Tag>
          ))}
          {types.length > 3 && (
            <Tag color="default" className="text-xs">
              +{types.length - 3}
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => (
        <Text className="text-white/70 text-sm">
          {date ? new Date(date).toLocaleString() : '-'}
        </Text>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: Schema) => (
        <Space>
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
            className="text-blue-400 hover:bg-blue-400/10"
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            className="text-green-400 hover:bg-green-400/10"
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个模板吗？"
            onConfirm={() => handleDelete(record.id!)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              icon={<DeleteOutlined />}
              className="text-red-400 hover:bg-red-400/10"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  useEffect(() => {
    loadSchemas();
  }, []);

  return (
    <div className="h-full">
      <div className="mb-6">
        <Title level={2} className="text-white mb-4">
          <SettingOutlined className="mr-2" />
          模板管理
        </Title>
        
        <div className="flex justify-between items-center mb-4">
          <Text className="text-white/80">
            管理知识图谱构建模板，定义实体类型和关系类型
          </Text>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingSchema(null);
              form.resetFields();
              setModalVisible(true);
            }}
            className="bg-gradient-to-r from-blue-500 to-cyan-500 border-0"
          >
            创建模板
          </Button>
        </div>
      </div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Card className="glass-effect border-white/20">
          <Table
            columns={columns}
            dataSource={schemas}
            rowKey="id"
            loading={loading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条记录`,
            }}
            className="custom-table"
          />
        </Card>
      </motion.div>

      {/* 创建/编辑模板弹窗 */}
      <Modal
        title={editingSchema ? '编辑模板' : '创建模板'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingSchema(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateOrUpdate}
        >
          <Form.Item
            name="theme"
            label="主题名称"
            rules={[{ required: true, message: '请输入主题名称' }]}
          >
            <Input placeholder="例如：人工智能、医学知识等" />
          </Form.Item>
          
          <Form.Item
            name="entity_types"
            label="实体类型"
            rules={[{ required: true, message: '请输入实体类型' }]}
            extra="多个类型用逗号分隔，例如：人物, 组织, 地点, 概念"
          >
            <TextArea
              rows={3}
              placeholder="人物, 组织, 地点, 概念, 技术, 产品"
            />
          </Form.Item>
          
          <Form.Item
            name="relationship_types"
            label="关系类型"
            rules={[{ required: true, message: '请输入关系类型' }]}
            extra="多个类型用逗号分隔，例如：属于, 包含, 相关, 影响"
          >
            <TextArea
              rows={3}
              placeholder="属于, 包含, 相关, 影响, 合作, 竞争"
            />
          </Form.Item>
          
          <Form.Item className="mb-0 text-right">
            <Space>
              <Button onClick={() => {
                setModalVisible(false);
                setEditingSchema(null);
                form.resetFields();
              }}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                className="bg-gradient-to-r from-blue-500 to-cyan-500 border-0"
              >
                {editingSchema ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 查看模板详情弹窗 */}
      <Modal
        title="模板详情"
        open={viewModalVisible}
        onCancel={() => {
          setViewModalVisible(false);
          setViewingSchema(null);
        }}
        footer={[
          <Button key="close" onClick={() => {
            setViewModalVisible(false);
            setViewingSchema(null);
          }}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {viewingSchema && (
          <div className="space-y-4">
            <div>
              <Text strong>主题名称：</Text>
              <Text>{viewingSchema.theme}</Text>
            </div>
            
            <Divider />
            
            <div>
              <Text strong>实体类型：</Text>
              <div className="mt-2 flex flex-wrap gap-2">
                {viewingSchema.entity_types.map((type, index) => (
                  <Tag key={index} color="blue">{type}</Tag>
                ))}
              </div>
            </div>
            
            <Divider />
            
            <div>
              <Text strong>关系类型：</Text>
              <div className="mt-2 flex flex-wrap gap-2">
                {viewingSchema.relationship_types.map((type, index) => (
                  <Tag key={index} color="green">{type}</Tag>
                ))}
              </div>
            </div>
            
            <Divider />
            
            <div className="flex justify-between text-sm text-gray-500">
              <span>创建时间: {viewingSchema.created_at ? new Date(viewingSchema.created_at).toLocaleString() : '-'}</span>
              <span>更新时间: {viewingSchema.updated_at ? new Date(viewingSchema.updated_at).toLocaleString() : '-'}</span>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default SchemaManagePage;
