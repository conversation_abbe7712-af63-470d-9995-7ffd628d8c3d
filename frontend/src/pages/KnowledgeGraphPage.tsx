import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Input,
  Button,
  Select,
  Space,
  Typography,
  Statistic,
  Row,
  Col,
  message,
  Spin,
  Tag,
} from 'antd';
import {
  ShareAltOutlined,
  SearchOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { motion } from 'framer-motion';
import { Network } from 'vis-network';
import { DataSet } from 'vis-data';
import { knowledgeGraphAPI } from '../services/api';
import type { KnowledgeGraphData, KnowledgeGraphNode, KnowledgeGraphLink, KGStats } from '../types';

const { Title, Text } = Typography;
const { Search } = Input;

const KnowledgeGraphPage: React.FC = () => {
  const [graphData, setGraphData] = useState<KnowledgeGraphData>({ nodes: [], links: [] });
  const [stats, setStats] = useState<KGStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedNode, setSelectedNode] = useState<KnowledgeGraphNode | null>(null);
  const [searchValue, setSearchValue] = useState('');
  const [nodeLimit, setNodeLimit] = useState(100);
  const networkRef = useRef<HTMLDivElement>(null);
  const networkInstance = useRef<Network | null>(null);

  const loadKnowledgeGraph = async (limit: number = 100) => {
    setLoading(true);
    try {
      const data = await knowledgeGraphAPI.getKnowledgeGraph(limit);
      setGraphData(data);
      renderNetwork(data);
    } catch (error) {
      console.error('加载知识图谱失败:', error);
      message.error('加载知识图谱失败');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      const statsData = await knowledgeGraphAPI.getStats();
      setStats(statsData);
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  const renderNetwork = (data: KnowledgeGraphData) => {
    if (!networkRef.current) return;

    // 准备节点数据
    const nodes = new DataSet(
      data.nodes.map(node => ({
        id: node.id,
        label: node.label,
        group: node.group,
        title: node.description || node.label,
        color: getNodeColor(node.group),
        font: { color: '#ffffff', size: 12 },
        borderWidth: 2,
        borderWidthSelected: 3,
      }))
    );

    // 准备边数据
    const edges = new DataSet(
      data.links.map((link, index) => ({
        id: index,
        from: link.source,
        to: link.target,
        label: link.label,
        width: Math.max(1, (link.weight || 1) * 2),
        color: { color: 'rgba(255,255,255,0.6)' },
        font: { color: '#ffffff', size: 10 },
        arrows: { to: { enabled: true, scaleFactor: 0.5 } },
      }))
    );

    const options = {
      nodes: {
        shape: 'dot',
        size: 20,
        font: {
          size: 12,
          color: '#ffffff',
        },
        borderWidth: 2,
        shadow: true,
      },
      edges: {
        width: 2,
        color: { inherit: 'from' },
        smooth: {
          type: 'continuous',
          roundness: 0.5,
        },
        arrows: {
          to: { enabled: true, scaleFactor: 0.5 },
        },
        font: {
          size: 10,
          color: '#ffffff',
          strokeWidth: 2,
          strokeColor: '#000000',
        },
      },
      physics: {
        enabled: true,
        stabilization: { iterations: 100 },
        barnesHut: {
          gravitationalConstant: -2000,
          centralGravity: 0.3,
          springLength: 95,
          springConstant: 0.04,
          damping: 0.09,
        },
      },
      interaction: {
        hover: true,
        selectConnectedEdges: false,
      },
      layout: {
        improvedLayout: true,
      },
    };

    // 销毁旧的网络实例
    if (networkInstance.current) {
      networkInstance.current.destroy();
    }

    // 创建新的网络实例
    networkInstance.current = new Network(networkRef.current, { nodes, edges }, options);

    // 添加事件监听器
    networkInstance.current.on('selectNode', (params) => {
      if (params.nodes.length > 0) {
        const nodeId = params.nodes[0];
        const node = data.nodes.find(n => n.id === nodeId);
        setSelectedNode(node || null);
      }
    });

    networkInstance.current.on('deselectNode', () => {
      setSelectedNode(null);
    });
  };

  const getNodeColor = (group: string) => {
    const colors: Record<string, string> = {
      'Person': '#ff6b6b',
      'Organization': '#4ecdc4',
      'Location': '#45b7d1',
      'Event': '#96ceb4',
      'Concept': '#ffeaa7',
      'Technology': '#dda0dd',
      'Product': '#98d8c8',
      'default': '#74b9ff',
    };
    return colors[group] || colors.default;
  };

  const handleSearch = (value: string) => {
    if (!networkInstance.current) return;
    
    if (value) {
      const matchingNodes = graphData.nodes.filter(node =>
        node.label.toLowerCase().includes(value.toLowerCase()) ||
        (node.description && node.description.toLowerCase().includes(value.toLowerCase()))
      );
      
      if (matchingNodes.length > 0) {
        const nodeIds = matchingNodes.map(node => node.id);
        networkInstance.current.selectNodes(nodeIds);
        networkInstance.current.focus(nodeIds[0], { scale: 1.5 });
        message.success(`找到 ${matchingNodes.length} 个匹配节点`);
      } else {
        message.warning('未找到匹配的节点');
      }
    } else {
      networkInstance.current.unselectAll();
    }
  };

  const handleFullscreen = () => {
    if (networkRef.current) {
      if (networkRef.current.requestFullscreen) {
        networkRef.current.requestFullscreen();
      }
    }
  };

  useEffect(() => {
    loadKnowledgeGraph(nodeLimit);
    loadStats();
    
    return () => {
      if (networkInstance.current) {
        networkInstance.current.destroy();
      }
    };
  }, []);

  return (
    <div className="h-full flex flex-col">
      <div className="mb-6">
        <Title level={2} className="text-white mb-4">
          <ShareAltOutlined className="mr-2" />
          知识图谱可视化
        </Title>
        
        {/* 统计信息 */}
        {stats && (
          <Row gutter={[16, 16]} className="mb-4">
            <Col xs={12} sm={6}>
              <Card className="glass-effect border-white/20 text-center">
                <Statistic
                  title={<span className="text-white/80">节点总数</span>}
                  value={stats.total_nodes}
                  valueStyle={{ color: '#fff', fontSize: '20px' }}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card className="glass-effect border-white/20 text-center">
                <Statistic
                  title={<span className="text-white/80">关系总数</span>}
                  value={stats.total_links}
                  valueStyle={{ color: '#fff', fontSize: '20px' }}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card className="glass-effect border-white/20 text-center">
                <Statistic
                  title={<span className="text-white/80">实体类型</span>}
                  value={Object.keys(stats.node_types).length}
                  valueStyle={{ color: '#fff', fontSize: '20px' }}
                />
              </Card>
            </Col>
            <Col xs={12} sm={6}>
              <Card className="glass-effect border-white/20 text-center">
                <Statistic
                  title={<span className="text-white/80">关系类型</span>}
                  value={Object.keys(stats.link_types).length}
                  valueStyle={{ color: '#fff', fontSize: '20px' }}
                />
              </Card>
            </Col>
          </Row>
        )}
        
        {/* 控制面板 */}
        <Card className="glass-effect border-white/20 mb-4">
          <div className="flex flex-wrap items-center gap-4">
            <Search
              placeholder="搜索节点..."
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onSearch={handleSearch}
              style={{ width: 300 }}
              enterButton={<SearchOutlined />}
            />
            
            <Select
              value={nodeLimit}
              onChange={(value) => {
                setNodeLimit(value);
                loadKnowledgeGraph(value);
              }}
              style={{ width: 150 }}
              options={[
                { value: 50, label: '50个节点' },
                { value: 100, label: '100个节点' },
                { value: 200, label: '200个节点' },
                { value: 500, label: '500个节点' },
              ]}
            />
            
            <Button
              icon={<ReloadOutlined />}
              onClick={() => loadKnowledgeGraph(nodeLimit)}
              loading={loading}
              className="glass-effect text-white border-white/20"
            >
              刷新
            </Button>
            
            <Button
              icon={<FullscreenOutlined />}
              onClick={handleFullscreen}
              className="glass-effect text-white border-white/20"
            >
              全屏
            </Button>
          </div>
        </Card>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex gap-4">
        {/* 图谱可视化 */}
        <div className="flex-1">
          <Card className="glass-effect border-white/20 h-full" bodyStyle={{ padding: 0 }}>
            <Spin spinning={loading} tip="加载中...">
              <div
                ref={networkRef}
                className="w-full h-full"
                style={{ minHeight: '600px', background: 'rgba(0,0,0,0.3)' }}
              />
            </Spin>
          </Card>
        </div>
        
        {/* 节点详情面板 */}
        <motion.div
          initial={{ width: 0, opacity: 0 }}
          animate={{ 
            width: selectedNode ? 300 : 0, 
            opacity: selectedNode ? 1 : 0 
          }}
          transition={{ duration: 0.3 }}
          className="overflow-hidden"
        >
          {selectedNode && (
            <Card className="glass-effect border-white/20 h-full" style={{ width: 300 }}>
              <div className="text-center mb-4">
                <div
                  className="w-12 h-12 rounded-full mx-auto mb-2 flex items-center justify-center"
                  style={{ backgroundColor: getNodeColor(selectedNode.group) }}
                >
                  <InfoCircleOutlined className="text-white text-xl" />
                </div>
                <Title level={4} className="text-white mb-1">
                  {selectedNode.label}
                </Title>
                <Tag color="blue">{selectedNode.group}</Tag>
              </div>
              
              {selectedNode.description && (
                <div className="mb-4">
                  <Text className="text-white/80 text-sm">
                    {selectedNode.description}
                  </Text>
                </div>
              )}
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <Text className="text-white/70">节点ID:</Text>
                  <Text className="text-white text-xs">{selectedNode.id}</Text>
                </div>
                <div className="flex justify-between">
                  <Text className="text-white/70">类型:</Text>
                  <Text className="text-white">{selectedNode.group}</Text>
                </div>
              </div>
            </Card>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default KnowledgeGraphPage;
