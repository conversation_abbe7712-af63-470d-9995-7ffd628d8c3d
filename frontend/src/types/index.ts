// 聊天相关类型
export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: number;
  sources?: SourceInfo[];
  thinking?: string;
}

export interface ChatResponse {
  response: string;
  sources?: SourceInfo[];
  execution_log?: any[];
  thinking?: string;
}

export interface SourceInfo {
  id: string;
  title: string;
  content: string;
  source: string;
  chunk_id?: string;
}

// 知识图谱相关类型
export interface KnowledgeGraphNode {
  id: string;
  label: string;
  group: string;
  description?: string;
  x?: number;
  y?: number;
}

export interface KnowledgeGraphLink {
  source: string;
  target: string;
  label: string;
  weight?: number;
}

export interface KnowledgeGraphData {
  nodes: KnowledgeGraphNode[];
  links: KnowledgeGraphLink[];
}

// 模板相关类型
export interface Schema {
  id?: string;
  theme: string;
  entity_types: string[];
  relationship_types: string[];
  created_at?: string;
  updated_at?: string;
}

// API请求类型
export interface ChatRequest {
  message: string;
  agent_type?: string;
  session_id?: string;
  debug?: boolean;
  use_deeper_tool?: boolean;
  show_thinking?: boolean;
}

export interface BuildKGRequest {
  theme: string;
  files: File[];
}

// Agent类型
export type AgentType = 
  | 'graph_agent'
  | 'hybrid_agent' 
  | 'naive_rag_agent'
  | 'deep_research_agent'
  | 'fusion_agent';

// 构建状态
export interface BuildStatus {
  has_output: boolean;
  has_cache: boolean;
  files_count: number;
  is_ready: boolean;
}

// 统计信息
export interface KGStats {
  total_nodes: number;
  total_links: number;
  node_types: Record<string, number>;
  link_types: Record<string, number>;
}
