import axios from 'axios';
import type {
  ChatRequest,
  ChatResponse,
  KnowledgeGraphData,
  Schema,
  BuildStatus,
  KGStats,
  SourceInfo
} from '../types';

// API基础配置
const API_BASE_URL = 'http://localhost:8012/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// 聊天API
export const chatAPI = {
  // 发送消息
  async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    const response = await api.post('/chat', request);
    return response.data;
  },

  // 流式发送消息
  async sendMessageStream(
    request: ChatRequest,
    onToken?: (token: string) => void,
    onComplete?: (response: ChatResponse) => void
  ): Promise<void> {
    const response = await fetch(`${API_BASE_URL}/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body');
    }

    const decoder = new TextDecoder();
    let buffer = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              if (data.token) {
                onToken?.(data.token);
              } else if (data.status === 'complete' && data.response) {
                onComplete?.(data.response);
              }
            } catch (e) {
              console.warn('Failed to parse SSE data:', line);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  },

  // 清除聊天历史
  async clearChat(sessionId?: string): Promise<{ success: boolean }> {
    const response = await api.post('/clear', { session_id: sessionId });
    return response.data;
  },
};

// 知识图谱API
export const knowledgeGraphAPI = {
  // 获取全局知识图谱
  async getKnowledgeGraph(limit: number = 100): Promise<KnowledgeGraphData> {
    const response = await api.get(`/knowledge_graph?limit=${limit}`);
    return response.data;
  },

  // 根据消息获取相关知识图谱
  async getKnowledgeGraphFromMessage(
    message: string,
    limit: number = 50
  ): Promise<KnowledgeGraphData> {
    const response = await api.get(
      `/knowledge_graph_from_message?message=${encodeURIComponent(message)}&limit=${limit}`
    );
    return response.data;
  },

  // 获取统计信息
  async getStats(): Promise<KGStats> {
    const response = await api.get('/kg_stats');
    return response.data;
  },
};

// 模板管理API
export const schemaAPI = {
  // 获取所有模板
  async getSchemas(): Promise<Schema[]> {
    const response = await api.get('/schemas');
    return response.data;
  },

  // 创建模板
  async createSchema(schema: Omit<Schema, 'id'>): Promise<Schema> {
    const response = await api.post('/schemas', schema);
    return response.data;
  },

  // 更新模板
  async updateSchema(id: string, schema: Partial<Schema>): Promise<Schema> {
    const response = await api.put(`/schemas/${id}`, schema);
    return response.data;
  },

  // 删除模板
  async deleteSchema(id: string): Promise<{ success: boolean }> {
    const response = await api.delete(`/schemas/${id}`);
    return response.data;
  },
};

// 知识图谱构建API
export const buildAPI = {
  // 构建知识图谱
  async buildKG(theme: string, files: File[]): Promise<{ status: string; message: string }> {
    const formData = new FormData();
    formData.append('theme', theme);
    files.forEach((file) => {
      formData.append('files', file);
    });

    const response = await api.post('/build_kg', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 获取构建状态
  async getStatus(): Promise<BuildStatus> {
    const response = await api.get('/kg_status');
    return response.data;
  },

  // 知识图谱对话
  async chatKG(question: string): Promise<ChatResponse> {
    const response = await api.post('/chat_kg', { question });
    return response.data;
  },
};

// 源内容API
export const sourceAPI = {
  // 获取源内容
  async getSourceContent(sourceId: string): Promise<SourceInfo> {
    const response = await api.get(`/source/${sourceId}`);
    return response.data;
  },

  // 批量获取源内容
  async getSourceContentBatch(sourceIds: string[]): Promise<SourceInfo[]> {
    const response = await api.post('/source/batch', { source_ids: sourceIds });
    return response.data;
  },
};

// 文档解析API
export const parseAPI = {
  // 解析文档
  async parseDocument(file: File): Promise<{ content: string; metadata: any }> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/parse', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};

export default api;
