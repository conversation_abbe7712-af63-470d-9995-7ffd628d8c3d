import axios from 'axios'

// API 基础配置
const API_BASE_URL = 'http://localhost:8000'

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 120000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url)
    return response
  },
  (error) => {
    console.error('API Error:', error.response?.status, error.message)
    return Promise.reject(error)
  }
)

// 类型定义
export interface Message {
  role: 'user' | 'assistant'
  content: string
  message_id?: string
  timestamp?: number
  kg_data?: KnowledgeGraphData
  thinking_content?: string
}

export interface KnowledgeGraphData {
  nodes: Array<{
    id: string
    label: string
    type?: string
    properties?: Record<string, any>
  }>
  links: Array<{
    source: string
    target: string
    label: string
    weight?: number
  }>
}

export interface ChatResponse {
  answer: string
  message_id: string
  execution_trace?: any
  kg_data?: KnowledgeGraphData
  thinking_content?: string
}

export interface AgentType {
  id: string
  name: string
  description: string
}

// API 方法
export const chatAPI = {
  // 发送消息
  async sendMessage(
    message: string,
    agentType: string = 'graph_agent',
    sessionId?: string
  ): Promise<ChatResponse> {
    const response = await api.post('/chat', {
      message,
      agent_type: agentType,
      session_id: sessionId,
    })
    return response.data
  },

  // 流式发送消息
  async sendMessageStream(
    message: string,
    agentType: string = 'graph_agent',
    sessionId?: string,
    onToken?: (token: string) => void
  ): Promise<string> {
    const response = await fetch(`${API_BASE_URL}/chat_stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message,
        agent_type: agentType,
        session_id: sessionId,
      }),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body?.getReader()
    const decoder = new TextDecoder()
    let fullResponse = ''

    if (reader) {
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') {
              return fullResponse
            }
            try {
              const parsed = JSON.parse(data)
              if (parsed.token) {
                fullResponse += parsed.token
                onToken?.(parsed.token)
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    }

    return fullResponse
  },

  // 清除聊天历史
  async clearChat(sessionId?: string): Promise<void> {
    await api.post('/clear_chat', { session_id: sessionId })
  },

  // 发送反馈
  async sendFeedback(
    messageId: string,
    feedback: 'positive' | 'negative',
    comment?: string
  ): Promise<void> {
    await api.post('/feedback', {
      message_id: messageId,
      feedback,
      comment,
    })
  },
}

export const knowledgeGraphAPI = {
  // 获取知识图谱数据
  async getKnowledgeGraph(
    message: string,
    query?: string
  ): Promise<KnowledgeGraphData> {
    const response = await api.post('/kg_from_message', {
      message,
      query,
    })
    return response.data
  },

  // 知识图谱推理
  async getKGReasoning(
    reasoningType: string,
    entityA: string,
    entityB?: string,
    maxDepth: number = 3,
    algorithm: string = 'louvain'
  ): Promise<any> {
    const response = await api.post('/kg_reasoning', {
      reasoning_type: reasoningType,
      entity_a: entityA,
      entity_b: entityB,
      max_depth: maxDepth,
      algorithm,
    })
    return response.data
  },
}

export const sourceAPI = {
  // 获取源内容
  async getSourceContent(sourceIds: string[]): Promise<any> {
    const response = await api.post('/source_content', {
      source_ids: sourceIds,
    })
    return response.data
  },

  // 批量获取源文件信息
  async getSourceFileInfo(sourceIds: string[]): Promise<any> {
    const response = await api.post('/source_file_info_batch', {
      source_ids: sourceIds,
    })
    return response.data
  },
}

export default api
