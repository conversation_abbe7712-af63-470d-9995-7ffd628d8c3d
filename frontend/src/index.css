@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: 'Inter', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

#root {
  min-height: 100vh;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 动画效果 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* 玻璃态效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 渐变文字 */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 自定义表格样式 */
.custom-table .ant-table {
  background: transparent;
}

.custom-table .ant-table-thead > tr > th {
  background: rgba(255, 255, 255, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

.custom-table .ant-table-tbody > tr > td {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.custom-table .ant-table-tbody > tr:hover > td {
  background: rgba(255, 255, 255, 0.05);
}

.custom-table .ant-pagination {
  color: #ffffff;
}

.custom-table .ant-pagination .ant-pagination-item {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.custom-table .ant-pagination .ant-pagination-item a {
  color: #ffffff;
}

.custom-table .ant-pagination .ant-pagination-item-active {
  background: rgba(59, 130, 246, 0.5);
  border-color: #3b82f6;
}

/* 自定义步骤条样式 */
.ant-steps .ant-steps-item-process .ant-steps-item-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
}

.ant-steps .ant-steps-item-finish .ant-steps-item-icon {
  background: #10b981;
  border-color: #10b981;
}

.ant-steps .ant-steps-item-wait .ant-steps-item-icon {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

/* 自定义上传组件样式 */
.ant-upload-drag {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 2px dashed rgba(255, 255, 255, 0.3) !important;
}

.ant-upload-drag:hover {
  border-color: #667eea !important;
  background: rgba(102, 126, 234, 0.1) !important;
}

/* 自定义选择器样式 */
.ant-select-dropdown {
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
}

.ant-select-item {
  color: #ffffff;
}

.ant-select-item-option-selected {
  background: rgba(59, 130, 246, 0.3);
}

.ant-select-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 自定义模态框样式 */
.ant-modal-content {
  background: rgba(15, 23, 42, 0.95);
  backdrop-filter: blur(10px);
}

.ant-modal-header {
  background: transparent;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ant-modal-title {
  color: #ffffff;
}

.ant-modal-close {
  color: #ffffff;
}

/* 自定义输入框样式 */
.ant-input,
.ant-input-affix-wrapper,
.ant-select-selector {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

.ant-input::placeholder,
.ant-select-selection-placeholder {
  color: rgba(255, 255, 255, 0.5) !important;
}

.ant-input:focus,
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  border-color: #667eea !important;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2) !important;
}

/* 自定义折叠面板样式 */
.ant-collapse {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ant-collapse-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.ant-collapse-header {
  color: #ffffff !important;
  background: rgba(255, 255, 255, 0.05);
}

.ant-collapse-content {
  background: transparent;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.ant-collapse-content-box {
  color: #ffffff;
}
