/**
 * vis-util
 * https://github.com/visjs/vis-util
 *
 * utilitie collection for visjs
 *
 * @version 6.0.0
 * @date    2025-07-12T18:02:43.836Z
 *
 * @copyright (c) 2011-2017 Almende B.V, http://almende.com
 * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
 *
 * @license
 * vis.js is dual licensed under both
 *
 *   1. The Apache 2.0 License
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *   and
 *
 *   2. The MIT License
 *      http://opensource.org/licenses/MIT
 *
 * vis.js may be distributed under either license.
 */
var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function e(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var n,r,i,o,a,s,c,u,l,h,f,p,d,v,g,m,y={};function b(){if(r)return n;r=1;var e=function(t){return t&&t.Math===Math&&t};return n=e("object"==typeof globalThis&&globalThis)||e("object"==typeof window&&window)||e("object"==typeof self&&self)||e("object"==typeof t&&t)||e("object"==typeof n&&n)||function(){return this}()||Function("return this")()}function w(){return o?i:(o=1,i=function(t){try{return!!t()}catch(t){return!0}})}function C(){return s?a:(s=1,a=!w()(function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))}function k(){if(u)return c;u=1;var t=C(),e=Function.prototype,n=e.apply,r=e.call;return c="object"==typeof Reflect&&Reflect.apply||(t?r.bind(n):function(){return r.apply(n,arguments)}),c}function E(){if(h)return l;h=1;var t=C(),e=Function.prototype,n=e.call,r=t&&e.bind.bind(n,n);return l=t?r:function(t){return function(){return n.apply(t,arguments)}},l}function O(){if(p)return f;p=1;var t=E(),e=t({}.toString),n=t("".slice);return f=function(t){return n(e(t),8,-1)}}function F(){if(v)return d;v=1;var t=O(),e=E();return d=function(n){if("Function"===t(n))return e(n)}}function _(){if(m)return g;m=1;var t="object"==typeof document&&document.all;return g=void 0===t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(t){return"function"==typeof t}}var T,S,P,D,x={};function A(){return S?T:(S=1,T=!w()(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))}function j(){if(D)return P;D=1;var t=C(),e=Function.prototype.call;return P=t?e.bind(e):function(){return e.apply(e,arguments)},P}var R,N,I,B,M,L,z,W,H,q,U,Y,X,V,G,Q,J,$,Z,K,tt,et,nt,rt,it,ot,at,st,ct,ut,lt,ht,ft,pt,dt,vt,gt,mt={};function yt(){if(R)return mt;R=1;var t={}.propertyIsEnumerable,e=Object.getOwnPropertyDescriptor,n=e&&!t.call({1:2},1);return mt.f=n?function(t){var n=e(this,t);return!!n&&n.enumerable}:t,mt}function bt(){return I?N:(I=1,N=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}})}function wt(){if(M)return B;M=1;var t=E(),e=w(),n=O(),r=Object,i=t("".split);return B=e(function(){return!r("z").propertyIsEnumerable(0)})?function(t){return"String"===n(t)?i(t,""):r(t)}:r}function Ct(){return z?L:(z=1,L=function(t){return null==t})}function kt(){if(H)return W;H=1;var t=Ct(),e=TypeError;return W=function(n){if(t(n))throw new e("Can't call method on "+n);return n}}function Et(){if(U)return q;U=1;var t=wt(),e=kt();return q=function(n){return t(e(n))}}function Ot(){if(X)return Y;X=1;var t=_();return Y=function(e){return"object"==typeof e?null!==e:t(e)}}function Ft(){return G?V:(G=1,V={})}function _t(){if(J)return Q;J=1;var t=Ft(),e=b(),n=_(),r=function(t){return n(t)?t:void 0};return Q=function(n,i){return arguments.length<2?r(t[n])||r(e[n]):t[n]&&t[n][i]||e[n]&&e[n][i]},Q}function Tt(){return Z?$:(Z=1,$=E()({}.isPrototypeOf))}function St(){if(tt)return K;tt=1;var t=b().navigator,e=t&&t.userAgent;return K=e?String(e):""}function Pt(){if(nt)return et;nt=1;var t,e,n=b(),r=St(),i=n.process,o=n.Deno,a=i&&i.versions||o&&o.version,s=a&&a.v8;return s&&(e=(t=s.split("."))[0]>0&&t[0]<4?1:+(t[0]+t[1])),!e&&r&&(!(t=r.match(/Edge\/(\d+)/))||t[1]>=74)&&(t=r.match(/Chrome\/(\d+)/))&&(e=+t[1]),et=e}function Dt(){if(it)return rt;it=1;var t=Pt(),e=w(),n=b().String;return rt=!!Object.getOwnPropertySymbols&&!e(function(){var e=Symbol("symbol detection");return!n(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&t&&t<41}),rt}function xt(){return at?ot:(at=1,ot=Dt()&&!Symbol.sham&&"symbol"==typeof Symbol.iterator)}function At(){if(ct)return st;ct=1;var t=_t(),e=_(),n=Tt(),r=Object;return st=xt()?function(t){return"symbol"==typeof t}:function(i){var o=t("Symbol");return e(o)&&n(o.prototype,r(i))}}function jt(){if(lt)return ut;lt=1;var t=String;return ut=function(e){try{return t(e)}catch(t){return"Object"}}}function Rt(){if(ft)return ht;ft=1;var t=_(),e=jt(),n=TypeError;return ht=function(r){if(t(r))return r;throw new n(e(r)+" is not a function")}}function Nt(){if(dt)return pt;dt=1;var t=Rt(),e=Ct();return pt=function(n,r){var i=n[r];return e(i)?void 0:t(i)}}function It(){if(gt)return vt;gt=1;var t=j(),e=_(),n=Ot(),r=TypeError;return vt=function(i,o){var a,s;if("string"===o&&e(a=i.toString)&&!n(s=t(a,i)))return s;if(e(a=i.valueOf)&&!n(s=t(a,i)))return s;if("string"!==o&&e(a=i.toString)&&!n(s=t(a,i)))return s;throw new r("Can't convert object to primitive value")}}var Bt,Mt,Lt,zt,Wt,Ht,qt,Ut,Yt,Xt,Vt,Gt,Qt,Jt,$t,Zt,Kt,te,ee,ne,re,ie,oe,ae,se,ce,ue,le,he={exports:{}};function fe(){return Mt?Bt:(Mt=1,Bt=!0)}function pe(){if(zt)return Lt;zt=1;var t=b(),e=Object.defineProperty;return Lt=function(n,r){try{e(t,n,{value:r,configurable:!0,writable:!0})}catch(e){t[n]=r}return r}}function de(){if(Wt)return he.exports;Wt=1;var t=fe(),e=b(),n=pe(),r="__core-js_shared__",i=he.exports=e[r]||n(r,{});return(i.versions||(i.versions=[])).push({version:"3.44.0",mode:t?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.44.0/LICENSE",source:"https://github.com/zloirock/core-js"}),he.exports}function ve(){if(qt)return Ht;qt=1;var t=de();return Ht=function(e,n){return t[e]||(t[e]=n||{})}}function ge(){if(Yt)return Ut;Yt=1;var t=kt(),e=Object;return Ut=function(n){return e(t(n))}}function me(){if(Vt)return Xt;Vt=1;var t=E(),e=ge(),n=t({}.hasOwnProperty);return Xt=Object.hasOwn||function(t,r){return n(e(t),r)}}function ye(){if(Qt)return Gt;Qt=1;var t=E(),e=0,n=Math.random(),r=t(1.1.toString);return Gt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+r(++e+n,36)}}function be(){if($t)return Jt;$t=1;var t=b(),e=ve(),n=me(),r=ye(),i=Dt(),o=xt(),a=t.Symbol,s=e("wks"),c=o?a.for||a:a&&a.withoutSetter||r;return Jt=function(t){return n(s,t)||(s[t]=i&&n(a,t)?a[t]:c("Symbol."+t)),s[t]}}function we(){if(Kt)return Zt;Kt=1;var t=j(),e=Ot(),n=At(),r=Nt(),i=It(),o=TypeError,a=be()("toPrimitive");return Zt=function(s,c){if(!e(s)||n(s))return s;var u,l=r(s,a);if(l){if(void 0===c&&(c="default"),u=t(l,s,c),!e(u)||n(u))return u;throw new o("Can't convert object to primitive value")}return void 0===c&&(c="number"),i(s,c)}}function Ce(){if(ee)return te;ee=1;var t=we(),e=At();return te=function(n){var r=t(n,"string");return e(r)?r:r+""}}function ke(){if(re)return ne;re=1;var t=b(),e=Ot(),n=t.document,r=e(n)&&e(n.createElement);return ne=function(t){return r?n.createElement(t):{}}}function Ee(){if(oe)return ie;oe=1;var t=A(),e=w(),n=ke();return ie=!t&&!e(function(){return 7!==Object.defineProperty(n("div"),"a",{get:function(){return 7}}).a})}function Oe(){if(ae)return x;ae=1;var t=A(),e=j(),n=yt(),r=bt(),i=Et(),o=Ce(),a=me(),s=Ee(),c=Object.getOwnPropertyDescriptor;return x.f=t?c:function(t,u){if(t=i(t),u=o(u),s)try{return c(t,u)}catch(t){}if(a(t,u))return r(!e(n.f,t,u),t[u])},x}function Fe(){if(ce)return se;ce=1;var t=w(),e=_(),n=/#|\.prototype\./,r=function(n,r){var c=o[i(n)];return c===s||c!==a&&(e(r)?t(r):!!r)},i=r.normalize=function(t){return String(t).replace(n,".").toLowerCase()},o=r.data={},a=r.NATIVE="N",s=r.POLYFILL="P";return se=r}function _e(){if(le)return ue;le=1;var t=F(),e=Rt(),n=C(),r=t(t.bind);return ue=function(t,i){return e(t),void 0===i?t:n?r(t,i):function(){return t.apply(i,arguments)}},ue}var Te,Se,Pe,De,xe,Ae,je,Re,Ne,Ie,Be,Me,Le,ze,We,He,qe,Ue,Ye,Xe,Ve,Ge,Qe,Je,$e,Ze,Ke,tn,en,nn,rn,on,an,sn,cn,un,ln,hn,fn={};function pn(){return Se?Te:(Se=1,Te=A()&&w()(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype}))}function dn(){if(De)return Pe;De=1;var t=Ot(),e=String,n=TypeError;return Pe=function(r){if(t(r))return r;throw new n(e(r)+" is not an object")}}function vn(){if(xe)return fn;xe=1;var t=A(),e=Ee(),n=pn(),r=dn(),i=Ce(),o=TypeError,a=Object.defineProperty,s=Object.getOwnPropertyDescriptor,c="enumerable",u="configurable",l="writable";return fn.f=t?n?function(t,e,n){if(r(t),e=i(e),r(n),"function"==typeof t&&"prototype"===e&&"value"in n&&l in n&&!n[l]){var o=s(t,e);o&&o[l]&&(t[e]=n.value,n={configurable:u in n?n[u]:o[u],enumerable:c in n?n[c]:o[c],writable:!1})}return a(t,e,n)}:a:function(t,n,s){if(r(t),n=i(n),r(s),e)try{return a(t,n,s)}catch(t){}if("get"in s||"set"in s)throw new o("Accessors not supported");return"value"in s&&(t[n]=s.value),t},fn}function gn(){if(je)return Ae;je=1;var t=A(),e=vn(),n=bt();return Ae=t?function(t,r,i){return e.f(t,r,n(1,i))}:function(t,e,n){return t[e]=n,t}}function mn(){if(Ne)return Re;Ne=1;var t=b(),e=k(),n=F(),r=_(),i=Oe().f,o=Fe(),a=Ft(),s=_e(),c=gn(),u=me(),l=function(t){var n=function(r,i,o){if(this instanceof n){switch(arguments.length){case 0:return new t;case 1:return new t(r);case 2:return new t(r,i)}return new t(r,i,o)}return e(t,this,arguments)};return n.prototype=t.prototype,n};return Re=function(e,h){var f,p,d,v,g,m,y,b,w,C=e.target,k=e.global,E=e.stat,O=e.proto,F=k?t:E?t[C]:t[C]&&t[C].prototype,_=k?a:a[C]||c(a,C,{})[C],T=_.prototype;for(v in h)p=!(f=o(k?v:C+(E?".":"#")+v,e.forced))&&F&&u(F,v),m=_[v],p&&(y=e.dontCallGetSet?(w=i(F,v))&&w.value:F[v]),g=p&&y?y:h[v],(f||O||typeof m!=typeof g)&&(b=e.bind&&p?s(g,t):e.wrap&&p?l(g):O&&r(g)?n(g):g,(e.sham||g&&g.sham||m&&m.sham)&&c(b,"sham",!0),c(_,v,b),O&&(u(a,d=C+"Prototype")||c(a,d,{}),c(a[d],v,g),e.real&&T&&(f||!T[v])&&c(T,v,g)))}}function yn(){if(Be)return Ie;Be=1;var t=O();return Ie=Array.isArray||function(e){return"Array"===t(e)}}function bn(){if(Le)return Me;Le=1;var t=Math.ceil,e=Math.floor;return Me=Math.trunc||function(n){var r=+n;return(r>0?e:t)(r)}}function wn(){if(We)return ze;We=1;var t=bn();return ze=function(e){var n=+e;return n!=n||0===n?0:t(n)}}function Cn(){if(qe)return He;qe=1;var t=wn(),e=Math.min;return He=function(n){var r=t(n);return r>0?e(r,9007199254740991):0}}function kn(){if(Ye)return Ue;Ye=1;var t=Cn();return Ue=function(e){return t(e.length)}}function En(){if(Ve)return Xe;Ve=1;var t=TypeError;return Xe=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e}}function On(){if(Qe)return Ge;Qe=1;var t=A(),e=vn(),n=bt();return Ge=function(r,i,o){t?e.f(r,i,n(0,o)):r[i]=o}}function Fn(){if($e)return Je;$e=1;var t={};return t[be()("toStringTag")]="z",Je="[object z]"===String(t)}function _n(){if(Ke)return Ze;Ke=1;var t=Fn(),e=_(),n=O(),r=be()("toStringTag"),i=Object,o="Arguments"===n(function(){return arguments}());return Ze=t?n:function(t){var a,s,c;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(s=function(t,e){try{return t[e]}catch(t){}}(a=i(t),r))?s:o?n(a):"Object"===(c=n(a))&&e(a.callee)?"Arguments":c}}function Tn(){if(en)return tn;en=1;var t=E(),e=_(),n=de(),r=t(Function.toString);return e(n.inspectSource)||(n.inspectSource=function(t){return r(t)}),tn=n.inspectSource}function Sn(){if(rn)return nn;rn=1;var t=E(),e=w(),n=_(),r=_n(),i=_t(),o=Tn(),a=function(){},s=i("Reflect","construct"),c=/^\s*(?:class|function)\b/,u=t(c.exec),l=!c.test(a),h=function(t){if(!n(t))return!1;try{return s(a,[],t),!0}catch(t){return!1}},f=function(t){if(!n(t))return!1;switch(r(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return l||!!u(c,o(t))}catch(t){return!0}};return f.sham=!0,nn=!s||e(function(){var t;return h(h.call)||!h(Object)||!h(function(){t=!0})||t})?f:h}function Pn(){if(an)return on;an=1;var t=yn(),e=Sn(),n=Ot(),r=be()("species"),i=Array;return on=function(o){var a;return t(o)&&(a=o.constructor,(e(a)&&(a===i||t(a.prototype))||n(a)&&null===(a=a[r]))&&(a=void 0)),void 0===a?i:a}}function Dn(){if(cn)return sn;cn=1;var t=Pn();return sn=function(e,n){return new(t(e))(0===n?0:n)}}function xn(){if(ln)return un;ln=1;var t=w(),e=be(),n=Pt(),r=e("species");return un=function(e){return n>=51||!t(function(){var t=[];return(t.constructor={})[r]=function(){return{foo:1}},1!==t[e](Boolean).foo})}}function An(){if(hn)return y;hn=1;var t=mn(),e=w(),n=yn(),r=Ot(),i=ge(),o=kn(),a=En(),s=On(),c=Dn(),u=xn(),l=be(),h=Pt(),f=l("isConcatSpreadable"),p=h>=51||!e(function(){var t=[];return t[f]=!1,t.concat()[0]!==t}),d=function(t){if(!r(t))return!1;var e=t[f];return void 0!==e?!!e:n(t)};return t({target:"Array",proto:!0,arity:1,forced:!p||!u("concat")},{concat:function(t){var e,n,r,u,l,h=i(this),f=c(h,0),p=0;for(e=-1,r=arguments.length;e<r;e++)if(d(l=-1===e?h:arguments[e]))for(u=o(l),a(p+u),n=0;n<u;n++,p++)n in l&&s(f,p,l[n]);else a(p+1),s(f,p++,l);return f.length=p,f}}),y}var jn,Rn,Nn={},In={};function Bn(){if(Rn)return jn;Rn=1;var t=_n(),e=String;return jn=function(n){if("Symbol"===t(n))throw new TypeError("Cannot convert a Symbol value to a string");return e(n)}}var Mn,Ln,zn,Wn,Hn,qn,Un,Yn,Xn,Vn,Gn,Qn,Jn,$n,Zn,Kn,tr,er,nr,rr={};function ir(){if(Ln)return Mn;Ln=1;var t=wn(),e=Math.max,n=Math.min;return Mn=function(r,i){var o=t(r);return o<0?e(o+i,0):n(o,i)}}function or(){if(Wn)return zn;Wn=1;var t=Et(),e=ir(),n=kn(),r=function(r){return function(i,o,a){var s=t(i),c=n(s);if(0===c)return!r&&-1;var u,l=e(a,c);if(r&&o!=o){for(;c>l;)if((u=s[l++])!=u)return!0}else for(;c>l;l++)if((r||l in s)&&s[l]===o)return r||l||0;return!r&&-1}};return zn={includes:r(!0),indexOf:r(!1)}}function ar(){return qn?Hn:(qn=1,Hn={})}function sr(){if(Yn)return Un;Yn=1;var t=E(),e=me(),n=Et(),r=or().indexOf,i=ar(),o=t([].push);return Un=function(t,a){var s,c=n(t),u=0,l=[];for(s in c)!e(i,s)&&e(c,s)&&o(l,s);for(;a.length>u;)e(c,s=a[u++])&&(~r(l,s)||o(l,s));return l}}function cr(){return Vn?Xn:(Vn=1,Xn=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"])}function ur(){if(Qn)return Gn;Qn=1;var t=sr(),e=cr();return Gn=Object.keys||function(n){return t(n,e)}}function lr(){if(Jn)return rr;Jn=1;var t=A(),e=pn(),n=vn(),r=dn(),i=Et(),o=ur();return rr.f=t&&!e?Object.defineProperties:function(t,e){r(t);for(var a,s=i(e),c=o(e),u=c.length,l=0;u>l;)n.f(t,a=c[l++],s[a]);return t},rr}function hr(){return Zn?$n:(Zn=1,$n=_t()("document","documentElement"))}function fr(){if(tr)return Kn;tr=1;var t=ve(),e=ye(),n=t("keys");return Kn=function(t){return n[t]||(n[t]=e(t))}}function pr(){if(nr)return er;nr=1;var t,e=dn(),n=lr(),r=cr(),i=ar(),o=hr(),a=ke(),s="prototype",c="script",u=fr()("IE_PROTO"),l=function(){},h=function(t){return"<"+c+">"+t+"</"+c+">"},f=function(t){t.write(h("")),t.close();var e=t.parentWindow.Object;return t=null,e},p=function(){try{t=new ActiveXObject("htmlfile")}catch(t){}var e,n,i;p="undefined"!=typeof document?document.domain&&t?f(t):(n=a("iframe"),i="java"+c+":",n.style.display="none",o.appendChild(n),n.src=String(i),(e=n.contentWindow.document).open(),e.write(h("document.F=Object")),e.close(),e.F):f(t);for(var u=r.length;u--;)delete p[s][r[u]];return p()};return i[u]=!0,er=Object.create||function(t,r){var i;return null!==t?(l[s]=e(t),i=new l,l[s]=null,i[u]=t):i=p(),void 0===r?i:n.f(i,r)}}var dr,vr={};function gr(){if(dr)return vr;dr=1;var t=sr(),e=cr().concat("length","prototype");return vr.f=Object.getOwnPropertyNames||function(n){return t(n,e)},vr}var mr,yr,br,wr={};function Cr(){return yr?mr:(yr=1,mr=E()([].slice))}function kr(){if(br)return wr;br=1;var t=O(),e=Et(),n=gr().f,r=Cr(),i="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[];return wr.f=function(o){return i&&"Window"===t(o)?function(t){try{return n(t)}catch(t){return r(i)}}(o):n(e(o))},wr}var Er,Or,Fr,_r,Tr,Sr={};function Pr(){return Er||(Er=1,Sr.f=Object.getOwnPropertySymbols),Sr}function Dr(){if(Fr)return Or;Fr=1;var t=gn();return Or=function(e,n,r,i){return i&&i.enumerable?e[n]=r:t(e,n,r),e}}function xr(){if(Tr)return _r;Tr=1;var t=vn();return _r=function(e,n,r){return t.f(e,n,r)}}var Ar,jr,Rr,Nr,Ir,Br,Mr,Lr,zr,Wr,Hr,qr,Ur,Yr,Xr,Vr,Gr={};function Qr(){if(Ar)return Gr;Ar=1;var t=be();return Gr.f=t,Gr}function Jr(){if(Rr)return jr;Rr=1;var t=Ft(),e=me(),n=Qr(),r=vn().f;return jr=function(i){var o=t.Symbol||(t.Symbol={});e(o,i)||r(o,i,{value:n.f(i)})}}function $r(){if(Ir)return Nr;Ir=1;var t=j(),e=_t(),n=be(),r=Dr();return Nr=function(){var i=e("Symbol"),o=i&&i.prototype,a=o&&o.valueOf,s=n("toPrimitive");o&&!o[s]&&r(o,s,function(e){return t(a,this)},{arity:1})}}function Zr(){if(Mr)return Br;Mr=1;var t=Fn(),e=_n();return Br=t?{}.toString:function(){return"[object "+e(this)+"]"}}function Kr(){if(zr)return Lr;zr=1;var t=Fn(),e=vn().f,n=gn(),r=me(),i=Zr(),o=be()("toStringTag");return Lr=function(a,s,c,u){var l=c?a:a&&a.prototype;l&&(r(l,o)||e(l,o,{configurable:!0,value:s}),u&&!t&&n(l,"toString",i))}}function ti(){if(Hr)return Wr;Hr=1;var t=b(),e=_(),n=t.WeakMap;return Wr=e(n)&&/native code/.test(String(n))}function ei(){if(Ur)return qr;Ur=1;var t,e,n,r=ti(),i=b(),o=Ot(),a=gn(),s=me(),c=de(),u=fr(),l=ar(),h="Object already initialized",f=i.TypeError,p=i.WeakMap;if(r||c.state){var d=c.state||(c.state=new p);d.get=d.get,d.has=d.has,d.set=d.set,t=function(t,e){if(d.has(t))throw new f(h);return e.facade=t,d.set(t,e),e},e=function(t){return d.get(t)||{}},n=function(t){return d.has(t)}}else{var v=u("state");l[v]=!0,t=function(t,e){if(s(t,v))throw new f(h);return e.facade=t,a(t,v,e),e},e=function(t){return s(t,v)?t[v]:{}},n=function(t){return s(t,v)}}return qr={set:t,get:e,has:n,enforce:function(r){return n(r)?e(r):t(r,{})},getterFor:function(t){return function(n){var r;if(!o(n)||(r=e(n)).type!==t)throw new f("Incompatible receiver, "+t+" required");return r}}}}function ni(){if(Xr)return Yr;Xr=1;var t=_e(),e=E(),n=wt(),r=ge(),i=kn(),o=Dn(),a=e([].push),s=function(e){var s=1===e,c=2===e,u=3===e,l=4===e,h=6===e,f=7===e,p=5===e||h;return function(d,v,g,m){for(var y,b,w=r(d),C=n(w),k=i(C),E=t(v,g),O=0,F=m||o,_=s?F(d,k):c||f?F(d,0):void 0;k>O;O++)if((p||O in C)&&(b=E(y=C[O],O,w),e))if(s)_[O]=b;else if(b)switch(e){case 3:return!0;case 5:return y;case 6:return O;case 2:a(_,y)}else switch(e){case 4:return!1;case 7:a(_,y)}return h?-1:u||l?l:_}};return Yr={forEach:s(0),map:s(1),filter:s(2),some:s(3),every:s(4),find:s(5),findIndex:s(6),filterReject:s(7)}}var ri,ii,oi,ai={};function si(){return ii?ri:(ii=1,ri=Dt()&&!!Symbol.for&&!!Symbol.keyFor)}var ci,ui={};var li,hi,fi,pi={};function di(){if(hi)return li;hi=1;var t=E(),e=yn(),n=_(),r=O(),i=Bn(),o=t([].push);return li=function(t){if(n(t))return t;if(e(t)){for(var a=t.length,s=[],c=0;c<a;c++){var u=t[c];"string"==typeof u?o(s,u):"number"!=typeof u&&"Number"!==r(u)&&"String"!==r(u)||o(s,i(u))}var l=s.length,h=!0;return function(t,n){if(h)return h=!1,n;if(e(this))return n;for(var r=0;r<l;r++)if(s[r]===t)return n}}},li}function vi(){if(fi)return pi;fi=1;var t=mn(),e=_t(),n=k(),r=j(),i=E(),o=w(),a=_(),s=At(),c=Cr(),u=di(),l=Dt(),h=String,f=e("JSON","stringify"),p=i(/./.exec),d=i("".charAt),v=i("".charCodeAt),g=i("".replace),m=i(1.1.toString),y=/[\uD800-\uDFFF]/g,b=/^[\uD800-\uDBFF]$/,C=/^[\uDC00-\uDFFF]$/,O=!l||o(function(){var t=e("Symbol")("stringify detection");return"[null]"!==f([t])||"{}"!==f({a:t})||"{}"!==f(Object(t))}),F=o(function(){return'"\\udf06\\ud834"'!==f("\udf06\ud834")||'"\\udead"'!==f("\udead")}),T=function(t,e){var i=c(arguments),o=u(e);if(a(o)||void 0!==t&&!s(t))return i[1]=function(t,e){if(a(o)&&(e=r(o,this,h(t),e)),!s(e))return e},n(f,null,i)},S=function(t,e,n){var r=d(n,e-1),i=d(n,e+1);return p(b,t)&&!p(C,i)||p(C,t)&&!p(b,r)?"\\u"+m(v(t,0),16):t};return f&&t({target:"JSON",stat:!0,arity:3,forced:O||F},{stringify:function(t,e,r){var i=c(arguments),o=n(O?T:f,null,i);return F&&"string"==typeof o?g(o,y,S):o}}),pi}var gi,mi,yi={};function bi(){return mi||(mi=1,function(){if(Vr)return In;Vr=1;var t=mn(),e=b(),n=j(),r=E(),i=fe(),o=A(),a=Dt(),s=w(),c=me(),u=Tt(),l=dn(),h=Et(),f=Ce(),p=Bn(),d=bt(),v=pr(),g=ur(),m=gr(),y=kr(),C=Pr(),k=Oe(),O=vn(),F=lr(),_=yt(),T=Dr(),S=xr(),P=ve(),D=fr(),x=ar(),R=ye(),N=be(),I=Qr(),B=Jr(),M=$r(),L=Kr(),z=ei(),W=ni().forEach,H=D("hidden"),q="Symbol",U="prototype",Y=z.set,X=z.getterFor(q),V=Object[U],G=e.Symbol,Q=G&&G[U],J=e.RangeError,$=e.TypeError,Z=e.QObject,K=k.f,tt=O.f,et=y.f,nt=_.f,rt=r([].push),it=P("symbols"),ot=P("op-symbols"),at=P("wks"),st=!Z||!Z[U]||!Z[U].findChild,ct=function(t,e,n){var r=K(V,e);r&&delete V[e],tt(t,e,n),r&&t!==V&&tt(V,e,r)},ut=o&&s(function(){return 7!==v(tt({},"a",{get:function(){return tt(this,"a",{value:7}).a}})).a})?ct:tt,lt=function(t,e){var n=it[t]=v(Q);return Y(n,{type:q,tag:t,description:e}),o||(n.description=e),n},ht=function(t,e,n){t===V&&ht(ot,e,n),l(t);var r=f(e);return l(n),c(it,r)?(n.enumerable?(c(t,H)&&t[H][r]&&(t[H][r]=!1),n=v(n,{enumerable:d(0,!1)})):(c(t,H)||tt(t,H,d(1,v(null))),t[H][r]=!0),ut(t,r,n)):tt(t,r,n)},ft=function(t,e){l(t);var r=h(e),i=g(r).concat(gt(r));return W(i,function(e){o&&!n(pt,r,e)||ht(t,e,r[e])}),t},pt=function(t){var e=f(t),r=n(nt,this,e);return!(this===V&&c(it,e)&&!c(ot,e))&&(!(r||!c(this,e)||!c(it,e)||c(this,H)&&this[H][e])||r)},dt=function(t,e){var n=h(t),r=f(e);if(n!==V||!c(it,r)||c(ot,r)){var i=K(n,r);return!i||!c(it,r)||c(n,H)&&n[H][r]||(i.enumerable=!0),i}},vt=function(t){var e=et(h(t)),n=[];return W(e,function(t){c(it,t)||c(x,t)||rt(n,t)}),n},gt=function(t){var e=t===V,n=et(e?ot:h(t)),r=[];return W(n,function(t){!c(it,t)||e&&!c(V,t)||rt(r,it[t])}),r};a||(G=function(){if(u(Q,this))throw new $("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?p(arguments[0]):void 0,r=R(t),i=function(t){var o=void 0===this?e:this;o===V&&n(i,ot,t),c(o,H)&&c(o[H],r)&&(o[H][r]=!1);var a=d(1,t);try{ut(o,r,a)}catch(t){if(!(t instanceof J))throw t;ct(o,r,a)}};return o&&st&&ut(V,r,{configurable:!0,set:i}),lt(r,t)},T(Q=G[U],"toString",function(){return X(this).tag}),T(G,"withoutSetter",function(t){return lt(R(t),t)}),_.f=pt,O.f=ht,F.f=ft,k.f=dt,m.f=y.f=vt,C.f=gt,I.f=function(t){return lt(N(t),t)},o&&(S(Q,"description",{configurable:!0,get:function(){return X(this).description}}),i||T(V,"propertyIsEnumerable",pt,{unsafe:!0}))),t({global:!0,constructor:!0,wrap:!0,forced:!a,sham:!a},{Symbol:G}),W(g(at),function(t){B(t)}),t({target:q,stat:!0,forced:!a},{useSetter:function(){st=!0},useSimple:function(){st=!1}}),t({target:"Object",stat:!0,forced:!a,sham:!o},{create:function(t,e){return void 0===e?v(t):ft(v(t),e)},defineProperty:ht,defineProperties:ft,getOwnPropertyDescriptor:dt}),t({target:"Object",stat:!0,forced:!a},{getOwnPropertyNames:vt}),M(),L(G,q),x[H]=!0}(),function(){if(oi)return ai;oi=1;var t=mn(),e=_t(),n=me(),r=Bn(),i=ve(),o=si(),a=i("string-to-symbol-registry"),s=i("symbol-to-string-registry");t({target:"Symbol",stat:!0,forced:!o},{for:function(t){var i=r(t);if(n(a,i))return a[i];var o=e("Symbol")(i);return a[i]=o,s[o]=i,o}})}(),function(){if(ci)return ui;ci=1;var t=mn(),e=me(),n=At(),r=jt(),i=ve(),o=si(),a=i("symbol-to-string-registry");t({target:"Symbol",stat:!0,forced:!o},{keyFor:function(t){if(!n(t))throw new TypeError(r(t)+" is not a symbol");if(e(a,t))return a[t]}})}(),vi(),function(){if(gi)return yi;gi=1;var t=mn(),e=Dt(),n=w(),r=Pr(),i=ge();t({target:"Object",stat:!0,forced:!e||n(function(){r.f(1)})},{getOwnPropertySymbols:function(t){var e=r.f;return e?e(i(t)):[]}})}()),Nn}var wi;var Ci;var ki;var Ei;var Oi;var Fi;var _i;var Ti;var Si;var Pi;var Di;var xi;var Ai,ji={};var Ri,Ni={};var Ii;var Bi,Mi,Li,zi={};function Wi(){return Li?Mi:(Li=1,An(),bi(),wi||(wi=1,Jr()("asyncDispose")),Ci||(Ci=1,Jr()("asyncIterator")),ki||(ki=1,Jr()("dispose")),Ei||(Ei=1,Jr()("hasInstance")),Oi||(Oi=1,Jr()("isConcatSpreadable")),Fi||(Fi=1,Jr()("iterator")),_i||(_i=1,Jr()("match")),Ti||(Ti=1,Jr()("matchAll")),Si||(Si=1,Jr()("replace")),Pi||(Pi=1,Jr()("search")),Di||(Di=1,Jr()("species")),xi||(xi=1,Jr()("split")),function(){if(Ai)return ji;Ai=1;var t=Jr(),e=$r();t("toPrimitive"),e()}(),function(){if(Ri)return Ni;Ri=1;var t=_t(),e=Jr(),n=Kr();e("toStringTag"),n(t("Symbol"),"Symbol")}(),Ii||(Ii=1,Jr()("unscopables")),function(){if(Bi)return zi;Bi=1;var t=b();Kr()(t.JSON,"JSON",!0)}(),Mi=Ft().Symbol)}var Hi,qi,Ui,Yi,Xi,Vi,Gi,Qi,Ji,$i,Zi,Ki,to,eo,no,ro,io,oo,ao,so,co,uo,lo,ho,fo,po,vo,go,mo,yo,bo,wo,Co,ko,Eo,Oo={};function Fo(){return qi?Hi:(qi=1,Hi=function(){})}function _o(){return Yi?Ui:(Yi=1,Ui={})}function To(){if(Vi)return Xi;Vi=1;var t=A(),e=me(),n=Function.prototype,r=t&&Object.getOwnPropertyDescriptor,i=e(n,"name"),o=i&&"something"===function(){}.name,a=i&&(!t||t&&r(n,"name").configurable);return Xi={EXISTS:i,PROPER:o,CONFIGURABLE:a}}function So(){return Qi?Gi:(Qi=1,Gi=!w()(function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))}function Po(){if($i)return Ji;$i=1;var t=me(),e=_(),n=ge(),r=fr(),i=So(),o=r("IE_PROTO"),a=Object,s=a.prototype;return Ji=i?a.getPrototypeOf:function(r){var i=n(r);if(t(i,o))return i[o];var c=i.constructor;return e(c)&&i instanceof c?c.prototype:i instanceof a?s:null}}function Do(){if(Ki)return Zi;Ki=1;var t,e,n,r=w(),i=_(),o=Ot(),a=pr(),s=Po(),c=Dr(),u=be(),l=fe(),h=u("iterator"),f=!1;return[].keys&&("next"in(n=[].keys())?(e=s(s(n)))!==Object.prototype&&(t=e):f=!0),!o(t)||r(function(){var e={};return t[h].call(e)!==e})?t={}:l&&(t=a(t)),i(t[h])||c(t,h,function(){return this}),Zi={IteratorPrototype:t,BUGGY_SAFARI_ITERATORS:f}}function xo(){if(eo)return to;eo=1;var t=Do().IteratorPrototype,e=pr(),n=bt(),r=Kr(),i=_o(),o=function(){return this};return to=function(a,s,c,u){var l=s+" Iterator";return a.prototype=e(t,{next:n(+!u,c)}),r(a,l,!1,!0),i[l]=o,a}}function Ao(){if(ro)return no;ro=1;var t=E(),e=Rt();return no=function(n,r,i){try{return t(e(Object.getOwnPropertyDescriptor(n,r)[i]))}catch(t){}}}function jo(){if(oo)return io;oo=1;var t=Ot();return io=function(e){return t(e)||null===e}}function Ro(){if(so)return ao;so=1;var t=jo(),e=String,n=TypeError;return ao=function(r){if(t(r))return r;throw new n("Can't set "+e(r)+" as a prototype")}}function No(){if(uo)return co;uo=1;var t=Ao(),e=Ot(),n=kt(),r=Ro();return co=Object.setPrototypeOf||("__proto__"in{}?function(){var i,o=!1,a={};try{(i=t(Object.prototype,"__proto__","set"))(a,[]),o=a instanceof Array}catch(t){}return function(t,a){return n(t),r(a),e(t)?(o?i(t,a):t.__proto__=a,t):t}}():void 0)}function Io(){if(ho)return lo;ho=1;var t=mn(),e=j(),n=fe(),r=To(),i=_(),o=xo(),a=Po(),s=No(),c=Kr(),u=gn(),l=Dr(),h=be(),f=_o(),p=Do(),d=r.PROPER,v=r.CONFIGURABLE,g=p.IteratorPrototype,m=p.BUGGY_SAFARI_ITERATORS,y=h("iterator"),b="keys",w="values",C="entries",k=function(){return this};return lo=function(r,h,p,E,O,F,_){o(p,h,E);var T,S,P,D=function(t){if(t===O&&N)return N;if(!m&&t&&t in j)return j[t];switch(t){case b:case w:case C:return function(){return new p(this,t)}}return function(){return new p(this)}},x=h+" Iterator",A=!1,j=r.prototype,R=j[y]||j["@@iterator"]||O&&j[O],N=!m&&R||D(O),I="Array"===h&&j.entries||R;if(I&&(T=a(I.call(new r)))!==Object.prototype&&T.next&&(n||a(T)===g||(s?s(T,g):i(T[y])||l(T,y,k)),c(T,x,!0,!0),n&&(f[x]=k)),d&&O===w&&R&&R.name!==w&&(!n&&v?u(j,"name",w):(A=!0,N=function(){return e(R,this)})),O)if(S={values:D(w),keys:F?N:D(b),entries:D(C)},_)for(P in S)(m||A||!(P in j))&&l(j,P,S[P]);else t({target:h,proto:!0,forced:m||A},S);return n&&!_||j[y]===N||l(j,y,N,{name:O}),f[h]=N,S}}function Bo(){return po?fo:(po=1,fo=function(t,e){return{value:t,done:e}})}function Mo(){return yo?mo:(yo=1,mo={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0})}function Lo(){if(bo)return Oo;bo=1,function(){if(go)return vo;go=1;var t=Et(),e=Fo(),n=_o(),r=ei(),i=vn().f,o=Io(),a=Bo(),s=fe(),c=A(),u="Array Iterator",l=r.set,h=r.getterFor(u);vo=o(Array,"Array",function(e,n){l(this,{type:u,target:t(e),index:0,kind:n})},function(){var t=h(this),e=t.target,n=t.index++;if(!e||n>=e.length)return t.target=null,a(void 0,!0);switch(t.kind){case"keys":return a(n,!1);case"values":return a(e[n],!1)}return a([n,e[n]],!1)},"values");var f=n.Arguments=n.Array;if(e("keys"),e("values"),e("entries"),!s&&c&&"values"!==f.name)try{i(f,"name",{value:"values"})}catch(t){}}();var t=Mo(),e=b(),n=Kr(),r=_o();for(var i in t)n(e[i],i),r[i]=r.Array;return Oo}function zo(){if(Co)return wo;Co=1;var t=Wi();return Lo(),wo=t}var Wo,Ho,qo,Uo,Yo,Xo,Vo,Go,Qo,Jo,$o,Zo=e(Eo?ko:(Eo=1,ko=zo())),Ko={};function ta(){if(qo)return Ho;qo=1;var t=b(),e=Ft();return Ho=function(n,r){var i=e[n+"Prototype"],o=i&&i[r];if(o)return o;var a=t[n],s=a&&a.prototype;return s&&s[r]}}function ea(){return Yo?Uo:(Yo=1,function(){if(Wo)return Ko;Wo=1;var t=mn(),e=yn(),n=Sn(),r=Ot(),i=ir(),o=kn(),a=Et(),s=On(),c=be(),u=xn(),l=Cr(),h=u("slice"),f=c("species"),p=Array,d=Math.max;t({target:"Array",proto:!0,forced:!h},{slice:function(t,c){var u,h,v,g=a(this),m=o(g),y=i(t,m),b=i(void 0===c?m:c,m);if(e(g)&&(u=g.constructor,(n(u)&&(u===p||e(u.prototype))||r(u)&&null===(u=u[f]))&&(u=void 0),u===p||void 0===u))return l(g,y,b);for(h=new(void 0===u?p:u)(d(b-y,0)),v=0;y<b;y++,v++)y in g&&s(h,v,g[y]);return h.length=v,h}})}(),Uo=ta()("Array","slice"))}function na(){if(Vo)return Xo;Vo=1;var t=Tt(),e=ea(),n=Array.prototype;return Xo=function(r){var i=r.slice;return r===n||t(n,r)&&i===n.slice?e:i}}function ra(){return Qo?Go:(Qo=1,Go=na())}var ia,oa,aa,sa,ca,ua,la,ha,fa,pa=e($o?Jo:($o=1,Jo=ra()));function da(){if(oa)return ia;oa=1;var t=_t(),e=E(),n=gr(),r=Pr(),i=dn(),o=e([].concat);return ia=t("Reflect","ownKeys")||function(t){var e=n.f(i(t)),a=r.f;return a?o(e,a(t)):e},ia}function va(){return ca?sa:(ca=1,aa||(aa=1,mn()({target:"Reflect",stat:!0},{ownKeys:da()})),sa=Ft().Reflect.ownKeys)}function ga(){return la?ua:(la=1,ua=va())}var ma,ya,ba,wa,Ca,ka,Ea,Oa=e(fa?ha:(fa=1,ha=ga()));function Fa(){return ba?ya:(ba=1,ma||(ma=1,mn()({target:"Array",stat:!0},{isArray:yn()})),ya=Ft().Array.isArray)}function _a(){return Ca?wa:(Ca=1,wa=Fa())}var Ta,Sa,Pa,Da,xa,Aa,ja,Ra,Na,Ia=e(Ea?ka:(Ea=1,ka=_a())),Ba={};function Ma(){return Pa?Sa:(Pa=1,function(){if(Ta)return Ba;Ta=1;var t=mn(),e=ni().map;t({target:"Array",proto:!0,forced:!xn()("map")},{map:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}})}(),Sa=ta()("Array","map"))}function La(){if(xa)return Da;xa=1;var t=Tt(),e=Ma(),n=Array.prototype;return Da=function(r){var i=r.map;return r===n||t(n,r)&&i===n.map?e:i}}function za(){return ja?Aa:(ja=1,Aa=La())}var Wa,Ha,qa,Ua,Ya,Xa,Va,Ga=e(Na?Ra:(Na=1,Ra=za())),Qa={};function Ja(){return qa?Ha:(qa=1,function(){if(Wa)return Qa;Wa=1;var t=mn(),e=ge(),n=ur();t({target:"Object",stat:!0,forced:w()(function(){n(1)})},{keys:function(t){return n(e(t))}})}(),Ha=Ft().Object.keys)}function $a(){return Ya?Ua:(Ya=1,Ua=Ja())}var Za=e(Va?Xa:(Va=1,Xa=$a()));const Ka=Zo("DELETE");function ts(t){for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return es({},t,...n)}function es(){const t=ns(...arguments);return is(t),t}function ns(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(e.length<2)return e[0];if(e.length>2)return ns(es(e[0],e[1]),...pa(e).call(e,2));const r=e[0],i=e[1];if(r instanceof Date&&i instanceof Date)return r.setTime(i.getTime()),r;for(const t of Oa(i))Object.prototype.propertyIsEnumerable.call(i,t)&&(i[t]===Ka?delete r[t]:null===r[t]||null===i[t]||"object"!=typeof r[t]||"object"!=typeof i[t]||Ia(r[t])||Ia(i[t])?r[t]=rs(i[t]):r[t]=ns(r[t],i[t]));return r}function rs(t){return Ia(t)?Ga(t).call(t,t=>rs(t)):"object"==typeof t&&null!==t?t instanceof Date?new Date(t.getTime()):ns({},t):t}function is(t){for(const e of Za(t))t[e]===Ka?delete t[e]:"object"==typeof t[e]&&null!==t[e]&&is(t[e])}var os,as,ss,cs,us,ls,hs,fs={};function ps(){return ss?as:(ss=1,function(){if(os)return fs;os=1;var t=mn(),e=Date,n=E()(e.prototype.getTime);t({target:"Date",stat:!0},{now:function(){return n(new e)}})}(),as=Ft().Date.now)}function ds(){return us?cs:(us=1,cs=ps())}var vs=e(hs?ls:(hs=1,ls=ds()));function gs(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(t){let[e,n,r]=function(){const t=function(){let t=4022871197;return function(e){const n=e.toString();for(let e=0;e<n.length;e++){t+=n.charCodeAt(e);let r=.02519603282416938*t;t=r>>>0,r-=t,r*=t,t=r>>>0,r-=t,t+=4294967296*r}return 2.3283064365386963e-10*(t>>>0)}}();let e=t(" "),n=t(" "),r=t(" ");for(let i=0;i<arguments.length;i++)e-=t(i<0||arguments.length<=i?void 0:arguments[i]),e<0&&(e+=1),n-=t(i<0||arguments.length<=i?void 0:arguments[i]),n<0&&(n+=1),r-=t(i<0||arguments.length<=i?void 0:arguments[i]),r<0&&(r+=1);return[e,n,r]}(t),i=1;const o=()=>{const t=2091639*e+2.3283064365386963e-10*i;return e=n,n=r,r=t-(i=0|t)};return o.uint32=()=>4294967296*o(),o.fract53=()=>o()+11102230246251565e-32*(2097152*o()|0),o.algorithm="Alea",o.seed=t,o.version="0.9",o}(e.length?e:[vs()])}var ms,ys,bs,ws,Cs,ks,Es,Os,Fs,_s,Ts,Ss={};function Ps(){if(ys)return ms;ys=1;var t=E(),e=Rt(),n=Ot(),r=me(),i=Cr(),o=C(),a=Function,s=t([].concat),c=t([].join),u={};return ms=o?a.bind:function(t){var o=e(this),l=o.prototype,h=i(arguments,1),f=function(){var e=s(h,i(arguments));return this instanceof f?function(t,e,n){if(!r(u,e)){for(var i=[],o=0;o<e;o++)i[o]="a["+o+"]";u[e]=a("C,a","return new C("+c(i,",")+")")}return u[e](t,n)}(o,e.length,e):o.apply(t,e)};return n(l)&&(f.prototype=l),f},ms}function Ds(){return Cs?ws:(Cs=1,function(){if(bs)return Ss;bs=1;var t=mn(),e=Ps();t({target:"Function",proto:!0,forced:Function.bind!==e},{bind:e})}(),ws=ta()("Function","bind"))}function xs(){if(Es)return ks;Es=1;var t=Tt(),e=Ds(),n=Function.prototype;return ks=function(r){var i=r.bind;return r===n||t(n,r)&&i===n.bind?e:i}}function As(){return Fs?Os:(Fs=1,Os=xs())}var js,Rs,Ns,Is,Bs,Ms,Ls,zs,Ws,Hs,qs,Us,Ys,Xs=e(Ts?_s:(Ts=1,_s=As())),Vs={};function Gs(){if(Rs)return js;Rs=1;var t=w();return js=function(e,n){var r=[][e];return!!r&&t(function(){r.call(null,n||function(){return 1},1)})}}function Qs(){if(Is)return Ns;Is=1;var t=ni().forEach,e=Gs()("forEach");return Ns=e?[].forEach:function(e){return t(this,e,arguments.length>1?arguments[1]:void 0)},Ns}function Js(){return Ls?Ms:(Ls=1,function(){if(Bs)return Vs;Bs=1;var t=mn(),e=Qs();t({target:"Array",proto:!0,forced:[].forEach!==e},{forEach:e})}(),Ms=ta()("Array","forEach"))}function $s(){return Ws?zs:(Ws=1,zs=Js())}function Zs(){if(qs)return Hs;qs=1;var t=_n(),e=me(),n=Tt(),r=$s(),i=Array.prototype,o={DOMTokenList:!0,NodeList:!0};return Hs=function(a){var s=a.forEach;return a===i||n(i,a)&&s===i.forEach||e(o,t(a))?r:s}}var Ks,tc,ec,nc,rc,ic,oc,ac,sc,cc=e(Ys?Us:(Ys=1,Us=Zs())),uc={};function lc(){return ec?tc:(ec=1,function(){if(Ks)return uc;Ks=1;var t=mn(),e=E(),n=yn(),r=e([].reverse),i=[1,2];t({target:"Array",proto:!0,forced:String(i)===String(i.reverse())},{reverse:function(){return n(this)&&(this.length=this.length),r(this)}})}(),tc=ta()("Array","reverse"))}function hc(){if(rc)return nc;rc=1;var t=Tt(),e=lc(),n=Array.prototype;return nc=function(r){var i=r.reverse;return r===n||t(n,r)&&i===n.reverse?e:i}}function fc(){return oc?ic:(oc=1,ic=hc())}var pc,dc,vc,gc,mc,yc,bc,wc,Cc,kc,Ec,Oc,Fc,_c=e(sc?ac:(sc=1,ac=fc())),Tc={};function Sc(){if(dc)return pc;dc=1;var t=A(),e=yn(),n=TypeError,r=Object.getOwnPropertyDescriptor,i=t&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();return pc=i?function(t,i){if(e(t)&&!r(t,"length").writable)throw new n("Cannot set read only .length");return t.length=i}:function(t,e){return t.length=e}}function Pc(){if(gc)return vc;gc=1;var t=jt(),e=TypeError;return vc=function(n,r){if(!delete n[r])throw new e("Cannot delete property "+t(r)+" of "+t(n))}}function Dc(){return bc?yc:(bc=1,function(){if(mc)return Tc;mc=1;var t=mn(),e=ge(),n=ir(),r=wn(),i=kn(),o=Sc(),a=En(),s=Dn(),c=On(),u=Pc(),l=xn()("splice"),h=Math.max,f=Math.min;t({target:"Array",proto:!0,forced:!l},{splice:function(t,l){var p,d,v,g,m,y,b=e(this),w=i(b),C=n(t,w),k=arguments.length;for(0===k?p=d=0:1===k?(p=0,d=w-C):(p=k-2,d=f(h(r(l),0),w-C)),a(w+p-d),v=s(b,d),g=0;g<d;g++)(m=C+g)in b&&c(v,g,b[m]);if(v.length=d,p<d){for(g=C;g<w-d;g++)y=g+p,(m=g+d)in b?b[y]=b[m]:u(b,y);for(g=w;g>w-d+p;g--)u(b,g-1)}else if(p>d)for(g=w-d;g>C;g--)y=g+p-1,(m=g+d-1)in b?b[y]=b[m]:u(b,y);for(g=0;g<p;g++)b[g+C]=arguments[g+2];return o(b,w-d+p),v}})}(),yc=ta()("Array","splice"))}function xc(){if(Cc)return wc;Cc=1;var t=Tt(),e=Dc(),n=Array.prototype;return wc=function(r){var i=r.splice;return r===n||t(n,r)&&i===n.splice?e:i}}function Ac(){return Ec?kc:(Ec=1,kc=xc())}var jc,Rc=e(Fc?Oc:(Fc=1,Oc=Ac())),Nc={exports:{}};function Ic(){return jc||(jc=1,function(t){function e(t){if(t)return function(t){return Object.assign(t,e.prototype),t._callbacks=new Map,t}(t);this._callbacks=new Map}e.prototype.on=function(t,e){const n=this._callbacks.get(t)??[];return n.push(e),this._callbacks.set(t,n),this},e.prototype.once=function(t,e){const n=(...r)=>{this.off(t,n),e.apply(this,r)};return n.fn=e,this.on(t,n),this},e.prototype.off=function(t,e){if(void 0===t&&void 0===e)return this._callbacks.clear(),this;if(void 0===e)return this._callbacks.delete(t),this;const n=this._callbacks.get(t);if(n){for(const[t,r]of n.entries())if(r===e||r.fn===e){n.splice(t,1);break}0===n.length?this._callbacks.delete(t):this._callbacks.set(t,n)}return this},e.prototype.emit=function(t,...e){const n=this._callbacks.get(t);if(n){const t=[...n];for(const n of t)n.apply(this,e)}return this},e.prototype.listeners=function(t){return this._callbacks.get(t)??[]},e.prototype.listenerCount=function(t){if(t)return this.listeners(t).length;let e=0;for(const t of this._callbacks.values())e+=t.length;return e},e.prototype.hasListeners=function(t){return this.listenerCount(t)>0},e.prototype.addEventListener=e.prototype.on,e.prototype.removeListener=e.prototype.off,e.prototype.removeEventListener=e.prototype.off,e.prototype.removeAllListeners=e.prototype.off,t.exports=e}(Nc)),Nc.exports}var Bc=e(Ic());
/*! Hammer.JS - v2.0.17-rc - 2019-12-16
 * http://naver.github.io/egjs
 *
 * Forked By Naver egjs
 * Copyright (c) hammerjs
 * Licensed under the MIT license */
function Mc(){return Mc=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},Mc.apply(this,arguments)}function Lc(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}function zc(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}var Wc,Hc="function"!=typeof Object.assign?function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),n=1;n<arguments.length;n++){var r=arguments[n];if(null!=r)for(var i in r)r.hasOwnProperty(i)&&(e[i]=r[i])}return e}:Object.assign,qc=["","webkit","Moz","MS","ms","o"],Uc="undefined"==typeof document?{style:{}}:document.createElement("div"),Yc=Math.round,Xc=Math.abs,Vc=Date.now;function Gc(t,e){for(var n,r,i=e[0].toUpperCase()+e.slice(1),o=0;o<qc.length;){if((r=(n=qc[o])?n+i:e)in t)return r;o++}}Wc="undefined"==typeof window?{}:window;var Qc=Gc(Uc.style,"touchAction"),Jc=void 0!==Qc;var $c="compute",Zc="auto",Kc="manipulation",tu="none",eu="pan-x",nu="pan-y",ru=function(){if(!Jc)return!1;var t={},e=Wc.CSS&&Wc.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach(function(n){return t[n]=!e||Wc.CSS.supports("touch-action",n)}),t}(),iu="ontouchstart"in Wc,ou=void 0!==Gc(Wc,"PointerEvent"),au=iu&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),su="touch",cu="mouse",uu=16,lu=24,hu=["x","y"],fu=["clientX","clientY"];function pu(t,e,n){var r;if(t)if(t.forEach)t.forEach(e,n);else if(void 0!==t.length)for(r=0;r<t.length;)e.call(n,t[r],r,t),r++;else for(r in t)t.hasOwnProperty(r)&&e.call(n,t[r],r,t)}function du(t,e){return"function"==typeof t?t.apply(e&&e[0]||void 0,e):t}function vu(t,e){return t.indexOf(e)>-1}var gu=function(){function t(t,e){this.manager=t,this.set(e)}var e=t.prototype;return e.set=function(t){t===$c&&(t=this.compute()),Jc&&this.manager.element.style&&ru[t]&&(this.manager.element.style[Qc]=t),this.actions=t.toLowerCase().trim()},e.update=function(){this.set(this.manager.options.touchAction)},e.compute=function(){var t=[];return pu(this.manager.recognizers,function(e){du(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))}),function(t){if(vu(t,tu))return tu;var e=vu(t,eu),n=vu(t,nu);return e&&n?tu:e||n?e?eu:nu:vu(t,Kc)?Kc:Zc}(t.join(" "))},e.preventDefaults=function(t){var e=t.srcEvent,n=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var r=this.actions,i=vu(r,tu)&&!ru[tu],o=vu(r,nu)&&!ru[nu],a=vu(r,eu)&&!ru[eu];if(i){var s=1===t.pointers.length,c=t.distance<2,u=t.deltaTime<250;if(s&&c&&u)return}if(!a||!o)return i||o&&6&n||a&&n&lu?this.preventSrc(e):void 0}},e.preventSrc=function(t){this.manager.session.prevented=!0,t.preventDefault()},t}();function mu(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1}function yu(t){var e=t.length;if(1===e)return{x:Yc(t[0].clientX),y:Yc(t[0].clientY)};for(var n=0,r=0,i=0;i<e;)n+=t[i].clientX,r+=t[i].clientY,i++;return{x:Yc(n/e),y:Yc(r/e)}}function bu(t){for(var e=[],n=0;n<t.pointers.length;)e[n]={clientX:Yc(t.pointers[n].clientX),clientY:Yc(t.pointers[n].clientY)},n++;return{timeStamp:Vc(),pointers:e,center:yu(e),deltaX:t.deltaX,deltaY:t.deltaY}}function wu(t,e,n){n||(n=hu);var r=e[n[0]]-t[n[0]],i=e[n[1]]-t[n[1]];return Math.sqrt(r*r+i*i)}function Cu(t,e,n){n||(n=hu);var r=e[n[0]]-t[n[0]],i=e[n[1]]-t[n[1]];return 180*Math.atan2(i,r)/Math.PI}function ku(t,e){return t===e?1:Xc(t)>=Xc(e)?t<0?2:4:e<0?8:uu}function Eu(t,e,n){return{x:e/t||0,y:n/t||0}}function Ou(t,e){var n=t.session,r=e.pointers,i=r.length;n.firstInput||(n.firstInput=bu(e)),i>1&&!n.firstMultiple?n.firstMultiple=bu(e):1===i&&(n.firstMultiple=!1);var o=n.firstInput,a=n.firstMultiple,s=a?a.center:o.center,c=e.center=yu(r);e.timeStamp=Vc(),e.deltaTime=e.timeStamp-o.timeStamp,e.angle=Cu(s,c),e.distance=wu(s,c),function(t,e){var n=e.center,r=t.offsetDelta||{},i=t.prevDelta||{},o=t.prevInput||{};1!==e.eventType&&4!==o.eventType||(i=t.prevDelta={x:o.deltaX||0,y:o.deltaY||0},r=t.offsetDelta={x:n.x,y:n.y}),e.deltaX=i.x+(n.x-r.x),e.deltaY=i.y+(n.y-r.y)}(n,e),e.offsetDirection=ku(e.deltaX,e.deltaY);var u,l,h=Eu(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=h.x,e.overallVelocityY=h.y,e.overallVelocity=Xc(h.x)>Xc(h.y)?h.x:h.y,e.scale=a?(u=a.pointers,wu((l=r)[0],l[1],fu)/wu(u[0],u[1],fu)):1,e.rotation=a?function(t,e){return Cu(e[1],e[0],fu)+Cu(t[1],t[0],fu)}(a.pointers,r):0,e.maxPointers=n.prevInput?e.pointers.length>n.prevInput.maxPointers?e.pointers.length:n.prevInput.maxPointers:e.pointers.length,function(t,e){var n,r,i,o,a=t.lastInterval||e,s=e.timeStamp-a.timeStamp;if(8!==e.eventType&&(s>25||void 0===a.velocity)){var c=e.deltaX-a.deltaX,u=e.deltaY-a.deltaY,l=Eu(s,c,u);r=l.x,i=l.y,n=Xc(l.x)>Xc(l.y)?l.x:l.y,o=ku(c,u),t.lastInterval=e}else n=a.velocity,r=a.velocityX,i=a.velocityY,o=a.direction;e.velocity=n,e.velocityX=r,e.velocityY=i,e.direction=o}(n,e);var f,p=t.element,d=e.srcEvent;mu(f=d.composedPath?d.composedPath()[0]:d.path?d.path[0]:d.target,p)&&(p=f),e.target=p}function Fu(t,e,n){var r=n.pointers.length,i=n.changedPointers.length,o=1&e&&r-i===0,a=12&e&&r-i===0;n.isFirst=!!o,n.isFinal=!!a,o&&(t.session={}),n.eventType=e,Ou(t,n),t.emit("hammer.input",n),t.recognize(n),t.session.prevInput=n}function _u(t){return t.trim().split(/\s+/g)}function Tu(t,e,n){pu(_u(e),function(e){t.addEventListener(e,n,!1)})}function Su(t,e,n){pu(_u(e),function(e){t.removeEventListener(e,n,!1)})}function Pu(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||window}var Du=function(){function t(t,e){var n=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){du(t.options.enable,[t])&&n.handler(e)},this.init()}var e=t.prototype;return e.handler=function(){},e.init=function(){this.evEl&&Tu(this.element,this.evEl,this.domHandler),this.evTarget&&Tu(this.target,this.evTarget,this.domHandler),this.evWin&&Tu(Pu(this.element),this.evWin,this.domHandler)},e.destroy=function(){this.evEl&&Su(this.element,this.evEl,this.domHandler),this.evTarget&&Su(this.target,this.evTarget,this.domHandler),this.evWin&&Su(Pu(this.element),this.evWin,this.domHandler)},t}();function xu(t,e,n){if(t.indexOf&&!n)return t.indexOf(e);for(var r=0;r<t.length;){if(n&&t[r][n]==e||!n&&t[r]===e)return r;r++}return-1}var Au={pointerdown:1,pointermove:2,pointerup:4,pointercancel:8,pointerout:8},ju={2:su,3:"pen",4:cu,5:"kinect"},Ru="pointerdown",Nu="pointermove pointerup pointercancel";Wc.MSPointerEvent&&!Wc.PointerEvent&&(Ru="MSPointerDown",Nu="MSPointerMove MSPointerUp MSPointerCancel");var Iu=function(t){function e(){var n,r=e.prototype;return r.evEl=Ru,r.evWin=Nu,(n=t.apply(this,arguments)||this).store=n.manager.session.pointerEvents=[],n}return Lc(e,t),e.prototype.handler=function(t){var e=this.store,n=!1,r=t.type.toLowerCase().replace("ms",""),i=Au[r],o=ju[t.pointerType]||t.pointerType,a=o===su,s=xu(e,t.pointerId,"pointerId");1&i&&(0===t.button||a)?s<0&&(e.push(t),s=e.length-1):12&i&&(n=!0),s<0||(e[s]=t,this.callback(this.manager,i,{pointers:e,changedPointers:[t],pointerType:o,srcEvent:t}),n&&e.splice(s,1))},e}(Du);function Bu(t){return Array.prototype.slice.call(t,0)}function Mu(t,e,n){for(var r=[],i=[],o=0;o<t.length;){var a=e?t[o][e]:t[o];xu(i,a)<0&&r.push(t[o]),i[o]=a,o++}return n&&(r=e?r.sort(function(t,n){return t[e]>n[e]}):r.sort()),r}var Lu={touchstart:1,touchmove:2,touchend:4,touchcancel:8},zu=function(t){function e(){var n;return e.prototype.evTarget="touchstart touchmove touchend touchcancel",(n=t.apply(this,arguments)||this).targetIds={},n}return Lc(e,t),e.prototype.handler=function(t){var e=Lu[t.type],n=Wu.call(this,t,e);n&&this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:su,srcEvent:t})},e}(Du);function Wu(t,e){var n,r,i=Bu(t.touches),o=this.targetIds;if(3&e&&1===i.length)return o[i[0].identifier]=!0,[i,i];var a=Bu(t.changedTouches),s=[],c=this.target;if(r=i.filter(function(t){return mu(t.target,c)}),1===e)for(n=0;n<r.length;)o[r[n].identifier]=!0,n++;for(n=0;n<a.length;)o[a[n].identifier]&&s.push(a[n]),12&e&&delete o[a[n].identifier],n++;return s.length?[Mu(r.concat(s),"identifier",!0),s]:void 0}var Hu={mousedown:1,mousemove:2,mouseup:4},qu=function(t){function e(){var n,r=e.prototype;return r.evEl="mousedown",r.evWin="mousemove mouseup",(n=t.apply(this,arguments)||this).pressed=!1,n}return Lc(e,t),e.prototype.handler=function(t){var e=Hu[t.type];1&e&&0===t.button&&(this.pressed=!0),2&e&&1!==t.which&&(e=4),this.pressed&&(4&e&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:cu,srcEvent:t}))},e}(Du);function Uu(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var n={x:e.clientX,y:e.clientY},r=this.lastTouches;this.lastTouches.push(n);setTimeout(function(){var t=r.indexOf(n);t>-1&&r.splice(t,1)},2500)}}function Yu(t,e){1&t?(this.primaryTouch=e.changedPointers[0].identifier,Uu.call(this,e)):12&t&&Uu.call(this,e)}function Xu(t){for(var e=t.srcEvent.clientX,n=t.srcEvent.clientY,r=0;r<this.lastTouches.length;r++){var i=this.lastTouches[r],o=Math.abs(e-i.x),a=Math.abs(n-i.y);if(o<=25&&a<=25)return!0}return!1}var Vu=function(){return function(t){function e(e,n){var r;return(r=t.call(this,e,n)||this).handler=function(t,e,n){var i=n.pointerType===su,o=n.pointerType===cu;if(!(o&&n.sourceCapabilities&&n.sourceCapabilities.firesTouchEvents)){if(i)Yu.call(zc(zc(r)),e,n);else if(o&&Xu.call(zc(zc(r)),n))return;r.callback(t,e,n)}},r.touch=new zu(r.manager,r.handler),r.mouse=new qu(r.manager,r.handler),r.primaryTouch=null,r.lastTouches=[],r}return Lc(e,t),e.prototype.destroy=function(){this.touch.destroy(),this.mouse.destroy()},e}(Du)}();function Gu(t,e,n){return!!Array.isArray(t)&&(pu(t,n[e],n),!0)}var Qu=32,Ju=1;function $u(t,e){var n=e.manager;return n?n.get(t):t}function Zu(t){return 16&t?"cancel":8&t?"end":4&t?"move":2&t?"start":""}var Ku=function(){function t(t){void 0===t&&(t={}),this.options=Mc({enable:!0},t),this.id=Ju++,this.manager=null,this.state=1,this.simultaneous={},this.requireFail=[]}var e=t.prototype;return e.set=function(t){return Hc(this.options,t),this.manager&&this.manager.touchAction.update(),this},e.recognizeWith=function(t){if(Gu(t,"recognizeWith",this))return this;var e=this.simultaneous;return e[(t=$u(t,this)).id]||(e[t.id]=t,t.recognizeWith(this)),this},e.dropRecognizeWith=function(t){return Gu(t,"dropRecognizeWith",this)||(t=$u(t,this),delete this.simultaneous[t.id]),this},e.requireFailure=function(t){if(Gu(t,"requireFailure",this))return this;var e=this.requireFail;return-1===xu(e,t=$u(t,this))&&(e.push(t),t.requireFailure(this)),this},e.dropRequireFailure=function(t){if(Gu(t,"dropRequireFailure",this))return this;t=$u(t,this);var e=xu(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},e.hasRequireFailures=function(){return this.requireFail.length>0},e.canRecognizeWith=function(t){return!!this.simultaneous[t.id]},e.emit=function(t){var e=this,n=this.state;function r(n){e.manager.emit(n,t)}n<8&&r(e.options.event+Zu(n)),r(e.options.event),t.additionalEvent&&r(t.additionalEvent),n>=8&&r(e.options.event+Zu(n))},e.tryEmit=function(t){if(this.canEmit())return this.emit(t);this.state=Qu},e.canEmit=function(){for(var t=0;t<this.requireFail.length;){if(!(33&this.requireFail[t].state))return!1;t++}return!0},e.recognize=function(t){var e=Hc({},t);if(!du(this.options.enable,[this,e]))return this.reset(),void(this.state=Qu);56&this.state&&(this.state=1),this.state=this.process(e),30&this.state&&this.tryEmit(e)},e.process=function(t){},e.getTouchAction=function(){},e.reset=function(){},t}(),tl=function(t){function e(e){var n;return void 0===e&&(e={}),(n=t.call(this,Mc({event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},e))||this).pTime=!1,n.pCenter=!1,n._timer=null,n._input=null,n.count=0,n}Lc(e,t);var n=e.prototype;return n.getTouchAction=function(){return[Kc]},n.process=function(t){var e=this,n=this.options,r=t.pointers.length===n.pointers,i=t.distance<n.threshold,o=t.deltaTime<n.time;if(this.reset(),1&t.eventType&&0===this.count)return this.failTimeout();if(i&&o&&r){if(4!==t.eventType)return this.failTimeout();var a=!this.pTime||t.timeStamp-this.pTime<n.interval,s=!this.pCenter||wu(this.pCenter,t.center)<n.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,s&&a?this.count+=1:this.count=1,this._input=t,0===this.count%n.taps)return this.hasRequireFailures()?(this._timer=setTimeout(function(){e.state=8,e.tryEmit()},n.interval),2):8}return Qu},n.failTimeout=function(){var t=this;return this._timer=setTimeout(function(){t.state=Qu},this.options.interval),Qu},n.reset=function(){clearTimeout(this._timer)},n.emit=function(){8===this.state&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))},e}(Ku),el=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Mc({pointers:1},e))||this}Lc(e,t);var n=e.prototype;return n.attrTest=function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},n.process=function(t){var e=this.state,n=t.eventType,r=6&e,i=this.attrTest(t);return r&&(8&n||!i)?16|e:r||i?4&n?8|e:2&e?4|e:2:Qu},e}(Ku);function nl(t){return t===uu?"down":8===t?"up":2===t?"left":4===t?"right":""}var rl=function(t){function e(e){var n;return void 0===e&&(e={}),(n=t.call(this,Mc({event:"pan",threshold:10,pointers:1,direction:30},e))||this).pX=null,n.pY=null,n}Lc(e,t);var n=e.prototype;return n.getTouchAction=function(){var t=this.options.direction,e=[];return 6&t&&e.push(nu),t&lu&&e.push(eu),e},n.directionTest=function(t){var e=this.options,n=!0,r=t.distance,i=t.direction,o=t.deltaX,a=t.deltaY;return i&e.direction||(6&e.direction?(i=0===o?1:o<0?2:4,n=o!==this.pX,r=Math.abs(t.deltaX)):(i=0===a?1:a<0?8:uu,n=a!==this.pY,r=Math.abs(t.deltaY))),t.direction=i,n&&r>e.threshold&&i&e.direction},n.attrTest=function(t){return el.prototype.attrTest.call(this,t)&&(2&this.state||!(2&this.state)&&this.directionTest(t))},n.emit=function(e){this.pX=e.deltaX,this.pY=e.deltaY;var n=nl(e.direction);n&&(e.additionalEvent=this.options.event+n),t.prototype.emit.call(this,e)},e}(el),il=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Mc({event:"swipe",threshold:10,velocity:.3,direction:30,pointers:1},e))||this}Lc(e,t);var n=e.prototype;return n.getTouchAction=function(){return rl.prototype.getTouchAction.call(this)},n.attrTest=function(e){var n,r=this.options.direction;return 30&r?n=e.overallVelocity:6&r?n=e.overallVelocityX:r&lu&&(n=e.overallVelocityY),t.prototype.attrTest.call(this,e)&&r&e.offsetDirection&&e.distance>this.options.threshold&&e.maxPointers===this.options.pointers&&Xc(n)>this.options.velocity&&4&e.eventType},n.emit=function(t){var e=nl(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)},e}(el),ol=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Mc({event:"pinch",threshold:0,pointers:2},e))||this}Lc(e,t);var n=e.prototype;return n.getTouchAction=function(){return[tu]},n.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.scale-1)>this.options.threshold||2&this.state)},n.emit=function(e){if(1!==e.scale){var n=e.scale<1?"in":"out";e.additionalEvent=this.options.event+n}t.prototype.emit.call(this,e)},e}(el),al=function(t){function e(e){return void 0===e&&(e={}),t.call(this,Mc({event:"rotate",threshold:0,pointers:2},e))||this}Lc(e,t);var n=e.prototype;return n.getTouchAction=function(){return[tu]},n.attrTest=function(e){return t.prototype.attrTest.call(this,e)&&(Math.abs(e.rotation)>this.options.threshold||2&this.state)},e}(el),sl=function(t){function e(e){var n;return void 0===e&&(e={}),(n=t.call(this,Mc({event:"press",pointers:1,time:251,threshold:9},e))||this)._timer=null,n._input=null,n}Lc(e,t);var n=e.prototype;return n.getTouchAction=function(){return[Zc]},n.process=function(t){var e=this,n=this.options,r=t.pointers.length===n.pointers,i=t.distance<n.threshold,o=t.deltaTime>n.time;if(this._input=t,!i||!r||12&t.eventType&&!o)this.reset();else if(1&t.eventType)this.reset(),this._timer=setTimeout(function(){e.state=8,e.tryEmit()},n.time);else if(4&t.eventType)return 8;return Qu},n.reset=function(){clearTimeout(this._timer)},n.emit=function(t){8===this.state&&(t&&4&t.eventType?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=Vc(),this.manager.emit(this.options.event,this._input)))},e}(Ku),cl={domEvents:!1,touchAction:$c,enable:!0,inputTarget:null,inputClass:null,cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}},ul=[[al,{enable:!1}],[ol,{enable:!1},["rotate"]],[il,{direction:6}],[rl,{direction:6},["swipe"]],[tl],[tl,{event:"doubletap",taps:2},["tap"]],[sl]];function ll(t,e){var n,r=t.element;r.style&&(pu(t.options.cssProps,function(i,o){n=Gc(r.style,o),e?(t.oldCssProps[n]=r.style[n],r.style[n]=i):r.style[n]=t.oldCssProps[n]||""}),e||(t.oldCssProps={}))}var hl=function(){function t(t,e){var n,r=this;this.options=Hc({},cl,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new((n=this).options.inputClass||(ou?Iu:au?zu:iu?Vu:qu))(n,Fu),this.touchAction=new gu(this,this.options.touchAction),ll(this,!0),pu(this.options.recognizers,function(t){var e=r.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])},this)}var e=t.prototype;return e.set=function(t){return Hc(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},e.stop=function(t){this.session.stopped=t?2:1},e.recognize=function(t){var e=this.session;if(!e.stopped){var n;this.touchAction.preventDefaults(t);var r=this.recognizers,i=e.curRecognizer;(!i||i&&8&i.state)&&(e.curRecognizer=null,i=null);for(var o=0;o<r.length;)n=r[o],2===e.stopped||i&&n!==i&&!n.canRecognizeWith(i)?n.reset():n.recognize(t),!i&&14&n.state&&(e.curRecognizer=n,i=n),o++}},e.get=function(t){if(t instanceof Ku)return t;for(var e=this.recognizers,n=0;n<e.length;n++)if(e[n].options.event===t)return e[n];return null},e.add=function(t){if(Gu(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},e.remove=function(t){if(Gu(t,"remove",this))return this;var e=this.get(t);if(t){var n=this.recognizers,r=xu(n,e);-1!==r&&(n.splice(r,1),this.touchAction.update())}return this},e.on=function(t,e){if(void 0===t||void 0===e)return this;var n=this.handlers;return pu(_u(t),function(t){n[t]=n[t]||[],n[t].push(e)}),this},e.off=function(t,e){if(void 0===t)return this;var n=this.handlers;return pu(_u(t),function(t){e?n[t]&&n[t].splice(xu(n[t],e),1):delete n[t]}),this},e.emit=function(t,e){this.options.domEvents&&function(t,e){var n=document.createEvent("Event");n.initEvent(t,!0,!0),n.gesture=e,e.target.dispatchEvent(n)}(t,e);var n=this.handlers[t]&&this.handlers[t].slice();if(n&&n.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var r=0;r<n.length;)n[r](e),r++}},e.destroy=function(){this.element&&ll(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null},t}(),fl={touchstart:1,touchmove:2,touchend:4,touchcancel:8},pl=function(t){function e(){var n,r=e.prototype;return r.evTarget="touchstart",r.evWin="touchstart touchmove touchend touchcancel",(n=t.apply(this,arguments)||this).started=!1,n}return Lc(e,t),e.prototype.handler=function(t){var e=fl[t.type];if(1===e&&(this.started=!0),this.started){var n=dl.call(this,t,e);12&e&&n[0].length-n[1].length===0&&(this.started=!1),this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:su,srcEvent:t})}},e}(Du);function dl(t,e){var n=Bu(t.touches),r=Bu(t.changedTouches);return 12&e&&(n=Mu(n.concat(r),"identifier",!0)),[n,r]}function vl(t,e,n){var r="DEPRECATED METHOD: "+e+"\n"+n+" AT \n";return function(){var e=new Error("get-stack-trace"),n=e&&e.stack?e.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",i=window.console&&(window.console.warn||window.console.log);return i&&i.call(window.console,r,n),t.apply(this,arguments)}}var gl=vl(function(t,e,n){for(var r=Object.keys(e),i=0;i<r.length;)(!n||n&&void 0===t[r[i]])&&(t[r[i]]=e[r[i]]),i++;return t},"extend","Use `assign`."),ml=vl(function(t,e){return gl(t,e,!0)},"merge","Use `assign`.");function yl(t,e,n){var r,i=e.prototype;(r=t.prototype=Object.create(i)).constructor=t,r._super=i,n&&Hc(r,n)}function bl(t,e){return function(){return t.apply(e,arguments)}}var wl=function(){var t=function(t,e){return void 0===e&&(e={}),new hl(t,Mc({recognizers:ul.concat()},e))};return t.VERSION="2.0.17-rc",t.DIRECTION_ALL=30,t.DIRECTION_DOWN=uu,t.DIRECTION_LEFT=2,t.DIRECTION_RIGHT=4,t.DIRECTION_UP=8,t.DIRECTION_HORIZONTAL=6,t.DIRECTION_VERTICAL=lu,t.DIRECTION_NONE=1,t.DIRECTION_DOWN=uu,t.INPUT_START=1,t.INPUT_MOVE=2,t.INPUT_END=4,t.INPUT_CANCEL=8,t.STATE_POSSIBLE=1,t.STATE_BEGAN=2,t.STATE_CHANGED=4,t.STATE_ENDED=8,t.STATE_RECOGNIZED=8,t.STATE_CANCELLED=16,t.STATE_FAILED=Qu,t.Manager=hl,t.Input=Du,t.TouchAction=gu,t.TouchInput=zu,t.MouseInput=qu,t.PointerEventInput=Iu,t.TouchMouseInput=Vu,t.SingleTouchInput=pl,t.Recognizer=Ku,t.AttrRecognizer=el,t.Tap=tl,t.Pan=rl,t.Swipe=il,t.Pinch=ol,t.Rotate=al,t.Press=sl,t.on=Tu,t.off=Su,t.each=pu,t.merge=ml,t.extend=gl,t.bindFn=bl,t.assign=Hc,t.inherit=yl,t.bindFn=bl,t.prefixed=Gc,t.toArray=Bu,t.inArray=xu,t.uniqueArray=Mu,t.splitStr=_u,t.boolOrFn=du,t.hasParent=mu,t.addEventListeners=Tu,t.removeEventListeners=Su,t.defaults=Hc({},cl,{preset:ul}),t}();wl.defaults;const Cl="undefined"!=typeof window?window.Hammer||wl:function(){return function(){const t=()=>{};return{on:t,off:t,destroy:t,emit:t,get:()=>({set:t})}}()};function kl(t){var e;this._cleanupQueue=[],this.active=!1,this._dom={container:t,overlay:document.createElement("div")},this._dom.overlay.classList.add("vis-overlay"),this._dom.container.appendChild(this._dom.overlay),this._cleanupQueue.push(()=>{this._dom.overlay.parentNode.removeChild(this._dom.overlay)});const n=Cl(this._dom.overlay);n.on("tap",Xs(e=this._onTapOverlay).call(e,this)),this._cleanupQueue.push(()=>{n.destroy()});const r=["tap","doubletap","press","pinch","pan","panstart","panmove","panend"];cc(r).call(r,t=>{n.on(t,t=>{t.srcEvent.stopPropagation()})}),document&&document.body&&(this._onClick=e=>{(function(t,e){for(;t;){if(t===e)return!0;t=t.parentNode}return!1})(e.target,t)||this.deactivate()},document.body.addEventListener("click",this._onClick),this._cleanupQueue.push(()=>{document.body.removeEventListener("click",this._onClick)})),this._escListener=t=>{("key"in t?"Escape"===t.key:27===t.keyCode)&&this.deactivate()}}Bc(kl.prototype),kl.current=null,kl.prototype.destroy=function(){this.deactivate();for(const n of _c(t=Rc(e=this._cleanupQueue).call(e,0)).call(t)){var t,e;n()}},kl.prototype.activate=function(){kl.current&&kl.current.deactivate(),kl.current=this,this.active=!0,this._dom.overlay.style.display="none",this._dom.container.classList.add("vis-active"),this.emit("change"),this.emit("activate"),document.body.addEventListener("keydown",this._escListener)},kl.prototype.deactivate=function(){this.active=!1,this._dom.overlay.style.display="block",this._dom.container.classList.remove("vis-active"),document.body.removeEventListener("keydown",this._escListener),this.emit("change"),this.emit("deactivate")},kl.prototype._onTapOverlay=function(t){this.activate(),t.srcEvent.stopPropagation()};var El,Ol,Fl,_l,Tl,Sl,Pl,Dl,xl,Al,jl,Rl,Nl,Il={};function Bl(){if(Ol)return El;Ol=1;var t=wn(),e=Bn(),n=kt(),r=RangeError;return El=function(i){var o=e(n(this)),a="",s=t(i);if(s<0||s===1/0)throw new r("Wrong number of repetitions");for(;s>0;(s>>>=1)&&(o+=o))1&s&&(a+=o);return a}}function Ml(){if(_l)return Fl;_l=1;var t=E(),e=Cn(),n=Bn(),r=Bl(),i=kt(),o=t(r),a=t("".slice),s=Math.ceil,c=function(t){return function(r,c,u){var l,h,f=n(i(r)),p=e(c),d=f.length,v=void 0===u?" ":n(u);return p<=d||""===v?f:((h=o(v,s((l=p-d)/v.length))).length>l&&(h=a(h,0,l)),t?f+h:h+f)}};return Fl={start:c(!1),end:c(!0)}}function Ll(){if(Sl)return Tl;Sl=1;var t=E(),e=w(),n=Ml().start,r=RangeError,i=isFinite,o=Math.abs,a=Date.prototype,s=a.toISOString,c=t(a.getTime),u=t(a.getUTCDate),l=t(a.getUTCFullYear),h=t(a.getUTCHours),f=t(a.getUTCMilliseconds),p=t(a.getUTCMinutes),d=t(a.getUTCMonth),v=t(a.getUTCSeconds);return Tl=e(function(){return"0385-07-25T07:06:39.999Z"!==s.call(new Date(-50000000000001))})||!e(function(){s.call(new Date(NaN))})?function(){if(!i(c(this)))throw new r("Invalid time value");var t=this,e=l(t),a=f(t),s=e<0?"-":e>9999?"+":"";return s+n(o(e),s?6:4,0)+"-"+n(d(t)+1,2,0)+"-"+n(u(t),2,0)+"T"+n(h(t),2,0)+":"+n(p(t),2,0)+":"+n(v(t),2,0)+"."+n(a,3,0)+"Z"}:s}function zl(){if(xl)return Dl;xl=1,function(){if(Pl)return Il;Pl=1;var t=mn(),e=j(),n=ge(),r=we(),i=Ll(),o=O();t({target:"Date",proto:!0,forced:w()(function(){return null!==new Date(NaN).toJSON()||1!==e(Date.prototype.toJSON,{toISOString:function(){return 1}})})},{toJSON:function(t){var a=n(this),s=r(a,"number");return"number"!=typeof s||isFinite(s)?"toISOString"in a||"Date"!==o(a)?a.toISOString():e(i,a):null}})}(),vi();var t=Ft(),e=k();return t.JSON||(t.JSON={stringify:JSON.stringify}),Dl=function(n,r,i){return e(t.JSON.stringify,null,arguments)},Dl}function Wl(){return jl?Al:(jl=1,Al=zl())}var Hl,ql,Ul,Yl,Xl,Vl,Gl,Ql,Jl,$l=e(Nl?Rl:(Nl=1,Rl=Wl())),Zl={};function Kl(){if(ql)return Hl;ql=1;var t=A(),e=E(),n=j(),r=w(),i=ur(),o=Pr(),a=yt(),s=ge(),c=wt(),u=Object.assign,l=Object.defineProperty,h=e([].concat);return Hl=!u||r(function(){if(t&&1!==u({b:1},u(l({},"a",{enumerable:!0,get:function(){l(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var e={},n={},r=Symbol("assign detection"),o="abcdefghijklmnopqrst";return e[r]=7,o.split("").forEach(function(t){n[t]=t}),7!==u({},e)[r]||i(u({},n)).join("")!==o})?function(e,r){for(var u=s(e),l=arguments.length,f=1,p=o.f,d=a.f;l>f;)for(var v,g=c(arguments[f++]),m=p?h(i(g),p(g)):i(g),y=m.length,b=0;y>b;)v=m[b++],t&&!n(d,g,v)||(u[v]=g[v]);return u}:u,Hl}function th(){return Xl?Yl:(Xl=1,function(){if(Ul)return Zl;Ul=1;var t=mn(),e=Kl();t({target:"Object",stat:!0,arity:2,forced:Object.assign!==e},{assign:e})}(),Yl=Ft().Object.assign)}function eh(){return Gl?Vl:(Gl=1,Vl=th())}var nh,rh,ih,oh,ah,sh,ch,uh=e(Jl?Ql:(Jl=1,Ql=eh())),lh={},hh={};function fh(){if(rh)return nh;rh=1;var t=b(),e=St(),n=O(),r=function(t){return e.slice(0,t.length)===t};return nh=r("Bun/")?"BUN":r("Cloudflare-Workers")?"CLOUDFLARE":r("Deno/")?"DENO":r("Node.js/")?"NODE":t.Bun&&"string"==typeof Bun.version?"BUN":t.Deno&&"object"==typeof Deno.version?"DENO":"process"===n(t.process)?"NODE":t.window&&t.document?"BROWSER":"REST"}function ph(){if(oh)return ih;oh=1;var t=TypeError;return ih=function(e,n){if(e<n)throw new t("Not enough arguments");return e}}function dh(){if(sh)return ah;sh=1;var t,e=b(),n=k(),r=_(),i=fh(),o=St(),a=Cr(),s=ph(),c=e.Function,u=/MSIE .\./.test(o)||"BUN"===i&&((t=e.Bun.version.split(".")).length<3||"0"===t[0]&&(t[1]<3||"3"===t[1]&&"0"===t[2]));return ah=function(t,e){var i=e?2:1;return u?function(o,u){var l=s(arguments.length,1)>i,h=r(o)?o:c(o),f=l?a(arguments,i):[],p=l?function(){n(h,this,f)}:h;return e?t(p,u):t(p)}:t},ah}var vh,gh,mh,yh,bh,wh,Ch={};function kh(){return gh||(gh=1,function(){if(ch)return hh;ch=1;var t=mn(),e=b(),n=dh()(e.setInterval,!0);t({global:!0,bind:!0,forced:e.setInterval!==n},{setInterval:n})}(),function(){if(vh)return Ch;vh=1;var t=mn(),e=b(),n=dh()(e.setTimeout,!0);t({global:!0,bind:!0,forced:e.setTimeout!==n},{setTimeout:n})}()),lh}function Eh(){return yh?mh:(yh=1,kh(),mh=Ft().setTimeout)}var Oh,Fh,_h,Th,Sh,Ph,Dh,xh,Ah,jh,Rh,Nh=e(wh?bh:(wh=1,bh=Eh())),Ih={};function Bh(){if(Fh)return Oh;Fh=1;var t=ge(),e=ir(),n=kn();return Oh=function(r){for(var i=t(this),o=n(i),a=arguments.length,s=e(a>1?arguments[1]:void 0,o),c=a>2?arguments[2]:void 0,u=void 0===c?o:e(c,o);u>s;)i[s++]=r;return i},Oh}function Mh(){return Sh?Th:(Sh=1,function(){if(_h)return Ih;_h=1;var t=mn(),e=Bh(),n=Fo();t({target:"Array",proto:!0},{fill:e}),n("fill")}(),Th=ta()("Array","fill"))}function Lh(){if(Dh)return Ph;Dh=1;var t=Tt(),e=Mh(),n=Array.prototype;return Ph=function(r){var i=r.fill;return r===n||t(n,r)&&i===n.fill?e:i}}function zh(){return Ah?xh:(Ah=1,xh=Lh())}var Wh,Hh,qh,Uh=e(Rh?jh:(Rh=1,jh=zh())),Yh={};function Xh(){return qh?Hh:(qh=1,function(){if(Wh)return Yh;Wh=1;var t=mn(),e=or().includes,n=w(),r=Fo();t({target:"Array",proto:!0,forced:n(function(){return!Array(1).includes()})},{includes:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}}),r("includes")}(),Hh=ta()("Array","includes"))}var Vh,Gh,Qh,Jh,$h,Zh,Kh,tf,ef,nf,rf,of,af,sf,cf,uf={};function lf(){if(Gh)return Vh;Gh=1;var t=Ot(),e=O(),n=be()("match");return Vh=function(r){var i;return t(r)&&(void 0!==(i=r[n])?!!i:"RegExp"===e(r))}}function hf(){if(Jh)return Qh;Jh=1;var t=lf(),e=TypeError;return Qh=function(n){if(t(n))throw new e("The method doesn't accept regular expressions");return n}}function ff(){if(Zh)return $h;Zh=1;var t=be()("match");return $h=function(e){var n=/./;try{"/./"[e](n)}catch(r){try{return n[t]=!1,"/./"[e](n)}catch(t){}}return!1}}function pf(){return ef?tf:(ef=1,function(){if(Kh)return uf;Kh=1;var t=mn(),e=E(),n=hf(),r=kt(),i=Bn(),o=ff(),a=e("".indexOf);t({target:"String",proto:!0,forced:!o("includes")},{includes:function(t){return!!~a(i(r(this)),i(n(t)),arguments.length>1?arguments[1]:void 0)}})}(),tf=ta()("String","includes"))}function df(){if(rf)return nf;rf=1;var t=Tt(),e=Xh(),n=pf(),r=Array.prototype,i=String.prototype;return nf=function(o){var a=o.includes;return o===r||t(r,o)&&a===r.includes?e:"string"==typeof o||o===i||t(i,o)&&a===i.includes?n:a}}function vf(){return af?of:(af=1,of=df())}var gf,mf,yf,bf,wf,Cf,kf,Ef=e(cf?sf:(cf=1,sf=vf())),Of={};function Ff(){return yf?mf:(yf=1,function(){if(gf)return Of;gf=1;var t=mn(),e=w(),n=ge(),r=Po(),i=So();t({target:"Object",stat:!0,forced:e(function(){r(1)}),sham:!i},{getPrototypeOf:function(t){return r(n(t))}})}(),mf=Ft().Object.getPrototypeOf)}function _f(){return wf?bf:(wf=1,bf=Ff())}var Tf,Sf,Pf,Df,xf,Af,jf,Rf,Nf=e(kf?Cf:(kf=1,Cf=_f()));function If(){return Sf?Tf:(Sf=1,An(),Tf=ta()("Array","concat"))}function Bf(){if(Df)return Pf;Df=1;var t=Tt(),e=If(),n=Array.prototype;return Pf=function(r){var i=r.concat;return r===n||t(n,r)&&i===n.concat?e:i}}function Mf(){return Af?xf:(Af=1,xf=Bf())}var Lf,zf,Wf,Hf,qf,Uf,Yf,Xf,Vf,Gf=e(Rf?jf:(Rf=1,jf=Mf())),Qf={};function Jf(){return Wf?zf:(Wf=1,function(){if(Lf)return Qf;Lf=1;var t=mn(),e=ni().filter;t({target:"Array",proto:!0,forced:!xn()("filter")},{filter:function(t){return e(this,t,arguments.length>1?arguments[1]:void 0)}})}(),zf=ta()("Array","filter"))}function $f(){if(qf)return Hf;qf=1;var t=Tt(),e=Jf(),n=Array.prototype;return Hf=function(r){var i=r.filter;return r===n||t(n,r)&&i===n.filter?e:i}}function Zf(){return Yf?Uf:(Yf=1,Uf=$f())}var Kf,tp,ep,np,rp,ip,op,ap,sp,cp=e(Vf?Xf:(Vf=1,Xf=Zf())),up={};function lp(){if(tp)return Kf;tp=1;var t=A(),e=w(),n=E(),r=Po(),i=ur(),o=Et(),a=n(yt().f),s=n([].push),c=t&&e(function(){var t=Object.create(null);return t[2]=2,!a(t,2)}),u=function(e){return function(n){for(var u,l=o(n),h=i(l),f=c&&null===r(l),p=h.length,d=0,v=[];p>d;)u=h[d++],t&&!(f?u in l:a(l,u))||s(v,e?[u,l[u]]:l[u]);return v}};return Kf={entries:u(!0),values:u(!1)}}function hp(){return rp?np:(rp=1,function(){if(ep)return up;ep=1;var t=mn(),e=lp().values;t({target:"Object",stat:!0},{values:function(t){return e(t)}})}(),np=Ft().Object.values)}function fp(){return op?ip:(op=1,ip=hp())}var pp,dp,vp,gp,mp,yp,bp,wp,Cp,kp,Ep,Op,Fp,_p=e(sp?ap:(sp=1,ap=fp())),Tp={};function Sp(){return dp?pp:(dp=1,pp="\t\n\v\f\r                　\u2028\u2029\ufeff")}function Pp(){if(gp)return vp;gp=1;var t=E(),e=kt(),n=Bn(),r=Sp(),i=t("".replace),o=RegExp("^["+r+"]+"),a=RegExp("(^|[^"+r+"])["+r+"]+$"),s=function(t){return function(r){var s=n(e(r));return 1&t&&(s=i(s,o,"")),2&t&&(s=i(s,a,"$1")),s}};return vp={start:s(1),end:s(2),trim:s(3)}}function Dp(){if(yp)return mp;yp=1;var t=b(),e=w(),n=E(),r=Bn(),i=Pp().trim,o=Sp(),a=t.parseInt,s=t.Symbol,c=s&&s.iterator,u=/^[+-]?0x/i,l=n(u.exec),h=8!==a(o+"08")||22!==a(o+"0x16")||c&&!e(function(){a(Object(c))});return mp=h?function(t,e){var n=i(r(t));return a(n,e>>>0||(l(u,n)?16:10))}:a}function xp(){return Cp?wp:(Cp=1,function(){if(bp)return Tp;bp=1;var t=mn(),e=Dp();t({global:!0,forced:parseInt!==e},{parseInt:e})}(),wp=Ft().parseInt)}function Ap(){return Ep?kp:(Ep=1,kp=xp())}var jp,Rp,Np,Ip,Bp,Mp,Lp,zp,Wp,Hp=e(Fp?Op:(Fp=1,Op=Ap())),qp={};function Up(){return Np?Rp:(Np=1,function(){if(jp)return qp;jp=1;var t=mn(),e=F(),n=or().indexOf,r=Gs(),i=e([].indexOf),o=!!i&&1/i([1],1,-0)<0;t({target:"Array",proto:!0,forced:o||!r("indexOf")},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return o?i(this,t,e)||0:n(this,t,e)}})}(),Rp=ta()("Array","indexOf"))}function Yp(){if(Bp)return Ip;Bp=1;var t=Tt(),e=Up(),n=Array.prototype;return Ip=function(r){var i=r.indexOf;return r===n||t(n,r)&&i===n.indexOf?e:i}}function Xp(){return Lp?Mp:(Lp=1,Mp=Yp())}var Vp,Gp,Qp,Jp,$p,Zp,Kp,td=e(Wp?zp:(Wp=1,zp=Xp())),ed={};function nd(){return Qp?Gp:(Qp=1,function(){if(Vp)return ed;Vp=1;var t=mn(),e=lp().entries;t({target:"Object",stat:!0},{entries:function(t){return e(t)}})}(),Gp=Ft().Object.entries)}function rd(){return $p?Jp:($p=1,Jp=nd())}var id,od,ad,sd,cd,ud,ld,hd=e(Kp?Zp:(Kp=1,Zp=rd()));function fd(){if(ad)return od;ad=1,id||(id=1,mn()({target:"Object",stat:!0,sham:!A()},{create:pr()}));var t=Ft().Object;return od=function(e,n){return t.create(e,n)}}function pd(){return cd?sd:(cd=1,sd=fd())}var dd=e(ld?ud:(ld=1,ud=pd()));const vd=/^\/?Date\((-?\d+)/i,gd=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,md=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,yd=/^rgb\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *\)$/i,bd=/^rgba\( *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *(1?\d{1,2}|2[0-4]\d|25[0-5]) *, *([01]|0?\.\d+) *\)$/i;function wd(t){return t instanceof Number||"number"==typeof t}function Cd(t){if(t)for(;!0===t.hasChildNodes();){const e=t.firstChild;e&&(Cd(e),t.removeChild(e))}}function kd(t){return t instanceof String||"string"==typeof t}function Ed(t){return"object"==typeof t&&null!==t}function Od(t){if(t instanceof Date)return!0;if(kd(t)){if(vd.exec(t))return!0;if(!isNaN(Date.parse(t)))return!0}return!1}function Fd(t,e,n,r){let i=!1;!0===r&&(i=null===e[n]&&void 0!==t[n]),i?delete t[n]:t[n]=e[n]}function _d(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];for(const r in t)if(void 0!==e[r])if(null===e[r]||"object"!=typeof e[r])Fd(t,e,r,n);else{const i=t[r],o=e[r];Ed(i)&&Ed(o)&&_d(i,o,n)}}const Td=uh;function Sd(t,e){if(!Ia(t))throw new Error("Array with property names expected as first argument");for(var n=arguments.length,r=new Array(n>2?n-2:0),i=2;i<n;i++)r[i-2]=arguments[i];for(const n of r)for(let r=0;r<t.length;r++){const i=t[r];n&&Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}function Pd(t,e,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(Ia(n))throw new TypeError("Arrays are not supported by deepExtend");for(let i=0;i<t.length;i++){const o=t[i];if(Object.prototype.hasOwnProperty.call(n,o))if(n[o]&&n[o].constructor===Object)void 0===e[o]&&(e[o]={}),e[o].constructor===Object?xd(e[o],n[o],!1,r):Fd(e,n,o,r);else{if(Ia(n[o]))throw new TypeError("Arrays are not supported by deepExtend");Fd(e,n,o,r)}}return e}function Dd(t,e,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(Ia(n))throw new TypeError("Arrays are not supported by deepExtend");for(const i in n)if(Object.prototype.hasOwnProperty.call(n,i)&&!Ef(t).call(t,i))if(n[i]&&n[i].constructor===Object)void 0===e[i]&&(e[i]={}),e[i].constructor===Object?xd(e[i],n[i]):Fd(e,n,i,r);else if(Ia(n[i])){e[i]=[];for(let t=0;t<n[i].length;t++)e[i].push(n[i][t])}else Fd(e,n,i,r);return e}function xd(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];for(const o in e)if(Object.prototype.hasOwnProperty.call(e,o)||!0===n)if("object"==typeof e[o]&&null!==e[o]&&Nf(e[o])===Object.prototype)void 0===t[o]?t[o]=xd({},e[o],n):"object"==typeof t[o]&&null!==t[o]&&Nf(t[o])===Object.prototype?xd(t[o],e[o],n):Fd(t,e,o,r);else if(Ia(e[o])){var i;t[o]=pa(i=e[o]).call(i)}else Fd(t,e,o,r);return t}function Ad(t,e){if(t.length!==e.length)return!1;for(let n=0,r=t.length;n<r;n++)if(t[n]!=e[n])return!1;return!0}function jd(t){const e=typeof t;return"object"===e?null===t?"null":t instanceof Boolean?"Boolean":t instanceof Number?"Number":t instanceof String?"String":Ia(t)?"Array":t instanceof Date?"Date":"Object":"number"===e?"Number":"boolean"===e?"Boolean":"string"===e?"String":void 0===e?"undefined":e}function Rd(t,e){return[...t,e]}function Nd(t){return pa(t).call(t)}function Id(t){return t.getBoundingClientRect().left}function Bd(t){return t.getBoundingClientRect().right}function Md(t){return t.getBoundingClientRect().top}function Ld(t,e){let n=t.className.split(" ");const r=e.split(" ");n=Gf(n).call(n,cp(r).call(r,function(t){return!Ef(n).call(n,t)})),t.className=n.join(" ")}function zd(t,e){let n=t.className.split(" ");const r=e.split(" ");n=cp(n).call(n,function(t){return!Ef(r).call(r,t)}),t.className=n.join(" ")}function Wd(t,e){if(Ia(t)){const n=t.length;for(let r=0;r<n;r++)e(t[r],r,t)}else for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&e(t[n],n,t)}const Hd=_p;function qd(t,e,n){return t[e]!==n&&(t[e]=n,!0)}function Ud(t){let e=!1;return()=>{e||(e=!0,requestAnimationFrame(()=>{e=!1,t()}))}}function Yd(t){t||(t=window.event),t&&(t.preventDefault?t.preventDefault():t.returnValue=!1)}function Xd(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.event,e=null;return t&&(t.target?e=t.target:t.srcElement&&(e=t.srcElement)),e instanceof Element&&(null==e.nodeType||3!=e.nodeType||(e=e.parentNode,e instanceof Element))?e:null}function Vd(t,e){let n=t;for(;n;){if(n===e)return!0;if(!n.parentNode)return!1;n=n.parentNode}return!1}const Gd={asBoolean:(t,e)=>("function"==typeof t&&(t=t()),null!=t?0!=t:e||null),asNumber:(t,e)=>("function"==typeof t&&(t=t()),null!=t?Number(t)||e||null:e||null),asString:(t,e)=>("function"==typeof t&&(t=t()),null!=t?String(t):e||null),asSize:(t,e)=>("function"==typeof t&&(t=t()),kd(t)?t:wd(t)?t+"px":e||null),asElement:(t,e)=>("function"==typeof t&&(t=t()),t||e||null)};function Qd(t){let e;switch(t.length){case 3:case 4:return e=md.exec(t),e?{r:Hp(e[1]+e[1],16),g:Hp(e[2]+e[2],16),b:Hp(e[3]+e[3],16)}:null;case 6:case 7:return e=gd.exec(t),e?{r:Hp(e[1],16),g:Hp(e[2],16),b:Hp(e[3],16)}:null;default:return null}}function Jd(t,e){if(Ef(t).call(t,"rgba"))return t;if(Ef(t).call(t,"rgb")){const n=t.substr(td(t).call(t,"(")+1).replace(")","").split(",");return"rgba("+n[0]+","+n[1]+","+n[2]+","+e+")"}{const n=Qd(t);return null==n?t:"rgba("+n.r+","+n.g+","+n.b+","+e+")"}}function $d(t,e,n){var r;return"#"+pa(r=((1<<24)+(t<<16)+(e<<8)+n).toString(16)).call(r,1)}function Zd(t,e){if(kd(t)){let e=t;if(sv(e)){var n;const t=Ga(n=e.substr(4).substr(0,e.length-5).split(",")).call(n,function(t){return Hp(t)});e=$d(t[0],t[1],t[2])}if(!0===av(e)){const t=ov(e),n={h:t.h,s:.8*t.s,v:Math.min(1,1.02*t.v)},r={h:t.h,s:Math.min(1,1.25*t.s),v:.8*t.v},i=iv(r.h,r.s,r.v),o=iv(n.h,n.s,n.v);return{background:e,border:i,highlight:{background:o,border:i},hover:{background:o,border:i}}}return{background:e,border:e,highlight:{background:e,border:e},hover:{background:e,border:e}}}if(e){return{background:t.background||e.background,border:t.border||e.border,highlight:kd(t.highlight)?{border:t.highlight,background:t.highlight}:{background:t.highlight&&t.highlight.background||e.highlight.background,border:t.highlight&&t.highlight.border||e.highlight.border},hover:kd(t.hover)?{border:t.hover,background:t.hover}:{border:t.hover&&t.hover.border||e.hover.border,background:t.hover&&t.hover.background||e.hover.background}}}return{background:t.background||void 0,border:t.border||void 0,highlight:kd(t.highlight)?{border:t.highlight,background:t.highlight}:{background:t.highlight&&t.highlight.background||void 0,border:t.highlight&&t.highlight.border||void 0},hover:kd(t.hover)?{border:t.hover,background:t.hover}:{border:t.hover&&t.hover.border||void 0,background:t.hover&&t.hover.background||void 0}}}function Kd(t,e,n){t/=255,e/=255,n/=255;const r=Math.min(t,Math.min(e,n)),i=Math.max(t,Math.max(e,n));if(r===i)return{h:0,s:0,v:r};return{h:60*((t===r?3:n===r?1:5)-(t===r?e-n:n===r?t-e:n-t)/(i-r))/360,s:(i-r)/i,v:i}}function tv(t){const e=document.createElement("div"),n={};e.style.cssText=t;for(let t=0;t<e.style.length;++t)n[e.style[t]]=e.style.getPropertyValue(e.style[t]);return n}function ev(t,e){const n=tv(e);for(const[e,r]of hd(n))t.style.setProperty(e,r)}function nv(t,e){const n=tv(e);for(const e of Za(n))t.style.removeProperty(e)}function rv(t,e,n){let r,i,o;const a=Math.floor(6*t),s=6*t-a,c=n*(1-e),u=n*(1-s*e),l=n*(1-(1-s)*e);switch(a%6){case 0:r=n,i=l,o=c;break;case 1:r=u,i=n,o=c;break;case 2:r=c,i=n,o=l;break;case 3:r=c,i=u,o=n;break;case 4:r=l,i=c,o=n;break;case 5:r=n,i=c,o=u}return{r:Math.floor(255*r),g:Math.floor(255*i),b:Math.floor(255*o)}}function iv(t,e,n){const r=rv(t,e,n);return $d(r.r,r.g,r.b)}function ov(t){const e=Qd(t);if(!e)throw new TypeError("'".concat(t,"' is not a valid color."));return Kd(e.r,e.g,e.b)}function av(t){return/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(t)}function sv(t){return yd.test(t)}function cv(t){return bd.test(t)}function uv(t,e){if(null!==e&&"object"==typeof e){const n=dd(e);for(let r=0;r<t.length;r++)Object.prototype.hasOwnProperty.call(e,t[r])&&"object"==typeof e[t[r]]&&(n[t[r]]=lv(e[t[r]]));return n}return null}function lv(t){if(null===t||"object"!=typeof t)return null;if(t instanceof Element)return t;const e=dd(t);for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&"object"==typeof t[n]&&(e[n]=lv(t[n]));return e}function hv(t,e){for(let n=0;n<t.length;n++){const r=t[n];let i;for(i=n;i>0&&e(r,t[i-1])<0;i--)t[i]=t[i-1];t[i]=r}return t}function fv(t,e,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const i=function(t){return null!=t},o=function(t){return null!==t&&"object"==typeof t};if(!o(t))throw new Error("Parameter mergeTarget must be an object");if(!o(e))throw new Error("Parameter options must be an object");if(!i(n))throw new Error("Parameter option must have a value");if(!o(r))throw new Error("Parameter globalOptions must be an object");const a=e[n],s=o(r)&&!function(t){for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e))return!1;return!0}(r)?r[n]:void 0,c=s?s.enabled:void 0;if(void 0===a)return;if("boolean"==typeof a)return o(t[n])||(t[n]={}),void(t[n].enabled=a);if(null===a&&!o(t[n])){if(!i(s))return;t[n]=dd(s)}if(!o(a))return;let u=!0;void 0!==a.enabled?u=a.enabled:void 0!==c&&(u=s.enabled),function(t,e,n){o(t[n])||(t[n]={});const r=e[n],i=t[n];for(const t in r)Object.prototype.hasOwnProperty.call(r,t)&&(i[t]=r[t])}(t,e,n),t[n].enabled=u}function pv(t,e,n,r){let i=0,o=0,a=t.length-1;for(;o<=a&&i<1e4;){const s=Math.floor((o+a)/2),c=t[s],u=e(void 0===r?c[n]:c[n][r]);if(0==u)return s;-1==u?o=s+1:a=s-1,i++}return-1}function dv(t,e,n,r,i){let o,a,s,c,u=0,l=0,h=t.length-1;for(i=null!=i?i:function(t,e){return t==e?0:t<e?-1:1};l<=h&&u<1e4;){if(c=Math.floor(.5*(h+l)),o=t[Math.max(0,c-1)][n],a=t[c][n],s=t[Math.min(t.length-1,c+1)][n],0==i(a,e))return c;if(i(o,e)<0&&i(a,e)>0)return"before"==r?Math.max(0,c-1):c;if(i(a,e)<0&&i(s,e)>0)return"before"==r?c:Math.min(t.length-1,c+1);i(a,e)<0?l=c+1:h=c-1,u++}return-1}const vv={linear:t=>t,easeInQuad:t=>t*t,easeOutQuad:t=>t*(2-t),easeInOutQuad:t=>t<.5?2*t*t:(4-2*t)*t-1,easeInCubic:t=>t*t*t,easeOutCubic:t=>--t*t*t+1,easeInOutCubic:t=>t<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1,easeInQuart:t=>t*t*t*t,easeOutQuart:t=>1- --t*t*t*t,easeInOutQuart:t=>t<.5?8*t*t*t*t:1-8*--t*t*t*t,easeInQuint:t=>t*t*t*t*t,easeOutQuint:t=>1+--t*t*t*t*t,easeInOutQuint:t=>t<.5?16*t*t*t*t*t:1+16*--t*t*t*t*t};function gv(){const t=document.createElement("p");t.style.width="100%",t.style.height="200px";const e=document.createElement("div");e.style.position="absolute",e.style.top="0px",e.style.left="0px",e.style.visibility="hidden",e.style.width="200px",e.style.height="150px",e.style.overflow="hidden",e.appendChild(t),document.body.appendChild(e);const n=t.offsetWidth;e.style.overflow="scroll";let r=t.offsetWidth;return n==r&&(r=e.clientWidth),document.body.removeChild(e),n-r}function mv(t,e){let n;Ia(e)||(e=[e]);for(const r of t)if(r){n=r[e[0]];for(let t=1;t<e.length;t++)n&&(n=n[e[t]]);if(void 0!==n)break}return n}const yv={black:"#000000",navy:"#000080",darkblue:"#00008B",mediumblue:"#0000CD",blue:"#0000FF",darkgreen:"#006400",green:"#008000",teal:"#008080",darkcyan:"#008B8B",deepskyblue:"#00BFFF",darkturquoise:"#00CED1",mediumspringgreen:"#00FA9A",lime:"#00FF00",springgreen:"#00FF7F",aqua:"#00FFFF",cyan:"#00FFFF",midnightblue:"#191970",dodgerblue:"#1E90FF",lightseagreen:"#20B2AA",forestgreen:"#228B22",seagreen:"#2E8B57",darkslategray:"#2F4F4F",limegreen:"#32CD32",mediumseagreen:"#3CB371",turquoise:"#40E0D0",royalblue:"#4169E1",steelblue:"#4682B4",darkslateblue:"#483D8B",mediumturquoise:"#48D1CC",indigo:"#4B0082",darkolivegreen:"#556B2F",cadetblue:"#5F9EA0",cornflowerblue:"#6495ED",mediumaquamarine:"#66CDAA",dimgray:"#696969",slateblue:"#6A5ACD",olivedrab:"#6B8E23",slategray:"#708090",lightslategray:"#778899",mediumslateblue:"#7B68EE",lawngreen:"#7CFC00",chartreuse:"#7FFF00",aquamarine:"#7FFFD4",maroon:"#800000",purple:"#800080",olive:"#808000",gray:"#808080",skyblue:"#87CEEB",lightskyblue:"#87CEFA",blueviolet:"#8A2BE2",darkred:"#8B0000",darkmagenta:"#8B008B",saddlebrown:"#8B4513",darkseagreen:"#8FBC8F",lightgreen:"#90EE90",mediumpurple:"#9370D8",darkviolet:"#9400D3",palegreen:"#98FB98",darkorchid:"#9932CC",yellowgreen:"#9ACD32",sienna:"#A0522D",brown:"#A52A2A",darkgray:"#A9A9A9",lightblue:"#ADD8E6",greenyellow:"#ADFF2F",paleturquoise:"#AFEEEE",lightsteelblue:"#B0C4DE",powderblue:"#B0E0E6",firebrick:"#B22222",darkgoldenrod:"#B8860B",mediumorchid:"#BA55D3",rosybrown:"#BC8F8F",darkkhaki:"#BDB76B",silver:"#C0C0C0",mediumvioletred:"#C71585",indianred:"#CD5C5C",peru:"#CD853F",chocolate:"#D2691E",tan:"#D2B48C",lightgrey:"#D3D3D3",palevioletred:"#D87093",thistle:"#D8BFD8",orchid:"#DA70D6",goldenrod:"#DAA520",crimson:"#DC143C",gainsboro:"#DCDCDC",plum:"#DDA0DD",burlywood:"#DEB887",lightcyan:"#E0FFFF",lavender:"#E6E6FA",darksalmon:"#E9967A",violet:"#EE82EE",palegoldenrod:"#EEE8AA",lightcoral:"#F08080",khaki:"#F0E68C",aliceblue:"#F0F8FF",honeydew:"#F0FFF0",azure:"#F0FFFF",sandybrown:"#F4A460",wheat:"#F5DEB3",beige:"#F5F5DC",whitesmoke:"#F5F5F5",mintcream:"#F5FFFA",ghostwhite:"#F8F8FF",salmon:"#FA8072",antiquewhite:"#FAEBD7",linen:"#FAF0E6",lightgoldenrodyellow:"#FAFAD2",oldlace:"#FDF5E6",red:"#FF0000",fuchsia:"#FF00FF",magenta:"#FF00FF",deeppink:"#FF1493",orangered:"#FF4500",tomato:"#FF6347",hotpink:"#FF69B4",coral:"#FF7F50",darkorange:"#FF8C00",lightsalmon:"#FFA07A",orange:"#FFA500",lightpink:"#FFB6C1",pink:"#FFC0CB",gold:"#FFD700",peachpuff:"#FFDAB9",navajowhite:"#FFDEAD",moccasin:"#FFE4B5",bisque:"#FFE4C4",mistyrose:"#FFE4E1",blanchedalmond:"#FFEBCD",papayawhip:"#FFEFD5",lavenderblush:"#FFF0F5",seashell:"#FFF5EE",cornsilk:"#FFF8DC",lemonchiffon:"#FFFACD",floralwhite:"#FFFAF0",snow:"#FFFAFA",yellow:"#FFFF00",lightyellow:"#FFFFE0",ivory:"#FFFFF0",white:"#FFFFFF"};let bv=class{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this.pixelRatio=t,this.generated=!1,this.centerCoordinates={x:144.5,y:144.5},this.r=289*.49,this.color={r:255,g:255,b:255,a:1},this.hueCircle=void 0,this.initialColor={r:255,g:255,b:255,a:1},this.previousColor=void 0,this.applied=!1,this.updateCallback=()=>{},this.closeCallback=()=>{},this._create()}insertTo(t){void 0!==this.hammer&&(this.hammer.destroy(),this.hammer=void 0),this.container=t,this.container.appendChild(this.frame),this._bindHammer(),this._setSize()}setUpdateCallback(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker update callback is not a function.");this.updateCallback=t}setCloseCallback(t){if("function"!=typeof t)throw new Error("Function attempted to set as colorPicker closing callback is not a function.");this.closeCallback=t}_isColorString(t){if("string"==typeof t)return yv[t]}setColor(t){let e,n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if("none"===t)return;const r=this._isColorString(t);if(void 0!==r&&(t=r),!0===kd(t)){if(!0===sv(t)){const n=t.substr(4).substr(0,t.length-5).split(",");e={r:n[0],g:n[1],b:n[2],a:1}}else if(!0===cv(t)){const n=t.substr(5).substr(0,t.length-6).split(",");e={r:n[0],g:n[1],b:n[2],a:n[3]}}else if(!0===av(t)){const n=Qd(t);e={r:n.r,g:n.g,b:n.b,a:1}}}else if(t instanceof Object&&void 0!==t.r&&void 0!==t.g&&void 0!==t.b){const n=void 0!==t.a?t.a:"1.0";e={r:t.r,g:t.g,b:t.b,a:n}}if(void 0===e)throw new Error("Unknown color passed to the colorPicker. Supported are strings: rgb, hex, rgba. Object: rgb ({r:r,g:g,b:b,[a:a]}). Supplied: "+$l(t));this._setColor(e,n)}show(){void 0!==this.closeCallback&&(this.closeCallback(),this.closeCallback=void 0),this.applied=!1,this.frame.style.display="block",this._generateHueCircle()}_hide(){!0===(!(arguments.length>0&&void 0!==arguments[0])||arguments[0])&&(this.previousColor=uh({},this.color)),!0===this.applied&&this.updateCallback(this.initialColor),this.frame.style.display="none",Nh(()=>{void 0!==this.closeCallback&&(this.closeCallback(),this.closeCallback=void 0)},0)}_save(){this.updateCallback(this.color),this.applied=!1,this._hide()}_apply(){this.applied=!0,this.updateCallback(this.color),this._updatePicker(this.color)}_loadLast(){void 0!==this.previousColor?this.setColor(this.previousColor,!1):alert("There is no last color to load...")}_setColor(t){!0===(!(arguments.length>1&&void 0!==arguments[1])||arguments[1])&&(this.initialColor=uh({},t)),this.color=t;const e=Kd(t.r,t.g,t.b),n=2*Math.PI,r=this.r*e.s,i=this.centerCoordinates.x+r*Math.sin(n*e.h),o=this.centerCoordinates.y+r*Math.cos(n*e.h);this.colorPickerSelector.style.left=i-.5*this.colorPickerSelector.clientWidth+"px",this.colorPickerSelector.style.top=o-.5*this.colorPickerSelector.clientHeight+"px",this._updatePicker(t)}_setOpacity(t){this.color.a=t/100,this._updatePicker(this.color)}_setBrightness(t){const e=Kd(this.color.r,this.color.g,this.color.b);e.v=t/100;const n=rv(e.h,e.s,e.v);n.a=this.color.a,this.color=n,this._updatePicker()}_updatePicker(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.color;const e=Kd(t.r,t.g,t.b),n=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(n.webkitBackingStorePixelRatio||n.mozBackingStorePixelRatio||n.msBackingStorePixelRatio||n.oBackingStorePixelRatio||n.backingStorePixelRatio||1)),n.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);const r=this.colorPickerCanvas.clientWidth,i=this.colorPickerCanvas.clientHeight;n.clearRect(0,0,r,i),n.putImageData(this.hueCircle,0,0),n.fillStyle="rgba(0,0,0,"+(1-e.v)+")",n.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),Uh(n).call(n),this.brightnessRange.value=100*e.v,this.opacityRange.value=100*t.a,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}_setSize(){this.colorPickerCanvas.style.width="100%",this.colorPickerCanvas.style.height="100%",this.colorPickerCanvas.width=289*this.pixelRatio,this.colorPickerCanvas.height=289*this.pixelRatio}_create(){var t,e,n,r;if(this.frame=document.createElement("div"),this.frame.className="vis-color-picker",this.colorPickerDiv=document.createElement("div"),this.colorPickerSelector=document.createElement("div"),this.colorPickerSelector.className="vis-selector",this.colorPickerDiv.appendChild(this.colorPickerSelector),this.colorPickerCanvas=document.createElement("canvas"),this.colorPickerDiv.appendChild(this.colorPickerCanvas),this.colorPickerCanvas.getContext){const t=this.colorPickerCanvas.getContext("2d");this.pixelRatio=(window.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1),this.colorPickerCanvas.getContext("2d").setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0)}else{const t=document.createElement("DIV");t.style.color="red",t.style.fontWeight="bold",t.style.padding="10px",t.innerText="Error: your browser does not support HTML canvas",this.colorPickerCanvas.appendChild(t)}this.colorPickerDiv.className="vis-color",this.opacityDiv=document.createElement("div"),this.opacityDiv.className="vis-opacity",this.brightnessDiv=document.createElement("div"),this.brightnessDiv.className="vis-brightness",this.arrowDiv=document.createElement("div"),this.arrowDiv.className="vis-arrow",this.opacityRange=document.createElement("input");try{this.opacityRange.type="range",this.opacityRange.min="0",this.opacityRange.max="100"}catch(t){}this.opacityRange.value="100",this.opacityRange.className="vis-range",this.brightnessRange=document.createElement("input");try{this.brightnessRange.type="range",this.brightnessRange.min="0",this.brightnessRange.max="100"}catch(t){}this.brightnessRange.value="100",this.brightnessRange.className="vis-range",this.opacityDiv.appendChild(this.opacityRange),this.brightnessDiv.appendChild(this.brightnessRange);const i=this;this.opacityRange.onchange=function(){i._setOpacity(this.value)},this.opacityRange.oninput=function(){i._setOpacity(this.value)},this.brightnessRange.onchange=function(){i._setBrightness(this.value)},this.brightnessRange.oninput=function(){i._setBrightness(this.value)},this.brightnessLabel=document.createElement("div"),this.brightnessLabel.className="vis-label vis-brightness",this.brightnessLabel.innerText="brightness:",this.opacityLabel=document.createElement("div"),this.opacityLabel.className="vis-label vis-opacity",this.opacityLabel.innerText="opacity:",this.newColorDiv=document.createElement("div"),this.newColorDiv.className="vis-new-color",this.newColorDiv.innerText="new",this.initialColorDiv=document.createElement("div"),this.initialColorDiv.className="vis-initial-color",this.initialColorDiv.innerText="initial",this.cancelButton=document.createElement("div"),this.cancelButton.className="vis-button vis-cancel",this.cancelButton.innerText="cancel",this.cancelButton.onclick=Xs(t=this._hide).call(t,this,!1),this.applyButton=document.createElement("div"),this.applyButton.className="vis-button vis-apply",this.applyButton.innerText="apply",this.applyButton.onclick=Xs(e=this._apply).call(e,this),this.saveButton=document.createElement("div"),this.saveButton.className="vis-button vis-save",this.saveButton.innerText="save",this.saveButton.onclick=Xs(n=this._save).call(n,this),this.loadButton=document.createElement("div"),this.loadButton.className="vis-button vis-load",this.loadButton.innerText="load last",this.loadButton.onclick=Xs(r=this._loadLast).call(r,this),this.frame.appendChild(this.colorPickerDiv),this.frame.appendChild(this.arrowDiv),this.frame.appendChild(this.brightnessLabel),this.frame.appendChild(this.brightnessDiv),this.frame.appendChild(this.opacityLabel),this.frame.appendChild(this.opacityDiv),this.frame.appendChild(this.newColorDiv),this.frame.appendChild(this.initialColorDiv),this.frame.appendChild(this.cancelButton),this.frame.appendChild(this.applyButton),this.frame.appendChild(this.saveButton),this.frame.appendChild(this.loadButton)}_bindHammer(){this.drag={},this.pinch={},this.hammer=new Cl(this.colorPickerCanvas),this.hammer.get("pinch").set({enable:!0}),this.hammer.on("hammer.input",t=>{t.isFirst&&this._moveSelector(t)}),this.hammer.on("tap",t=>{this._moveSelector(t)}),this.hammer.on("panstart",t=>{this._moveSelector(t)}),this.hammer.on("panmove",t=>{this._moveSelector(t)}),this.hammer.on("panend",t=>{this._moveSelector(t)})}_generateHueCircle(){if(!1===this.generated){const t=this.colorPickerCanvas.getContext("2d");void 0===this.pixelRation&&(this.pixelRatio=(window.devicePixelRatio||1)/(t.webkitBackingStorePixelRatio||t.mozBackingStorePixelRatio||t.msBackingStorePixelRatio||t.oBackingStorePixelRatio||t.backingStorePixelRatio||1)),t.setTransform(this.pixelRatio,0,0,this.pixelRatio,0,0);const e=this.colorPickerCanvas.clientWidth,n=this.colorPickerCanvas.clientHeight;let r,i,o,a;t.clearRect(0,0,e,n),this.centerCoordinates={x:.5*e,y:.5*n},this.r=.49*e;const s=2*Math.PI/360,c=1/360,u=1/this.r;let l;for(o=0;o<360;o++)for(a=0;a<this.r;a++)r=this.centerCoordinates.x+a*Math.sin(s*o),i=this.centerCoordinates.y+a*Math.cos(s*o),l=rv(o*c,a*u,1),t.fillStyle="rgb("+l.r+","+l.g+","+l.b+")",t.fillRect(r-.5,i-.5,2,2);t.strokeStyle="rgba(0,0,0,1)",t.circle(this.centerCoordinates.x,this.centerCoordinates.y,this.r),t.stroke(),this.hueCircle=t.getImageData(0,0,e,n)}this.generated=!0}_moveSelector(t){const e=this.colorPickerDiv.getBoundingClientRect(),n=t.center.x-e.left,r=t.center.y-e.top,i=.5*this.colorPickerDiv.clientHeight,o=.5*this.colorPickerDiv.clientWidth,a=n-o,s=r-i,c=Math.atan2(a,s),u=.98*Math.min(Math.sqrt(a*a+s*s),o),l=Math.cos(c)*u+i,h=Math.sin(c)*u+o;this.colorPickerSelector.style.top=l-.5*this.colorPickerSelector.clientHeight+"px",this.colorPickerSelector.style.left=h-.5*this.colorPickerSelector.clientWidth+"px";let f=c/(2*Math.PI);f=f<0?f+1:f;const p=u/this.r,d=Kd(this.color.r,this.color.g,this.color.b);d.h=f,d.s=p;const v=rv(d.h,d.s,d.v);v.a=this.color.a,this.color=v,this.initialColorDiv.style.backgroundColor="rgba("+this.initialColor.r+","+this.initialColor.g+","+this.initialColor.b+","+this.initialColor.a+")",this.newColorDiv.style.backgroundColor="rgba("+this.color.r+","+this.color.g+","+this.color.b+","+this.color.a+")"}};function wv(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];if(e.length<1)throw new TypeError("Invalid arguments.");if(1===e.length)return document.createTextNode(e[0]);{const t=document.createElement(e[0]);return t.appendChild(wv(...pa(e).call(e,1))),t}}let Cv,kv=!1;const Ev="background: #FFeeee; color: #dd0000";const Ov=kl,Fv=bv,_v=class{constructor(t,e,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:()=>!1;this.parent=t,this.changedOptions=[],this.container=e,this.allowCreation=!1,this.hideOption=i,this.options={},this.initialized=!1,this.popupCounter=0,this.defaultOptions={enabled:!1,filter:!0,container:void 0,showButton:!0},uh(this.options,this.defaultOptions),this.configureOptions=n,this.moduleOptions={},this.domElements=[],this.popupDiv={},this.popupLimit=5,this.popupHistory={},this.colorPicker=new bv(r),this.wrapper=void 0}setOptions(t){if(void 0!==t){this.popupHistory={},this._removePopup();let e=!0;if("string"==typeof t)this.options.filter=t;else if(Ia(t))this.options.filter=t.join();else if("object"==typeof t){if(null==t)throw new TypeError("options cannot be null");void 0!==t.container&&(this.options.container=t.container),void 0!==cp(t)&&(this.options.filter=cp(t)),void 0!==t.showButton&&(this.options.showButton=t.showButton),void 0!==t.enabled&&(e=t.enabled)}else"boolean"==typeof t?(this.options.filter=!0,e=t):"function"==typeof t&&(this.options.filter=t,e=!0);!1===cp(this.options)&&(e=!1),this.options.enabled=e}this._clean()}setModuleOptions(t){this.moduleOptions=t,!0===this.options.enabled&&(this._clean(),void 0!==this.options.container&&(this.container=this.options.container),this._create())}_create(){this._clean(),this.changedOptions=[];const t=cp(this.options);let e=0,n=!1;for(const r in this.configureOptions)Object.prototype.hasOwnProperty.call(this.configureOptions,r)&&(this.allowCreation=!1,n=!1,"function"==typeof t?(n=t(r,[]),n=n||this._handleObject(this.configureOptions[r],[r],!0)):!0!==t&&-1===td(t).call(t,r)||(n=!0),!1!==n&&(this.allowCreation=!0,e>0&&this._makeItem([]),this._makeHeader(r),this._handleObject(this.configureOptions[r],[r])),e++);this._makeButton(),this._push()}_push(){this.wrapper=document.createElement("div"),this.wrapper.className="vis-configuration-wrapper",this.container.appendChild(this.wrapper);for(let t=0;t<this.domElements.length;t++)this.wrapper.appendChild(this.domElements[t]);this._showPopupIfNeeded()}_clean(){for(let t=0;t<this.domElements.length;t++)this.wrapper.removeChild(this.domElements[t]);void 0!==this.wrapper&&(this.container.removeChild(this.wrapper),this.wrapper=void 0),this.domElements=[],this._removePopup()}_getValue(t){let e=this.moduleOptions;for(let n=0;n<t.length;n++){if(void 0===e[t[n]]){e=void 0;break}e=e[t[n]]}return e}_makeItem(t){if(!0===this.allowCreation){const i=document.createElement("div");i.className="vis-configuration vis-config-item vis-config-s"+t.length;for(var e=arguments.length,n=new Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];return cc(n).call(n,t=>{i.appendChild(t)}),this.domElements.push(i),this.domElements.length}return 0}_makeHeader(t){const e=document.createElement("div");e.className="vis-configuration vis-config-header",e.innerText=t,this._makeItem([],e)}_makeLabel(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const r=document.createElement("div");if(r.className="vis-configuration vis-config-label vis-config-s"+e.length,!0===n){for(;r.firstChild;)r.removeChild(r.firstChild);r.appendChild(wv("i","b",t))}else r.innerText=t+":";return r}_makeDropdown(t,e,n){const r=document.createElement("select");r.className="vis-configuration vis-config-select";let i=0;void 0!==e&&-1!==td(t).call(t,e)&&(i=td(t).call(t,e));for(let e=0;e<t.length;e++){const n=document.createElement("option");n.value=t[e],e===i&&(n.selected="selected"),n.innerText=t[e],r.appendChild(n)}const o=this;r.onchange=function(){o._update(this.value,n)};const a=this._makeLabel(n[n.length-1],n);this._makeItem(n,a,r)}_makeRange(t,e,n){const r=t[0],i=t[1],o=t[2],a=t[3],s=document.createElement("input");s.className="vis-configuration vis-config-range";try{s.type="range",s.min=i,s.max=o}catch(t){}s.step=a;let c="",u=0;if(void 0!==e){const t=1.2;e<0&&e*t<i?(s.min=Math.ceil(e*t),u=s.min,c="range increased"):e/t<i&&(s.min=Math.ceil(e/t),u=s.min,c="range increased"),e*t>o&&1!==o&&(s.max=Math.ceil(e*t),u=s.max,c="range increased"),s.value=e}else s.value=r;const l=document.createElement("input");l.className="vis-configuration vis-config-rangeinput",l.value=s.value;const h=this;s.onchange=function(){l.value=this.value,h._update(Number(this.value),n)},s.oninput=function(){l.value=this.value};const f=this._makeLabel(n[n.length-1],n),p=this._makeItem(n,f,s,l);""!==c&&this.popupHistory[p]!==u&&(this.popupHistory[p]=u,this._setupPopup(c,p))}_makeButton(){if(!0===this.options.showButton){const t=document.createElement("div");t.className="vis-configuration vis-config-button",t.innerText="generate options",t.onclick=()=>{this._printOptions()},t.onmouseover=()=>{t.className="vis-configuration vis-config-button hover"},t.onmouseout=()=>{t.className="vis-configuration vis-config-button"},this.optionsContainer=document.createElement("div"),this.optionsContainer.className="vis-configuration vis-config-option-container",this.domElements.push(this.optionsContainer),this.domElements.push(t)}}_setupPopup(t,e){if(!0===this.initialized&&!0===this.allowCreation&&this.popupCounter<this.popupLimit){const n=document.createElement("div");n.id="vis-configuration-popup",n.className="vis-configuration-popup",n.innerText=t,n.onclick=()=>{this._removePopup()},this.popupCounter+=1,this.popupDiv={html:n,index:e}}}_removePopup(){void 0!==this.popupDiv.html&&(this.popupDiv.html.parentNode.removeChild(this.popupDiv.html),clearTimeout(this.popupDiv.hideTimeout),clearTimeout(this.popupDiv.deleteTimeout),this.popupDiv={})}_showPopupIfNeeded(){if(void 0!==this.popupDiv.html){const t=this.domElements[this.popupDiv.index].getBoundingClientRect();this.popupDiv.html.style.left=t.left+"px",this.popupDiv.html.style.top=t.top-30+"px",document.body.appendChild(this.popupDiv.html),this.popupDiv.hideTimeout=Nh(()=>{this.popupDiv.html.style.opacity=0},1500),this.popupDiv.deleteTimeout=Nh(()=>{this._removePopup()},1800)}}_makeCheckbox(t,e,n){const r=document.createElement("input");r.type="checkbox",r.className="vis-configuration vis-config-checkbox",r.checked=t,void 0!==e&&(r.checked=e,e!==t&&("object"==typeof t?e!==t.enabled&&this.changedOptions.push({path:n,value:e}):this.changedOptions.push({path:n,value:e})));const i=this;r.onchange=function(){i._update(this.checked,n)};const o=this._makeLabel(n[n.length-1],n);this._makeItem(n,o,r)}_makeTextInput(t,e,n){const r=document.createElement("input");r.type="text",r.className="vis-configuration vis-config-text",r.value=e,e!==t&&this.changedOptions.push({path:n,value:e});const i=this;r.onchange=function(){i._update(this.value,n)};const o=this._makeLabel(n[n.length-1],n);this._makeItem(n,o,r)}_makeColorField(t,e,n){const r=t[1],i=document.createElement("div");"none"!==(e=void 0===e?r:e)?(i.className="vis-configuration vis-config-colorBlock",i.style.backgroundColor=e):i.className="vis-configuration vis-config-colorBlock none",e=void 0===e?r:e,i.onclick=()=>{this._showColorPicker(e,i,n)};const o=this._makeLabel(n[n.length-1],n);this._makeItem(n,o,i)}_showColorPicker(t,e,n){e.onclick=function(){},this.colorPicker.insertTo(e),this.colorPicker.show(),this.colorPicker.setColor(t),this.colorPicker.setUpdateCallback(t=>{const r="rgba("+t.r+","+t.g+","+t.b+","+t.a+")";e.style.backgroundColor=r,this._update(r,n)}),this.colorPicker.setCloseCallback(()=>{e.onclick=()=>{this._showColorPicker(t,e,n)}})}_handleObject(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=!1;const i=cp(this.options);let o=!1;for(const a in t)if(Object.prototype.hasOwnProperty.call(t,a)){r=!0;const s=t[a],c=Rd(e,a);if("function"==typeof i&&(r=i(a,e),!1===r&&!Ia(s)&&"string"!=typeof s&&"boolean"!=typeof s&&s instanceof Object&&(this.allowCreation=!1,r=this._handleObject(s,c,!0),this.allowCreation=!1===n)),!1!==r){o=!0;const t=this._getValue(c);if(Ia(s))this._handleArray(s,t,c);else if("string"==typeof s)this._makeTextInput(s,t,c);else if("boolean"==typeof s)this._makeCheckbox(s,t,c);else if(s instanceof Object){if(!this.hideOption(e,a,this.moduleOptions))if(void 0!==s.enabled){const t=Rd(c,"enabled"),e=this._getValue(t);if(!0===e){const t=this._makeLabel(a,c,!0);this._makeItem(c,t),o=this._handleObject(s,c)||o}else this._makeCheckbox(s,e,c)}else{const t=this._makeLabel(a,c,!0);this._makeItem(c,t),o=this._handleObject(s,c)||o}}else console.error("dont know how to handle",s,a,c)}}return o}_handleArray(t,e,n){"string"==typeof t[0]&&"color"===t[0]?(this._makeColorField(t,e,n),t[1]!==e&&this.changedOptions.push({path:n,value:e})):"string"==typeof t[0]?(this._makeDropdown(t,e,n),t[0]!==e&&this.changedOptions.push({path:n,value:e})):"number"==typeof t[0]&&(this._makeRange(t,e,n),t[0]!==e&&this.changedOptions.push({path:n,value:Number(e)}))}_update(t,e){const n=this._constructOptions(t,e);this.parent.body&&this.parent.body.emitter&&this.parent.body.emitter.emit&&this.parent.body.emitter.emit("configChange",n),this.initialized=!0,this.parent.setOptions(n)}_constructOptions(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n;t="false"!==(t="true"===t||t)&&t;for(let n=0;n<e.length;n++)"global"!==e[n]&&(void 0===r[e[n]]&&(r[e[n]]={}),n!==e.length-1?r=r[e[n]]:r[e[n]]=t);return n}_printOptions(){const t=this.getOptions();for(;this.optionsContainer.firstChild;)this.optionsContainer.removeChild(this.optionsContainer.firstChild);this.optionsContainer.appendChild(wv("pre","const options = "+$l(t,null,2)))}getOptions(){const t={};for(let e=0;e<this.changedOptions.length;e++)this._constructOptions(this.changedOptions[e].value,this.changedOptions[e].path,t);return t}},Tv=Cl,Sv=class{constructor(t,e){this.container=t,this.overflowMethod=e||"cap",this.x=0,this.y=0,this.padding=5,this.hidden=!1,this.frame=document.createElement("div"),this.frame.className="vis-tooltip",this.container.appendChild(this.frame)}setPosition(t,e){this.x=Hp(t),this.y=Hp(e)}setText(t){if(t instanceof Element){for(;this.frame.firstChild;)this.frame.removeChild(this.frame.firstChild);this.frame.appendChild(t)}else this.frame.innerText=t}show(t){if(void 0===t&&(t=!0),!0===t){const t=this.frame.clientHeight,e=this.frame.clientWidth,n=this.frame.parentNode.clientHeight,r=this.frame.parentNode.clientWidth;let i=0,o=0;if("flip"==this.overflowMethod){let n=!1,a=!0;this.y-t<this.padding&&(a=!1),this.x+e>r-this.padding&&(n=!0),i=n?this.x-e:this.x,o=a?this.y-t:this.y}else o=this.y-t,o+t+this.padding>n&&(o=n-t-this.padding),o<this.padding&&(o=this.padding),i=this.x,i+e+this.padding>r&&(i=r-e-this.padding),i<this.padding&&(i=this.padding);this.frame.style.left=i+"px",this.frame.style.top=o+"px",this.frame.style.visibility="visible",this.hidden=!1}else this.hide()}hide(){this.hidden=!0,this.frame.style.left="0",this.frame.style.top="0",this.frame.style.visibility="hidden"}destroy(){this.frame.parentNode.removeChild(this.frame)}},Pv=Ev,Dv=class t{static validate(e,n,r){kv=!1,Cv=n;let i=n;return void 0!==r&&(i=n[r]),t.parse(e,i,[]),kv}static parse(e,n,r){for(const i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.check(i,e,n,r)}static check(e,n,r,i){if(void 0===r[e]&&void 0===r.__any__)return void t.getSuggestion(e,r,i);let o=e,a=!0;void 0===r[e]&&void 0!==r.__any__&&(o="__any__",a="object"===t.getType(n[e]));let s=r[o];a&&void 0!==s.__type__&&(s=s.__type__),t.checkFields(e,n,r,o,s,i)}static checkFields(e,n,r,i,o,a){const s=function(n){console.error("%c"+n+t.printLocation(a,e),Ev)},c=t.getType(n[e]),u=o[c];void 0!==u?"array"===t.getType(u)&&-1===td(u).call(u,n[e])?(s('Invalid option detected in "'+e+'". Allowed values are:'+t.print(u)+' not "'+n[e]+'". '),kv=!0):"object"===c&&"__any__"!==i&&(a=Rd(a,e),t.parse(n[e],r[i],a)):void 0===o.any&&(s('Invalid type received for "'+e+'". Expected: '+t.print(Za(o))+". Received ["+c+'] "'+n[e]+'"'),kv=!0)}static getType(t){const e=typeof t;return"object"===e?null===t?"null":t instanceof Boolean?"boolean":t instanceof Number?"number":t instanceof String?"string":Ia(t)?"array":t instanceof Date?"date":void 0!==t.nodeType?"dom":!0===t._isAMomentObject?"moment":"object":"number"===e?"number":"boolean"===e?"boolean":"string"===e?"string":void 0===e?"undefined":e}static getSuggestion(e,n,r){const i=t.findInOptions(e,n,r,!1),o=t.findInOptions(e,Cv,[],!0);let a;a=void 0!==i.indexMatch?" in "+t.printLocation(i.path,e,"")+'Perhaps it was incomplete? Did you mean: "'+i.indexMatch+'"?\n\n':o.distance<=4&&i.distance>o.distance?" in "+t.printLocation(i.path,e,"")+"Perhaps it was misplaced? Matching option found at: "+t.printLocation(o.path,o.closestMatch,""):i.distance<=8?'. Did you mean "'+i.closestMatch+'"?'+t.printLocation(i.path,e):". Did you mean one of these: "+t.print(Za(n))+t.printLocation(r,e),console.error('%cUnknown option detected: "'+e+'"'+a,Ev),kv=!0}static findInOptions(e,n,r){let i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],o=1e9,a="",s=[];const c=e.toLowerCase();let u;for(const h in n){let f;if(void 0!==n[h].__type__&&!0===i){const i=t.findInOptions(e,n[h],Rd(r,h));o>i.distance&&(a=i.closestMatch,s=i.path,o=i.distance,u=i.indexMatch)}else{var l;-1!==td(l=h.toLowerCase()).call(l,c)&&(u=h),f=t.levenshteinDistance(e,h),o>f&&(a=h,s=Nd(r),o=f)}}return{closestMatch:a,path:s,distance:o,indexMatch:u}}static printLocation(t,e){let n="\n\n"+(arguments.length>2&&void 0!==arguments[2]?arguments[2]:"Problem value found at: \n")+"options = {\n";for(let e=0;e<t.length;e++){for(let t=0;t<e+1;t++)n+="  ";n+=t[e]+": {\n"}for(let e=0;e<t.length+1;e++)n+="  ";n+=e+"\n";for(let e=0;e<t.length+1;e++){for(let r=0;r<t.length-e;r++)n+="  ";n+="}\n"}return n+"\n\n"}static print(t){return $l(t).replace(/(")|(\[)|(\])|(,"__type__")/g,"").replace(/(,)/g,", ")}static levenshteinDistance(t,e){if(0===t.length)return e.length;if(0===e.length)return t.length;const n=[];let r,i;for(r=0;r<=e.length;r++)n[r]=[r];for(i=0;i<=t.length;i++)n[0][i]=i;for(r=1;r<=e.length;r++)for(i=1;i<=t.length;i++)e.charAt(r-1)==t.charAt(i-1)?n[r][i]=n[r-1][i-1]:n[r][i]=Math.min(n[r-1][i-1]+1,Math.min(n[r][i-1]+1,n[r-1][i]+1));return n[e.length][t.length]}};export{Ov as Activator,gs as Alea,Fv as ColorPicker,_v as Configurator,Ka as DELETE,iv as HSVToHex,rv as HSVToRGB,Tv as Hammer,Sv as Popup,Kd as RGBToHSV,$d as RGBToHex,Pv as VALIDATOR_PRINT_STYLE,Dv as Validator,Ld as addClassName,ev as addCssText,pv as binarySearchCustom,dv as binarySearchValue,lv as bridgeObject,Rd as copyAndExtendArray,Nd as copyArray,xd as deepExtend,es as deepObjectAssign,vv as easingFunctions,Ad as equalArray,Td as extend,_d as fillIfDefined,Wd as forEach,Id as getAbsoluteLeft,Bd as getAbsoluteRight,Md as getAbsoluteTop,gv as getScrollBarWidth,Xd as getTarget,jd as getType,Vd as hasParent,ov as hexToHSV,Qd as hexToRGB,hv as insertSort,Od as isDate,wd as isNumber,Ed as isObject,kd as isString,av as isValidHex,sv as isValidRGB,cv as isValidRGBA,fv as mergeOptions,Gd as option,Jd as overrideOpacity,Zd as parseColor,Yd as preventDefault,ts as pureDeepObjectAssign,Cd as recursiveDOMDelete,zd as removeClassName,nv as removeCssText,uv as selectiveBridgeObject,Pd as selectiveDeepExtend,Sd as selectiveExtend,Dd as selectiveNotDeepExtend,Ud as throttle,Hd as toArray,mv as topMost,qd as updateProperty};
//# sourceMappingURL=vis-util.min.mjs.map
