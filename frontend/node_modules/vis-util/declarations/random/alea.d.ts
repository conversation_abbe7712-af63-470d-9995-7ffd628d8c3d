/**
 * Seedable, fast and reasonably good (not crypto but more than okay for our
 * needs) random number generator.
 * @remarks
 * Adapted from {@link https://web.archive.org/web/20110429100736/http://baagoe.com:80/en/RandomMusings/javascript}.
 * Original algorithm created by <PERSON> \<baagoe\@baagoe.com\> in 2010.
 */
/**
 * Random number generator.
 */
export interface RNG {
    /** Returns \<0, 1). Faster than [[fract53]]. */
    (): number;
    /** Returns \<0, 1). Provides more precise data. */
    fract53(): number;
    /** Returns \<0, 2^32). */
    uint32(): number;
    /** The algorithm gehind this instance. */
    algorithm: string;
    /** The seed used to seed this instance. */
    seed: Mashable[];
    /** The version of this instance. */
    version: string;
}
/**
 * Create a seeded pseudo random generator based on <PERSON><PERSON> by <PERSON>.
 * @param seed - All supplied arguments will be used as a seed. In case nothing
 * is supplied the current time will be used to seed the generator.
 * @returns A ready to use seeded generator.
 */
export declare function Alea(...seed: Mashable[]): RNG;
/**
 * Values of these types can be used as a seed.
 */
export type Mashable = number | string | boolean | object | bigint;
//# sourceMappingURL=alea.d.ts.map