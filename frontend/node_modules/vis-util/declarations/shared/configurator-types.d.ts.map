{"version": 3, "file": "configurator-types.d.ts", "sourceRoot": "", "sources": ["../../src/shared/configurator-types.ts"], "names": [], "mappings": "AAAA,UAAU,WAAW;IACnB,QAAQ,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC;IACrB,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC;IACzB,QAAQ,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC;IAC7B,QAAQ,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC;IACrB,QAAQ,CAAC,QAAQ,CAAC,EAAE,UAAU,CAAC;IAC/B,QAAQ,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC;IAC3B,QAAQ,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC;IAC3B,QAAQ,CAAC,MAAM,CAAC,EAAE,QAAQ,GAAG,SAAS,MAAM,EAAE,CAAC;IAC/C,QAAQ,CAAC,SAAS,CAAC,EAAE,WAAW,CAAC;CAClC;AACD,MAAM,MAAM,aAAa,GAAG;IAE1B,QAAQ,CAAC,QAAQ,EAAE;QACjB,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC;KAC3B,GAAG,WAAW,CAAC;CACjB,GAAG;IACF,QAAQ,EAAE,GAAG,IAAI,MAAM,GAAG,aAAa,GAAG,WAAW;CACtD,CAAC;AAEF;;GAEG;AACH,KAAK,aAAa,GAAG,OAAO,CAAC;AAC7B;;;GAGG;AACH,KAAK,SAAS,GAAG,MAAM,CAAC;AACxB;;;;;;;;;;;;GAYG;AACH,KAAK,WAAW,GAAG,SAAS,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAC7D;;;;GAIG;AACH,KAAK,aAAa,GAAG,SAAS;IAC5B,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC;IACxB,GAAG,CAAC,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,EAAE;CACjC,CAAC;AACF;;;GAGG;AACH,KAAK,UAAU,GAAG,SAAS,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAE5C,KAAK,iBAAiB,GAClB,aAAa,GACb,UAAU,GACV,aAAa,GACb,WAAW,GACX,SAAS,CAAC;AAEd,MAAM,MAAM,kBAAkB,GAAG;IAC/B,QAAQ,EAAE,GAAG,IAAI,MAAM,GAAG,kBAAkB,GAAG,iBAAiB;CACjE,CAAC;AAEF,MAAM,MAAM,sBAAsB,GAAG,CACnC,UAAU,EAAE,SAAS,MAAM,EAAE,EAC7B,UAAU,EAAE,MAAM,EAClB,OAAO,EAAE,GAAG,KACT,OAAO,CAAC"}