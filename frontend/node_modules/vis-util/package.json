{"name": "vis-util", "version": "6.0.0", "description": "utilitie collection for visjs", "browser": "./peer/umd/vis-util.min.cjs", "jsnext": "./esnext/esm/vis-util.mjs", "main": "./peer/umd/vis-util.cjs", "module": "./peer/esm/vis-util.mjs", "types": "./declarations/index.d.ts", "repository": {"type": "git", "url": "https://github.com/visjs/vis-util.git"}, "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "jos <<EMAIL>>", "<PERSON> <<EMAIL>>", "wimrijn<PERSON> <<EMAIL>>", "macleodbroad-wf <<EMAIL>>", "yotamberk <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Preetham G R <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Max <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "type": "module", "scripts": {"build": "run-s build:types build:code build:docs", "build:code": "rollup --config rollup.build.js", "build:docs": "typedoc", "build:types": "tsc -p tsconfig.types.json", "clean": "shx rm -rf \"{coverage,declarations,docs,esnext,peer,reports,standalone}\" \"index.{d.ts,js}\"", "style": "prettier --check .", "style-fix": "prettier --write .", "lint": "eslint --ext .js,.ts .", "lint-fix": "eslint --fix --ext .js,.ts .", "prepublishOnly": "npm run build", "test": "run-s test:unit test:types:check-dts test:types:tsc test:interop", "test:coverage": "cross-env BABEL_ENV=test-cov nyc mocha", "test:interop": "node interop.js", "test:interop:debug": "npm run test:interop -- --fail-command \"$SHELL\"", "test:types:check-dts": "check-dts", "test:types:tsc": "tsc --noemit --project tsconfig.check.json", "test:unit": "cross-env BABEL_ENV=test mocha", "type-check": "run-s test:types:*", "version": "npm run contributors:update && git add package.json", "contributors:update": "git-authors-cli", "preparepublish": "npm run contributors:update", "prepare": "husky"}, "lint-staged": {"*.*": "prettier --write", "*.{js,ts}": "eslint --fix", ".*.*": "prettier --write", ".*.{js,ts}": "eslint --fix"}, "config": {"snap-shot-it": {"sortSnapshots": true, "useRelativePath": true}}, "engines": {"node": ">=8"}, "keywords": ["util", "vis", "vis.js"], "license": "(Apache-2.0 OR MIT)", "bugs": {"url": "https://github.com/visjs/vis-util/issues"}, "homepage": "https://github.com/visjs/vis-util", "exports": {".": {"import": "./peer/esm/vis-util.mjs", "require": "./peer/umd/vis-util.cjs", "types": "./declarations/index.d.ts"}, "./package.json": "./package.json", "./declarations/index.d.ts": {"types": "./declarations/index.d.ts"}, "./standalone": {"import": "./standalone/esm/vis-util.mjs", "require": "./standalone/umd/vis-util.cjs", "types": "./declarations/index.d.ts"}, "./standalone/esm/vis-util.js": {"import": "./standalone/esm/vis-util.mjs", "types": "./declarations/index.d.ts"}, "./standalone/esm/vis-util.mjs": {"import": "./standalone/esm/vis-util.mjs", "types": "./declarations/index.d.ts"}, "./standalone/esm/vis-util.min.js": {"import": "./standalone/esm/vis-util.min.mjs", "types": "./declarations/index.d.ts"}, "./standalone/esm/vis-util.min.mjs": {"import": "./standalone/esm/vis-util.min.mjs", "types": "./declarations/index.d.ts"}, "./standalone/umd/vis-util.js": {"require": "./standalone/umd/vis-util.cjs", "types": "./declarations/index.d.ts"}, "./standalone/umd/vis-util.cjs": {"require": "./standalone/umd/vis-util.cjs", "types": "./declarations/index.d.ts"}, "./standalone/umd/vis-util.min.js": {"require": "./standalone/umd/vis-util.min.cjs", "types": "./declarations/index.d.ts"}, "./standalone/umd/vis-util.min.cjs": {"require": "./standalone/umd/vis-util.min.cjs", "types": "./declarations/index.d.ts"}, "./peer": {"import": "./peer/esm/vis-util.mjs", "require": "./peer/umd/vis-util.cjs", "types": "./declarations/index.d.ts"}, "./peer/esm/vis-util.js": {"import": "./peer/esm/vis-util.mjs", "types": "./declarations/index.d.ts"}, "./peer/esm/vis-util.mjs": {"import": "./peer/esm/vis-util.mjs", "types": "./declarations/index.d.ts"}, "./peer/esm/vis-util.min.js": {"import": "./peer/esm/vis-util.min.mjs", "types": "./declarations/index.d.ts"}, "./peer/esm/vis-util.min.mjs": {"import": "./peer/esm/vis-util.min.mjs", "types": "./declarations/index.d.ts"}, "./peer/umd/vis-util.js": {"require": "./peer/umd/vis-util.cjs", "types": "./declarations/index.d.ts"}, "./peer/umd/vis-util.cjs": {"require": "./peer/umd/vis-util.cjs", "types": "./declarations/index.d.ts"}, "./peer/umd/vis-util.min.js": {"require": "./peer/umd/vis-util.min.cjs", "types": "./declarations/index.d.ts"}, "./peer/umd/vis-util.min.cjs": {"require": "./peer/umd/vis-util.min.cjs", "types": "./declarations/index.d.ts"}, "./esnext": {"import": "./esnext/esm/vis-util.mjs", "require": "./esnext/umd/vis-util.cjs", "types": "./declarations/index.d.ts"}, "./esnext/esm/vis-util.js": {"import": "./esnext/esm/vis-util.mjs", "types": "./declarations/index.d.ts"}, "./esnext/esm/vis-util.mjs": {"import": "./esnext/esm/vis-util.mjs", "types": "./declarations/index.d.ts"}, "./esnext/esm/vis-util.min.js": {"import": "./esnext/esm/vis-util.min.mjs", "types": "./declarations/index.d.ts"}, "./esnext/esm/vis-util.min.mjs": {"import": "./esnext/esm/vis-util.min.mjs", "types": "./declarations/index.d.ts"}, "./esnext/umd/vis-util.js": {"require": "./esnext/umd/vis-util.cjs", "types": "./declarations/index.d.ts"}, "./esnext/umd/vis-util.cjs": {"require": "./esnext/umd/vis-util.cjs", "types": "./declarations/index.d.ts"}, "./esnext/umd/vis-util.min.js": {"require": "./esnext/umd/vis-util.min.cjs", "types": "./declarations/index.d.ts"}, "./esnext/umd/vis-util.min.cjs": {"require": "./esnext/umd/vis-util.min.cjs", "types": "./declarations/index.d.ts"}, "./standalone/styles/*": "./standalone/styles/*", "./peer/styles/*": "./peer/styles/*", "./esnext/styles/*": "./esnext/styles/*"}, "files": ["LICENSE*", "standalone", "peer", "esnext", "declarations"], "funding": {"type": "opencollective", "url": "https://opencollective.com/visjs"}, "volta": {"node": "24.3.0", "npm": "11.4.2", "pnpm": "10.12.4"}, "peerDependencies": {"@egjs/hammerjs": "^2.0.0", "component-emitter": "^1.3.0 || ^2.0.0"}, "devDependencies": {"@egjs/hammerjs": "2.0.17", "@types/chai": "5.2.2", "@types/jsdom-global": "3.0.7", "@types/mocha": "10.0.10", "@types/node": "24.0.3", "@types/sinon": "17.0.4", "assert": "2.1.0", "check-dts": "0.9.0", "component-emitter": "2.0.0", "cross-env": "7.0.3", "eslint": "8.55.0", "git-authors-cli": "1.0.52", "husky": "9.1.7", "jsdom": "26.1.0", "jsdom-global": "3.0.2", "lint-staged": "16.1.2", "mocha": "11.7.0", "npm-run-all": "4.1.5", "nyc": "17.1.0", "sazerac": "2.0.0", "semantic-release": "24.2.5", "shx": "^0.4.0", "sinon": "21.0.0", "snap-shot-it": "7.9.10", "typedoc": "0.28.5", "vis-dev-utils": "5.0.0"}}