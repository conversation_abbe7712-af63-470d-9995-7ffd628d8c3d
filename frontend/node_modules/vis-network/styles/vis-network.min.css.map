{"version": 3, "sources": ["ManipulationSystem.css", "NavigationHandler.css", "activator.css", "bootstrap.css", "color-picker.css", "configurator.css", "popup.css"], "names": [], "mappings": "AAAA,qCAOE,eAAmB,CACnB,gFAMC,CACD,oJAQC,CACD,4EAMC,CACD,8EAMC,CACD,+EAMC,CACD,yEAMC,CA7CD,sBAAqB,CAArB,iBAAqB,CALrB,sBAAuB,CAmDvB,+GAAmH,CAOnH,WAAY,CAHZ,MAAO,CAFP,eAAgB,CAChB,iBAAkB,CAElB,KAAM,CACN,UAEF,CAEA,uEAKE,WAAY,CAFZ,MAAO,CADP,iBAAkB,CAElB,OAEF,CAIA,iCAaE,0BAA2B,CAN3B,4BAA6B,CAG7B,k4vBAAqC,CAFrC,4BAA6B,CAC7B,2BAA4B,CAE5B,WAAY,CACZ,cAAe,CAPf,WAAY,CAJZ,iBAAkB,CAClB,OAAQ,CACR,KAAM,CAWN,wBAAyB,CACzB,uBAAwB,CACxB,qBAAsB,CACtB,oBAAqB,CACrB,gBAAiB,CAdjB,UAeF,CAEA,uCACE,UACF,CAEA,2GAgBE,0BAA2B,CAP3B,4BAA6B,CAC7B,uBAA4B,CAC5B,2BAA4B,CAN5B,WAAY,CAEZ,uBAAwB,CACxB,kBAAmB,CAFnB,sBAAuB,CAQvB,cAAe,CAZf,UAAW,CACX,mBAAoB,CACpB,cAAe,CAQf,WAAY,CACZ,gBAAiB,CAEjB,aAAwB,CAExB,wBAAyB,CACzB,uBAAwB,CACxB,qBAAsB,CACtB,oBAAqB,CACrB,gBACF,CAEA,6DACE,qCACF,CAEA,8DACE,qCACF,CAEA,gEACE,so2BACF,CAEA,wDACE,kCAAwC,CACxC,cACF,CACA,yDACE,kCACF,CACA,kDAEE,gBAAiB,CADjB,SAEF,CACA,sDAEE,eAAiB,CADjB,UAEF,CAEA,+DACE,842BACF,CAEA,6HAEE,s62BACF,CAEA,2EACE,wBAAyB,CACzB,qBACF,CAEA,mEACE,sl2BACF,CAEA,kEACE,s32BACF,CAEA,mGAGE,gBAAiB,CADjB,iBAEF,CACA,4DAKE,wBAAyB,CAHzB,oBAAqB,CADrB,UAAW,CAGX,WAAY,CAEZ,mBAAsB,CAHtB,SAIF,CCvLA,kDAUE,0BAA2B,CAH3B,2BAA4B,CAC5B,2BAA4B,CAL5B,uBAAwB,CACxB,kBAAmB,CAKnB,cAAe,CAHf,oBAAqB,CAJrB,WAAY,CAGZ,iBAAkB,CAMlB,wBAAyB,CACzB,uBAAwB,CACxB,qBAAsB,CACtB,oBAAqB,CACrB,gBAAiB,CAdjB,UAeF,CAEA,wDACE,yCACF,CAEA,yDACE,0CACF,CAEA,yDACE,02LAAuC,CACvC,WAAY,CACZ,SACF,CACA,2DACE,02LAAyC,CACzC,WAAY,CACZ,SACF,CACA,2DACE,08LAAyC,CACzC,WAAY,CACZ,SACF,CACA,4DACE,k7LAA0C,CAC1C,WAAY,CACZ,SACF,CACA,6DACE,0sLAAoC,CACpC,WAAY,CACZ,UACF,CACA,8DACE,08KAAqC,CACrC,WAAY,CACZ,UACF,CACA,kEACE,82LAA2C,CAC3C,WAAY,CACZ,UACF,CC5DA,aAIE,QAAW,CACX,MAAS,CAJT,iBAAkB,CAElB,OAAU,CADV,KAAQ,CAMR,UACF,CAEA,YACE,2BACF,CCXA,mBACE,YAAa,CACb,UACF,CCLA,qBAWE,qBAAyB,CADzB,kBAAmB,CAGnB,oCAA+C,CAD/C,YAAa,CALb,YAAa,CAJb,SAAU,CAEV,gBAAiB,CADjB,iBAAkB,CAKlB,YAAa,CARb,iBAAkB,CAClB,KAAQ,CAIR,WAAY,CAEZ,SAMF,CAEA,mCAGE,QAAS,CAFT,iBAAkB,CAClB,SAEF,CAEA,mFAIE,wBAAyB,CACzB,WAAY,CACZ,QAAS,CAGT,mBAAoB,CADpB,iBAAkB,CANlB,UAAW,CACX,OAAQ,CAIR,OAGF,CAEA,yCAEE,uEAA2B,CAC3B,iBAAkB,CAClB,gBACF,CAEA,mCAIE,cAAe,CADf,YAAa,CAFb,iBAAkB,CAClB,WAGF,CAEA,wCACE,iBAAkB,CAClB,SACF,CAEA,qCACE,iBAAkB,CAClB,SACF,CAEA,sCAQE,kBAAmB,CACnB,kJAYC,CACD,8RAcC,CACD,8IAYC,CACD,gJAYC,CACD,iJAYC,CACD,2IAYC,CAjFD,qBAAyB,CADzB,kBAAmB,CAmFnB,+GAAmH,CApFnH,WAAY,CAFZ,UAAW,CAFX,iBAAkB,CAClB,SAAU,CAEV,UAsFF,CAEA,uCAOE,UAAW,CAEX,iBAAkB,CADlB,gBAMF,CAEA,kFAZE,+BAAoC,CACpC,iBAAkB,CAMlB,oBAAyB,CADzB,cAAe,CAPf,WAAY,CAUZ,gBAAiB,CAZjB,iBAAkB,CAKlB,SAAU,CAMV,qBAAsB,CAVtB,WA4BF,CAdA,2CAOE,SAAU,CAEV,gBAAiB,CADjB,eAMF,CAEA,mCAGE,SAAU,CAFV,iBAAkB,CAClB,WAEF,CAEA,kDACE,SACF,CAEA,+CACE,SACF,CAEA,oCAUE,wBAAyB,CADzB,wBAAyB,CALzB,kBAAmB,CAOnB,cAAe,CARf,WAAY,CAIZ,gBAAiB,CANjB,iBAAkB,CAKlB,iBAAkB,CAElB,SAAU,CAHV,qBAAsB,CAHtB,UAUF,CAEA,+CAGE,QACF,CACA,6CAGE,SACF,CACA,8CAGE,UACF,CACA,6CAGE,UACF,CAEA,qCAEE,WAAY,CADZ,WAEF,CC1OA,sBAEE,aAAc,CACd,UAAW,CACX,cAAe,CAHf,iBAIF,CAEA,8BACE,aAAc,CACd,WACF,CAEA,oCACE,UAAW,CACX,UAAW,CACX,aACF,CAEA,kDAGE,qBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAJlB,aAAc,CAMd,SAAU,CADV,eAAgB,CAEhB,gBAAiB,CANjB,WAOF,CAEA,wCAME,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAIlB,cAAe,CAXf,aAAc,CAEd,WAAY,CAOZ,SAAU,CALV,gBAAiB,CAQjB,kBAAmB,CAJnB,eAAgB,CAEhB,gBAAiB,CAPjB,qBAAsB,CAFtB,WAYF,CAEA,8CACE,wBAAyB,CACzB,wBAAyB,CACzB,UACF,CAEA,sCACE,aAAc,CACd,UAAW,CAEX,WAAY,CAEZ,gBAAiB,CADjB,qBAAsB,CAFtB,WAIF,CAEA,oDAEE,wBAAyB,CAEzB,iBAAkB,CAHlB,SAAU,CAEV,gBAEF,CACA,oDAEE,wBAAyB,CAEzB,iBAAkB,CAHlB,SAAU,CAEV,gBAEF,CACA,oDAEE,wBAAyB,CAEzB,iBAAkB,CAHlB,SAAU,CAEV,gBAEF,CAEA,wCACE,cAAe,CACf,eACF,CAEA,uCAEE,WAAY,CACZ,gBAAiB,CAFjB,WAGF,CAEA,qDACE,WACF,CACA,qDACE,WACF,CAEA,4CAIE,qBAAyB,CACzB,iBAAkB,CAGlB,cAAe,CALf,WAAY,CAIZ,QAAW,CADX,SAAY,CALZ,OAAQ,CACR,UAOF,CAEA,4CACE,SACF,CAEA,8CAME,QAAS,CADT,WAAY,CAEZ,mBAAoB,CANpB,iBAAkB,CAClB,QAAS,CACT,UAKF,CAEA,yCAEE,uBAAwB,CAIxB,4BAAkC,CADlC,mBAAuB,CAKvB,WAAY,CADZ,WAEF,CACA,wEAGE,kBAAmB,CACnB,0DAA8D,CAC9D,sGAMC,CACD,2DAIC,CACD,wDAIC,CACD,yDAA6D,CAC7D,wDAA+D,CAG/D,qBAAyB,CAEzB,iBAAkB,CADlB,yBAAmC,CAHnC,+GAAmH,CAtBnH,UAAW,CADX,WA4BF,CACA,+DACE,uBAAwB,CAKxB,kBAAmB,CACnB,2DAA+D,CAC/D,uGAMC,CACD,uDAIC,CACD,yDAIC,CACD,0DAA8D,CAC9D,oDAAgE,CAxBhE,wBAAyB,CAGzB,iBAAkB,CAuBlB,4BAAmC,CADnC,+GAAmH,CAxBnH,WAAY,CA0BZ,eAAgB,CAzBhB,UA0BF,CACA,+CACE,YACF,CACA,8EACE,kBAAmB,CACnB,0DAA8D,CAC9D,sGAMC,CACD,2DAIC,CACD,wDAIC,CACD,yDAA6D,CAC7D,wDAA+D,CAC/D,+GACF,CAEA,2DAGE,kBAAmB,CACnB,0DAA8D,CAC9D,sGAMC,CACD,2DAIC,CACD,wDAIC,CACD,yDAA6D,CAC7D,wDAA+D,CAG/D,qBAAyB,CAEzB,iBAAkB,CADlB,yBAAmC,CAHnC,+GAAmH,CAtBnH,WAAY,CADZ,WA4BF,CACA,2DAME,kBAAmB,CALnB,WAAY,CAIZ,iBAAkB,CAHlB,WAAY,CACZ,UAIF,CAGA,wDACE,sBAAwB,CACxB,mBACF,CAEA,oDAKE,sBAAuB,CAGvB,wBAAyB,CACzB,kBAAmB,CAGnB,iBAAkB,CAVlB,UAAW,CADX,WAYF,CACA,yDACE,eAAgB,CAChB,kBACF,CACA,yDACE,eAAgB,CAChB,kBACF,CACA,oDAKE,kBAAmB,CAJnB,WAAY,CAGZ,iBAAkB,CAFlB,WAAY,CACZ,UAGF,CACA,+DACE,eACF,CACA,+DACE,eACF,CAEA,yBAEE,6BAAkC,CAClC,wBAAyB,CAOzB,iBAAkB,CAFlB,UAAc,CACd,cAAe,CAJf,WAAY,CADZ,gBAAiB,CAHjB,iBAAkB,CAMlB,iBAAkB,CAIlB,0CAA4C,CAC5C,uCAAyC,CACzC,kCAAoC,CAPpC,WAQF,CACA,+DAIE,wBAAyB,CACzB,WAAY,CACZ,QAAS,CAJT,SAAU,CAOV,mBAAoB,CADpB,iBAAkB,CALlB,OAAQ,CAIR,OAGF,CAEA,+BAEE,2FAAyC,CACzC,gBAAiB,CACjB,eACF,CACA,gCAEE,gFAA0B,CAC1B,iBAAkB,CAClB,gBACF,CCtVA,gBASE,wBAAyB,CAKzB,wBAAyB,CAHzB,sBAAuB,CACvB,yBAA0B,CAC1B,iBAAkB,CAGlB,sCAA2C,CAR3C,UAAc,CAFd,mBAAoB,CACpB,cAAe,CAJf,WAAY,CAcZ,mBAAoB,CAhBpB,iBAAkB,CAClB,iBAAkB,CAElB,kBAAmB,CAenB,SACF", "file": "vis-network.min.css", "sourcesContent": ["div.vis-network div.vis-manipulation {\n  box-sizing: content-box;\n\n  border-width: 0;\n  border-bottom: 1px;\n  border-style: solid;\n  border-color: #d6d9d8;\n  background: #ffffff; /* Old browsers */\n  background: -moz-linear-gradient(\n    top,\n    #ffffff 0%,\n    #fcfcfc 48%,\n    #fafafa 50%,\n    #fcfcfc 100%\n  ); /* FF3.6+ */\n  background: -webkit-gradient(\n    linear,\n    left top,\n    left bottom,\n    color-stop(0%, #ffffff),\n    color-stop(48%, #fcfcfc),\n    color-stop(50%, #fafafa),\n    color-stop(100%, #fcfcfc)\n  ); /* Chrome,Safari4+ */\n  background: -webkit-linear-gradient(\n    top,\n    #ffffff 0%,\n    #fcfcfc 48%,\n    #fafafa 50%,\n    #fcfcfc 100%\n  ); /* Chrome10+,Safari5.1+ */\n  background: -o-linear-gradient(\n    top,\n    #ffffff 0%,\n    #fcfcfc 48%,\n    #fafafa 50%,\n    #fcfcfc 100%\n  ); /* Opera 11.10+ */\n  background: -ms-linear-gradient(\n    top,\n    #ffffff 0%,\n    #fcfcfc 48%,\n    #fafafa 50%,\n    #fcfcfc 100%\n  ); /* IE10+ */\n  background: linear-gradient(\n    to bottom,\n    #ffffff 0%,\n    #fcfcfc 48%,\n    #fafafa 50%,\n    #fcfcfc 100%\n  ); /* W3C */\n  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#fcfcfc',GradientType=0 ); /* IE6-9 */\n\n  padding-top: 4px;\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 28px;\n}\n\ndiv.vis-network div.vis-edit-mode,\ndiv.vis-network button.vis-edit-mode {\n  position: absolute;\n  left: 0;\n  top: 5px;\n  height: 30px;\n}\n\n/* FIXME: shouldn't the vis-close button be a child of the vis-manipulation div? */\n\ndiv.vis-network button.vis-close {\n  position: absolute;\n  right: 0;\n  top: 0;\n  width: 30px;\n  height: 30px;\n\n  background-color: transparent;\n  background-position: 20px 3px;\n  background-repeat: no-repeat;\n  background-image: inline(\"cross.png\");\n  border: none;\n  cursor: pointer;\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\ndiv.vis-network button.vis-close:hover {\n  opacity: 0.6;\n}\n\ndiv.vis-network div.vis-manipulation button.vis-button,\ndiv.vis-network div.vis-edit-mode button.vis-button {\n  float: left;\n  font-family: verdana;\n  font-size: 12px;\n  border: none;\n  box-sizing: content-box;\n  -moz-border-radius: 15px;\n  border-radius: 15px;\n  background-color: transparent;\n  background-position: 0px 0px;\n  background-repeat: no-repeat;\n  height: 24px;\n  margin-left: 10px;\n  cursor: pointer;\n  padding: 0px 8px 0px 8px;\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\ndiv.vis-network div.vis-manipulation button.vis-button:hover {\n  box-shadow: 1px 1px 8px rgba(0, 0, 0, 0.2);\n}\n\ndiv.vis-network div.vis-manipulation button.vis-button:active {\n  box-shadow: 1px 1px 8px rgba(0, 0, 0, 0.5);\n}\n\ndiv.vis-network div.vis-manipulation button.vis-button.vis-back {\n  background-image: inline(\"backIcon.png\");\n}\n\ndiv.vis-network div.vis-manipulation div.vis-none:hover {\n  box-shadow: 1px 1px 8px rgba(0, 0, 0, 0);\n  cursor: default;\n}\ndiv.vis-network div.vis-manipulation div.vis-none:active {\n  box-shadow: 1px 1px 8px rgba(0, 0, 0, 0);\n}\ndiv.vis-network div.vis-manipulation div.vis-none {\n  padding: 0px;\n  line-height: 23px;\n}\ndiv.vis-network div.vis-manipulation div.notification {\n  margin: 2px;\n  font-weight: bold;\n}\n\ndiv.vis-network div.vis-manipulation button.vis-button.vis-add {\n  background-image: inline(\"addNodeIcon.png\");\n}\n\ndiv.vis-network div.vis-manipulation button.vis-button.vis-edit,\ndiv.vis-network div.vis-edit-mode button.vis-button.vis-edit {\n  background-image: inline(\"editIcon.png\");\n}\n\ndiv.vis-network div.vis-edit-mode button.vis-button.vis-edit.vis-edit-mode {\n  background-color: #fcfcfc;\n  border: 1px solid #cccccc;\n}\n\ndiv.vis-network div.vis-manipulation button.vis-button.vis-connect {\n  background-image: inline(\"connectIcon.png\");\n}\n\ndiv.vis-network div.vis-manipulation button.vis-button.vis-delete {\n  background-image: inline(\"deleteIcon.png\");\n}\n/* top right bottom left */\ndiv.vis-network div.vis-manipulation div.vis-label,\ndiv.vis-network div.vis-edit-mode div.vis-label {\n  margin: 0 0 0 23px;\n  line-height: 25px;\n}\ndiv.vis-network div.vis-manipulation div.vis-separator-line {\n  float: left;\n  display: inline-block;\n  width: 1px;\n  height: 21px;\n  background-color: #bdbdbd;\n  margin: 0px 7px 0 15px; /*top right bottom left*/\n}\n\n/* TODO: is this redundant?\ndiv.network-navigation_wrapper {\n  position: absolute;\n  left: 0;\n  top: 0;\n  width: 100%;\n  height: 100%;\n}\n*/\n", "div.vis-network div.vis-navigation div.vis-button {\n  width: 34px;\n  height: 34px;\n  -moz-border-radius: 17px;\n  border-radius: 17px;\n  position: absolute;\n  display: inline-block;\n  background-position: 2px 2px;\n  background-repeat: no-repeat;\n  cursor: pointer;\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n}\n\ndiv.vis-network div.vis-navigation div.vis-button:hover {\n  box-shadow: 0 0 3px 3px rgba(56, 207, 21, 0.3);\n}\n\ndiv.vis-network div.vis-navigation div.vis-button:active {\n  box-shadow: 0 0 1px 3px rgba(56, 207, 21, 0.95);\n}\n\ndiv.vis-network div.vis-navigation div.vis-button.vis-up {\n  background-image: inline(\"upArrow.png\");\n  bottom: 50px;\n  left: 55px;\n}\ndiv.vis-network div.vis-navigation div.vis-button.vis-down {\n  background-image: inline(\"downArrow.png\");\n  bottom: 10px;\n  left: 55px;\n}\ndiv.vis-network div.vis-navigation div.vis-button.vis-left {\n  background-image: inline(\"leftArrow.png\");\n  bottom: 10px;\n  left: 15px;\n}\ndiv.vis-network div.vis-navigation div.vis-button.vis-right {\n  background-image: inline(\"rightArrow.png\");\n  bottom: 10px;\n  left: 95px;\n}\ndiv.vis-network div.vis-navigation div.vis-button.vis-zoomIn {\n  background-image: inline(\"plus.png\");\n  bottom: 10px;\n  right: 15px;\n}\ndiv.vis-network div.vis-navigation div.vis-button.vis-zoomOut {\n  background-image: inline(\"minus.png\");\n  bottom: 10px;\n  right: 55px;\n}\ndiv.vis-network div.vis-navigation div.vis-button.vis-zoomExtends {\n  background-image: inline(\"zoomExtends.png\");\n  bottom: 50px;\n  right: 15px;\n}\n", ".vis-overlay {\n  position: absolute;\n  top: 0px;\n  right: 0px;\n  bottom: 0px;\n  left: 0px;\n\n  /* Must be displayed above for example selected Timeline items */\n  z-index: 10;\n}\n\n.vis-active {\n  box-shadow: 0 0 10px #86d5f8;\n}\n", "/* override some bootstrap styles screwing up the timelines css */\n\n.vis [class*=\"span\"] {\n  min-height: 0;\n  width: auto;\n}\n", "div.vis-color-picker {\n  position: absolute;\n  top: 0px;\n  left: 30px;\n  margin-top: -140px;\n  margin-left: 30px;\n  width: 310px;\n  height: 444px;\n  z-index: 1;\n  padding: 10px;\n  border-radius: 15px;\n  background-color: #ffffff;\n  display: none;\n  box-shadow: rgba(0, 0, 0, 0.5) 0px 0px 10px 0px;\n}\n\ndiv.vis-color-picker div.vis-arrow {\n  position: absolute;\n  top: 147px;\n  left: 5px;\n}\n\ndiv.vis-color-picker div.vis-arrow::after,\ndiv.vis-color-picker div.vis-arrow::before {\n  right: 100%;\n  top: 50%;\n  border: solid transparent;\n  content: \" \";\n  height: 0;\n  width: 0;\n  position: absolute;\n  pointer-events: none;\n}\n\ndiv.vis-color-picker div.vis-arrow:after {\n  border-color: rgba(255, 255, 255, 0);\n  border-right-color: #ffffff;\n  border-width: 30px;\n  margin-top: -30px;\n}\n\ndiv.vis-color-picker div.vis-color {\n  position: absolute;\n  width: 289px;\n  height: 289px;\n  cursor: pointer;\n}\n\ndiv.vis-color-picker div.vis-brightness {\n  position: absolute;\n  top: 313px;\n}\n\ndiv.vis-color-picker div.vis-opacity {\n  position: absolute;\n  top: 350px;\n}\n\ndiv.vis-color-picker div.vis-selector {\n  position: absolute;\n  top: 137px;\n  left: 137px;\n  width: 15px;\n  height: 15px;\n  border-radius: 15px;\n  border: 1px solid #ffffff;\n  background: #4c4c4c; /* Old browsers */\n  background: -moz-linear-gradient(\n    top,\n    #4c4c4c 0%,\n    #595959 12%,\n    #666666 25%,\n    #474747 39%,\n    #2c2c2c 50%,\n    #000000 51%,\n    #111111 60%,\n    #2b2b2b 76%,\n    #1c1c1c 91%,\n    #131313 100%\n  ); /* FF3.6+ */\n  background: -webkit-gradient(\n    linear,\n    left top,\n    left bottom,\n    color-stop(0%, #4c4c4c),\n    color-stop(12%, #595959),\n    color-stop(25%, #666666),\n    color-stop(39%, #474747),\n    color-stop(50%, #2c2c2c),\n    color-stop(51%, #000000),\n    color-stop(60%, #111111),\n    color-stop(76%, #2b2b2b),\n    color-stop(91%, #1c1c1c),\n    color-stop(100%, #131313)\n  ); /* Chrome,Safari4+ */\n  background: -webkit-linear-gradient(\n    top,\n    #4c4c4c 0%,\n    #595959 12%,\n    #666666 25%,\n    #474747 39%,\n    #2c2c2c 50%,\n    #000000 51%,\n    #111111 60%,\n    #2b2b2b 76%,\n    #1c1c1c 91%,\n    #131313 100%\n  ); /* Chrome10+,Safari5.1+ */\n  background: -o-linear-gradient(\n    top,\n    #4c4c4c 0%,\n    #595959 12%,\n    #666666 25%,\n    #474747 39%,\n    #2c2c2c 50%,\n    #000000 51%,\n    #111111 60%,\n    #2b2b2b 76%,\n    #1c1c1c 91%,\n    #131313 100%\n  ); /* Opera 11.10+ */\n  background: -ms-linear-gradient(\n    top,\n    #4c4c4c 0%,\n    #595959 12%,\n    #666666 25%,\n    #474747 39%,\n    #2c2c2c 50%,\n    #000000 51%,\n    #111111 60%,\n    #2b2b2b 76%,\n    #1c1c1c 91%,\n    #131313 100%\n  ); /* IE10+ */\n  background: linear-gradient(\n    to bottom,\n    #4c4c4c 0%,\n    #595959 12%,\n    #666666 25%,\n    #474747 39%,\n    #2c2c2c 50%,\n    #000000 51%,\n    #111111 60%,\n    #2b2b2b 76%,\n    #1c1c1c 91%,\n    #131313 100%\n  ); /* W3C */\n  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#4c4c4c', endColorstr='#131313',GradientType=0 ); /* IE6-9 */\n}\n\ndiv.vis-color-picker div.vis-new-color {\n  position: absolute;\n  width: 140px;\n  height: 20px;\n  border: 1px solid rgba(0, 0, 0, 0.1);\n  border-radius: 5px;\n  top: 380px;\n  left: 159px;\n  text-align: right;\n  padding-right: 2px;\n  font-size: 10px;\n  color: rgba(0, 0, 0, 0.4);\n  vertical-align: middle;\n  line-height: 20px;\n}\n\ndiv.vis-color-picker div.vis-initial-color {\n  position: absolute;\n  width: 140px;\n  height: 20px;\n  border: 1px solid rgba(0, 0, 0, 0.1);\n  border-radius: 5px;\n  top: 380px;\n  left: 10px;\n  text-align: left;\n  padding-left: 2px;\n  font-size: 10px;\n  color: rgba(0, 0, 0, 0.4);\n  vertical-align: middle;\n  line-height: 20px;\n}\n\ndiv.vis-color-picker div.vis-label {\n  position: absolute;\n  width: 300px;\n  left: 10px;\n}\n\ndiv.vis-color-picker div.vis-label.vis-brightness {\n  top: 300px;\n}\n\ndiv.vis-color-picker div.vis-label.vis-opacity {\n  top: 338px;\n}\n\ndiv.vis-color-picker div.vis-button {\n  position: absolute;\n  width: 68px;\n  height: 25px;\n  border-radius: 10px;\n  vertical-align: middle;\n  text-align: center;\n  line-height: 25px;\n  top: 410px;\n  border: 2px solid #d9d9d9;\n  background-color: #f7f7f7;\n  cursor: pointer;\n}\n\ndiv.vis-color-picker div.vis-button.vis-cancel {\n  /*border:2px solid #ff4e33;*/\n  /*background-color: #ff7761;*/\n  left: 5px;\n}\ndiv.vis-color-picker div.vis-button.vis-load {\n  /*border:2px solid #a153e6;*/\n  /*background-color: #cb8dff;*/\n  left: 82px;\n}\ndiv.vis-color-picker div.vis-button.vis-apply {\n  /*border:2px solid #4588e6;*/\n  /*background-color: #82b6ff;*/\n  left: 159px;\n}\ndiv.vis-color-picker div.vis-button.vis-save {\n  /*border:2px solid #45e655;*/\n  /*background-color: #6dff7c;*/\n  left: 236px;\n}\n\ndiv.vis-color-picker input.vis-range {\n  width: 290px;\n  height: 20px;\n}\n\n/* TODO: is this redundant?\ndiv.vis-color-picker input.vis-range-brightness {\n  width: 289px !important;\n}\n\n\ndiv.vis-color-picker input.vis-saturation-range {\n  width: 289px !important;\n}*/\n", "div.vis-configuration {\n  position: relative;\n  display: block;\n  float: left;\n  font-size: 12px;\n}\n\ndiv.vis-configuration-wrapper {\n  display: block;\n  width: 700px;\n}\n\ndiv.vis-configuration-wrapper::after {\n  clear: both;\n  content: \"\";\n  display: block;\n}\n\ndiv.vis-configuration.vis-config-option-container {\n  display: block;\n  width: 495px;\n  background-color: #ffffff;\n  border: 2px solid #f7f8fa;\n  border-radius: 4px;\n  margin-top: 20px;\n  left: 10px;\n  padding-left: 5px;\n}\n\ndiv.vis-configuration.vis-config-button {\n  display: block;\n  width: 495px;\n  height: 25px;\n  vertical-align: middle;\n  line-height: 25px;\n  background-color: #f7f8fa;\n  border: 2px solid #ceced0;\n  border-radius: 4px;\n  margin-top: 20px;\n  left: 10px;\n  padding-left: 5px;\n  cursor: pointer;\n  margin-bottom: 30px;\n}\n\ndiv.vis-configuration.vis-config-button.hover {\n  background-color: #4588e6;\n  border: 2px solid #214373;\n  color: #ffffff;\n}\n\ndiv.vis-configuration.vis-config-item {\n  display: block;\n  float: left;\n  width: 495px;\n  height: 25px;\n  vertical-align: middle;\n  line-height: 25px;\n}\n\ndiv.vis-configuration.vis-config-item.vis-config-s2 {\n  left: 10px;\n  background-color: #f7f8fa;\n  padding-left: 5px;\n  border-radius: 3px;\n}\ndiv.vis-configuration.vis-config-item.vis-config-s3 {\n  left: 20px;\n  background-color: #e4e9f0;\n  padding-left: 5px;\n  border-radius: 3px;\n}\ndiv.vis-configuration.vis-config-item.vis-config-s4 {\n  left: 30px;\n  background-color: #cfd8e6;\n  padding-left: 5px;\n  border-radius: 3px;\n}\n\ndiv.vis-configuration.vis-config-header {\n  font-size: 18px;\n  font-weight: bold;\n}\n\ndiv.vis-configuration.vis-config-label {\n  width: 120px;\n  height: 25px;\n  line-height: 25px;\n}\n\ndiv.vis-configuration.vis-config-label.vis-config-s3 {\n  width: 110px;\n}\ndiv.vis-configuration.vis-config-label.vis-config-s4 {\n  width: 100px;\n}\n\ndiv.vis-configuration.vis-config-colorBlock {\n  top: 1px;\n  width: 30px;\n  height: 19px;\n  border: 1px solid #444444;\n  border-radius: 2px;\n  padding: 0px;\n  margin: 0px;\n  cursor: pointer;\n}\n\ninput.vis-configuration.vis-config-checkbox {\n  left: -5px;\n}\n\ninput.vis-configuration.vis-config-rangeinput {\n  position: relative;\n  top: -5px;\n  width: 60px;\n  /*height:13px;*/\n  padding: 1px;\n  margin: 0;\n  pointer-events: none;\n}\n\ninput.vis-configuration.vis-config-range {\n  /*removes default webkit styles*/\n  -webkit-appearance: none;\n\n  /*fix for FF unable to apply focus style bug */\n  border: 0px solid white;\n  background-color: rgba(0, 0, 0, 0);\n\n  /*required for proper track sizing in FF*/\n  width: 300px;\n  height: 20px;\n}\ninput.vis-configuration.vis-config-range::-webkit-slider-runnable-track {\n  width: 300px;\n  height: 5px;\n  background: #dedede; /* Old browsers */\n  background: -moz-linear-gradient(top, #dedede 0%, #c8c8c8 99%); /* FF3.6+ */\n  background: -webkit-gradient(\n    linear,\n    left top,\n    left bottom,\n    color-stop(0%, #dedede),\n    color-stop(99%, #c8c8c8)\n  ); /* Chrome,Safari4+ */\n  background: -webkit-linear-gradient(\n    top,\n    #dedede 0%,\n    #c8c8c8 99%\n  ); /* Chrome10+,Safari5.1+ */\n  background: -o-linear-gradient(\n    top,\n    #dedede 0%,\n    #c8c8c8 99%\n  ); /* Opera 11.10+ */\n  background: -ms-linear-gradient(top, #dedede 0%, #c8c8c8 99%); /* IE10+ */\n  background: linear-gradient(to bottom, #dedede 0%, #c8c8c8 99%); /* W3C */\n  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#dedede', endColorstr='#c8c8c8',GradientType=0 ); /* IE6-9 */\n\n  border: 1px solid #999999;\n  box-shadow: #aaaaaa 0px 0px 3px 0px;\n  border-radius: 3px;\n}\ninput.vis-configuration.vis-config-range::-webkit-slider-thumb {\n  -webkit-appearance: none;\n  border: 1px solid #14334b;\n  height: 17px;\n  width: 17px;\n  border-radius: 50%;\n  background: #3876c2; /* Old browsers */\n  background: -moz-linear-gradient(top, #3876c2 0%, #385380 100%); /* FF3.6+ */\n  background: -webkit-gradient(\n    linear,\n    left top,\n    left bottom,\n    color-stop(0%, #3876c2),\n    color-stop(100%, #385380)\n  ); /* Chrome,Safari4+ */\n  background: -webkit-linear-gradient(\n    top,\n    #3876c2 0%,\n    #385380 100%\n  ); /* Chrome10+,Safari5.1+ */\n  background: -o-linear-gradient(\n    top,\n    #3876c2 0%,\n    #385380 100%\n  ); /* Opera 11.10+ */\n  background: -ms-linear-gradient(top, #3876c2 0%, #385380 100%); /* IE10+ */\n  background: linear-gradient(to bottom, #3876c2 0%, #385380 100%); /* W3C */\n  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3876c2', endColorstr='#385380',GradientType=0 ); /* IE6-9 */\n  box-shadow: #111927 0px 0px 1px 0px;\n  margin-top: -7px;\n}\ninput.vis-configuration.vis-config-range:focus {\n  outline: none;\n}\ninput.vis-configuration.vis-config-range:focus::-webkit-slider-runnable-track {\n  background: #9d9d9d; /* Old browsers */\n  background: -moz-linear-gradient(top, #9d9d9d 0%, #c8c8c8 99%); /* FF3.6+ */\n  background: -webkit-gradient(\n    linear,\n    left top,\n    left bottom,\n    color-stop(0%, #9d9d9d),\n    color-stop(99%, #c8c8c8)\n  ); /* Chrome,Safari4+ */\n  background: -webkit-linear-gradient(\n    top,\n    #9d9d9d 0%,\n    #c8c8c8 99%\n  ); /* Chrome10+,Safari5.1+ */\n  background: -o-linear-gradient(\n    top,\n    #9d9d9d 0%,\n    #c8c8c8 99%\n  ); /* Opera 11.10+ */\n  background: -ms-linear-gradient(top, #9d9d9d 0%, #c8c8c8 99%); /* IE10+ */\n  background: linear-gradient(to bottom, #9d9d9d 0%, #c8c8c8 99%); /* W3C */\n  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#9d9d9d', endColorstr='#c8c8c8',GradientType=0 ); /* IE6-9 */\n}\n\ninput.vis-configuration.vis-config-range::-moz-range-track {\n  width: 300px;\n  height: 10px;\n  background: #dedede; /* Old browsers */\n  background: -moz-linear-gradient(top, #dedede 0%, #c8c8c8 99%); /* FF3.6+ */\n  background: -webkit-gradient(\n    linear,\n    left top,\n    left bottom,\n    color-stop(0%, #dedede),\n    color-stop(99%, #c8c8c8)\n  ); /* Chrome,Safari4+ */\n  background: -webkit-linear-gradient(\n    top,\n    #dedede 0%,\n    #c8c8c8 99%\n  ); /* Chrome10+,Safari5.1+ */\n  background: -o-linear-gradient(\n    top,\n    #dedede 0%,\n    #c8c8c8 99%\n  ); /* Opera 11.10+ */\n  background: -ms-linear-gradient(top, #dedede 0%, #c8c8c8 99%); /* IE10+ */\n  background: linear-gradient(to bottom, #dedede 0%, #c8c8c8 99%); /* W3C */\n  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#dedede', endColorstr='#c8c8c8',GradientType=0 ); /* IE6-9 */\n\n  border: 1px solid #999999;\n  box-shadow: #aaaaaa 0px 0px 3px 0px;\n  border-radius: 3px;\n}\ninput.vis-configuration.vis-config-range::-moz-range-thumb {\n  border: none;\n  height: 16px;\n  width: 16px;\n\n  border-radius: 50%;\n  background: #385380;\n}\n\n/*hide the outline behind the border*/\ninput.vis-configuration.vis-config-range:-moz-focusring {\n  outline: 1px solid white;\n  outline-offset: -1px;\n}\n\ninput.vis-configuration.vis-config-range::-ms-track {\n  width: 300px;\n  height: 5px;\n\n  /*remove bg colour from the track, we'll use ms-fill-lower and ms-fill-upper instead */\n  background: transparent;\n\n  /*leave room for the larger thumb to overflow with a transparent border */\n  border-color: transparent;\n  border-width: 6px 0;\n\n  /*remove default tick marks*/\n  color: transparent;\n}\ninput.vis-configuration.vis-config-range::-ms-fill-lower {\n  background: #777;\n  border-radius: 10px;\n}\ninput.vis-configuration.vis-config-range::-ms-fill-upper {\n  background: #ddd;\n  border-radius: 10px;\n}\ninput.vis-configuration.vis-config-range::-ms-thumb {\n  border: none;\n  height: 16px;\n  width: 16px;\n  border-radius: 50%;\n  background: #385380;\n}\ninput.vis-configuration.vis-config-range:focus::-ms-fill-lower {\n  background: #888;\n}\ninput.vis-configuration.vis-config-range:focus::-ms-fill-upper {\n  background: #ccc;\n}\n\n.vis-configuration-popup {\n  position: absolute;\n  background: rgba(57, 76, 89, 0.85);\n  border: 2px solid #f2faff;\n  line-height: 30px;\n  height: 30px;\n  width: 150px;\n  text-align: center;\n  color: #ffffff;\n  font-size: 14px;\n  border-radius: 4px;\n  -webkit-transition: opacity 0.3s ease-in-out;\n  -moz-transition: opacity 0.3s ease-in-out;\n  transition: opacity 0.3s ease-in-out;\n}\n.vis-configuration-popup:after,\n.vis-configuration-popup:before {\n  left: 100%;\n  top: 50%;\n  border: solid transparent;\n  content: \" \";\n  height: 0;\n  width: 0;\n  position: absolute;\n  pointer-events: none;\n}\n\n.vis-configuration-popup:after {\n  border-color: rgba(136, 183, 213, 0);\n  border-left-color: rgba(57, 76, 89, 0.85);\n  border-width: 8px;\n  margin-top: -8px;\n}\n.vis-configuration-popup:before {\n  border-color: rgba(194, 225, 245, 0);\n  border-left-color: #f2faff;\n  border-width: 12px;\n  margin-top: -12px;\n}\n", "div.vis-tooltip {\n  position: absolute;\n  visibility: hidden;\n  padding: 5px;\n  white-space: nowrap;\n\n  font-family: verdana;\n  font-size: 14px;\n  color: #000000;\n  background-color: #f5f4ed;\n\n  -moz-border-radius: 3px;\n  -webkit-border-radius: 3px;\n  border-radius: 3px;\n  border: 1px solid #808074;\n\n  box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.2);\n  pointer-events: none;\n\n  z-index: 5;\n}\n"]}