// node_modules/vis-data/peer/esm/vis-data.mjs
var commonjsGlobal = typeof globalThis !== "undefined" ? globalThis : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : {};
function getDefaultExportFromCjs(x) {
  return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, "default") ? x["default"] : x;
}
var defineProperty$5 = { exports: {} };
var es_object_defineProperty = {};
var globalThis_1;
var hasRequiredGlobalThis;
function requireGlobalThis() {
  if (hasRequiredGlobalThis) return globalThis_1;
  hasRequiredGlobalThis = 1;
  var check = function(it) {
    return it && it.Math === Math && it;
  };
  globalThis_1 = // eslint-disable-next-line es/no-global-this -- safe
  check(typeof globalThis == "object" && globalThis) || check(typeof window == "object" && window) || // eslint-disable-next-line no-restricted-globals -- safe
  check(typeof self == "object" && self) || check(typeof commonjsGlobal == "object" && commonjsGlobal) || check(typeof globalThis_1 == "object" && globalThis_1) || // eslint-disable-next-line no-new-func -- fallback
  /* @__PURE__ */ (function() {
    return this;
  })() || Function("return this")();
  return globalThis_1;
}
var fails;
var hasRequiredFails;
function requireFails() {
  if (hasRequiredFails) return fails;
  hasRequiredFails = 1;
  fails = function(exec) {
    try {
      return !!exec();
    } catch (error) {
      return true;
    }
  };
  return fails;
}
var functionBindNative;
var hasRequiredFunctionBindNative;
function requireFunctionBindNative() {
  if (hasRequiredFunctionBindNative) return functionBindNative;
  hasRequiredFunctionBindNative = 1;
  var fails2 = requireFails();
  functionBindNative = !fails2(function() {
    var test = (function() {
    }).bind();
    return typeof test != "function" || test.hasOwnProperty("prototype");
  });
  return functionBindNative;
}
var functionApply;
var hasRequiredFunctionApply;
function requireFunctionApply() {
  if (hasRequiredFunctionApply) return functionApply;
  hasRequiredFunctionApply = 1;
  var NATIVE_BIND = requireFunctionBindNative();
  var FunctionPrototype = Function.prototype;
  var apply = FunctionPrototype.apply;
  var call = FunctionPrototype.call;
  functionApply = typeof Reflect == "object" && Reflect.apply || (NATIVE_BIND ? call.bind(apply) : function() {
    return call.apply(apply, arguments);
  });
  return functionApply;
}
var functionUncurryThis;
var hasRequiredFunctionUncurryThis;
function requireFunctionUncurryThis() {
  if (hasRequiredFunctionUncurryThis) return functionUncurryThis;
  hasRequiredFunctionUncurryThis = 1;
  var NATIVE_BIND = requireFunctionBindNative();
  var FunctionPrototype = Function.prototype;
  var call = FunctionPrototype.call;
  var uncurryThisWithBind = NATIVE_BIND && FunctionPrototype.bind.bind(call, call);
  functionUncurryThis = NATIVE_BIND ? uncurryThisWithBind : function(fn) {
    return function() {
      return call.apply(fn, arguments);
    };
  };
  return functionUncurryThis;
}
var classofRaw;
var hasRequiredClassofRaw;
function requireClassofRaw() {
  if (hasRequiredClassofRaw) return classofRaw;
  hasRequiredClassofRaw = 1;
  var uncurryThis = requireFunctionUncurryThis();
  var toString2 = uncurryThis({}.toString);
  var stringSlice = uncurryThis("".slice);
  classofRaw = function(it) {
    return stringSlice(toString2(it), 8, -1);
  };
  return classofRaw;
}
var functionUncurryThisClause;
var hasRequiredFunctionUncurryThisClause;
function requireFunctionUncurryThisClause() {
  if (hasRequiredFunctionUncurryThisClause) return functionUncurryThisClause;
  hasRequiredFunctionUncurryThisClause = 1;
  var classofRaw2 = requireClassofRaw();
  var uncurryThis = requireFunctionUncurryThis();
  functionUncurryThisClause = function(fn) {
    if (classofRaw2(fn) === "Function") return uncurryThis(fn);
  };
  return functionUncurryThisClause;
}
var isCallable;
var hasRequiredIsCallable;
function requireIsCallable() {
  if (hasRequiredIsCallable) return isCallable;
  hasRequiredIsCallable = 1;
  var documentAll = typeof document == "object" && document.all;
  isCallable = typeof documentAll == "undefined" && documentAll !== void 0 ? function(argument) {
    return typeof argument == "function" || argument === documentAll;
  } : function(argument) {
    return typeof argument == "function";
  };
  return isCallable;
}
var objectGetOwnPropertyDescriptor = {};
var descriptors;
var hasRequiredDescriptors;
function requireDescriptors() {
  if (hasRequiredDescriptors) return descriptors;
  hasRequiredDescriptors = 1;
  var fails2 = requireFails();
  descriptors = !fails2(function() {
    return Object.defineProperty({}, 1, { get: function() {
      return 7;
    } })[1] !== 7;
  });
  return descriptors;
}
var functionCall;
var hasRequiredFunctionCall;
function requireFunctionCall() {
  if (hasRequiredFunctionCall) return functionCall;
  hasRequiredFunctionCall = 1;
  var NATIVE_BIND = requireFunctionBindNative();
  var call = Function.prototype.call;
  functionCall = NATIVE_BIND ? call.bind(call) : function() {
    return call.apply(call, arguments);
  };
  return functionCall;
}
var objectPropertyIsEnumerable = {};
var hasRequiredObjectPropertyIsEnumerable;
function requireObjectPropertyIsEnumerable() {
  if (hasRequiredObjectPropertyIsEnumerable) return objectPropertyIsEnumerable;
  hasRequiredObjectPropertyIsEnumerable = 1;
  var $propertyIsEnumerable = {}.propertyIsEnumerable;
  var getOwnPropertyDescriptor2 = Object.getOwnPropertyDescriptor;
  var NASHORN_BUG = getOwnPropertyDescriptor2 && !$propertyIsEnumerable.call({ 1: 2 }, 1);
  objectPropertyIsEnumerable.f = NASHORN_BUG ? function propertyIsEnumerable(V) {
    var descriptor = getOwnPropertyDescriptor2(this, V);
    return !!descriptor && descriptor.enumerable;
  } : $propertyIsEnumerable;
  return objectPropertyIsEnumerable;
}
var createPropertyDescriptor;
var hasRequiredCreatePropertyDescriptor;
function requireCreatePropertyDescriptor() {
  if (hasRequiredCreatePropertyDescriptor) return createPropertyDescriptor;
  hasRequiredCreatePropertyDescriptor = 1;
  createPropertyDescriptor = function(bitmap, value) {
    return {
      enumerable: !(bitmap & 1),
      configurable: !(bitmap & 2),
      writable: !(bitmap & 4),
      value
    };
  };
  return createPropertyDescriptor;
}
var indexedObject;
var hasRequiredIndexedObject;
function requireIndexedObject() {
  if (hasRequiredIndexedObject) return indexedObject;
  hasRequiredIndexedObject = 1;
  var uncurryThis = requireFunctionUncurryThis();
  var fails2 = requireFails();
  var classof2 = requireClassofRaw();
  var $Object = Object;
  var split = uncurryThis("".split);
  indexedObject = fails2(function() {
    return !$Object("z").propertyIsEnumerable(0);
  }) ? function(it) {
    return classof2(it) === "String" ? split(it, "") : $Object(it);
  } : $Object;
  return indexedObject;
}
var isNullOrUndefined;
var hasRequiredIsNullOrUndefined;
function requireIsNullOrUndefined() {
  if (hasRequiredIsNullOrUndefined) return isNullOrUndefined;
  hasRequiredIsNullOrUndefined = 1;
  isNullOrUndefined = function(it) {
    return it === null || it === void 0;
  };
  return isNullOrUndefined;
}
var requireObjectCoercible;
var hasRequiredRequireObjectCoercible;
function requireRequireObjectCoercible() {
  if (hasRequiredRequireObjectCoercible) return requireObjectCoercible;
  hasRequiredRequireObjectCoercible = 1;
  var isNullOrUndefined2 = requireIsNullOrUndefined();
  var $TypeError = TypeError;
  requireObjectCoercible = function(it) {
    if (isNullOrUndefined2(it)) throw new $TypeError("Can't call method on " + it);
    return it;
  };
  return requireObjectCoercible;
}
var toIndexedObject;
var hasRequiredToIndexedObject;
function requireToIndexedObject() {
  if (hasRequiredToIndexedObject) return toIndexedObject;
  hasRequiredToIndexedObject = 1;
  var IndexedObject = requireIndexedObject();
  var requireObjectCoercible2 = requireRequireObjectCoercible();
  toIndexedObject = function(it) {
    return IndexedObject(requireObjectCoercible2(it));
  };
  return toIndexedObject;
}
var isObject;
var hasRequiredIsObject;
function requireIsObject() {
  if (hasRequiredIsObject) return isObject;
  hasRequiredIsObject = 1;
  var isCallable2 = requireIsCallable();
  isObject = function(it) {
    return typeof it == "object" ? it !== null : isCallable2(it);
  };
  return isObject;
}
var path;
var hasRequiredPath;
function requirePath() {
  if (hasRequiredPath) return path;
  hasRequiredPath = 1;
  path = {};
  return path;
}
var getBuiltIn;
var hasRequiredGetBuiltIn;
function requireGetBuiltIn() {
  if (hasRequiredGetBuiltIn) return getBuiltIn;
  hasRequiredGetBuiltIn = 1;
  var path2 = requirePath();
  var globalThis2 = requireGlobalThis();
  var isCallable2 = requireIsCallable();
  var aFunction = function(variable) {
    return isCallable2(variable) ? variable : void 0;
  };
  getBuiltIn = function(namespace, method) {
    return arguments.length < 2 ? aFunction(path2[namespace]) || aFunction(globalThis2[namespace]) : path2[namespace] && path2[namespace][method] || globalThis2[namespace] && globalThis2[namespace][method];
  };
  return getBuiltIn;
}
var objectIsPrototypeOf;
var hasRequiredObjectIsPrototypeOf;
function requireObjectIsPrototypeOf() {
  if (hasRequiredObjectIsPrototypeOf) return objectIsPrototypeOf;
  hasRequiredObjectIsPrototypeOf = 1;
  var uncurryThis = requireFunctionUncurryThis();
  objectIsPrototypeOf = uncurryThis({}.isPrototypeOf);
  return objectIsPrototypeOf;
}
var environmentUserAgent;
var hasRequiredEnvironmentUserAgent;
function requireEnvironmentUserAgent() {
  if (hasRequiredEnvironmentUserAgent) return environmentUserAgent;
  hasRequiredEnvironmentUserAgent = 1;
  var globalThis2 = requireGlobalThis();
  var navigator2 = globalThis2.navigator;
  var userAgent = navigator2 && navigator2.userAgent;
  environmentUserAgent = userAgent ? String(userAgent) : "";
  return environmentUserAgent;
}
var environmentV8Version;
var hasRequiredEnvironmentV8Version;
function requireEnvironmentV8Version() {
  if (hasRequiredEnvironmentV8Version) return environmentV8Version;
  hasRequiredEnvironmentV8Version = 1;
  var globalThis2 = requireGlobalThis();
  var userAgent = requireEnvironmentUserAgent();
  var process = globalThis2.process;
  var Deno2 = globalThis2.Deno;
  var versions = process && process.versions || Deno2 && Deno2.version;
  var v8 = versions && versions.v8;
  var match, version;
  if (v8) {
    match = v8.split(".");
    version = match[0] > 0 && match[0] < 4 ? 1 : +(match[0] + match[1]);
  }
  if (!version && userAgent) {
    match = userAgent.match(/Edge\/(\d+)/);
    if (!match || match[1] >= 74) {
      match = userAgent.match(/Chrome\/(\d+)/);
      if (match) version = +match[1];
    }
  }
  environmentV8Version = version;
  return environmentV8Version;
}
var symbolConstructorDetection;
var hasRequiredSymbolConstructorDetection;
function requireSymbolConstructorDetection() {
  if (hasRequiredSymbolConstructorDetection) return symbolConstructorDetection;
  hasRequiredSymbolConstructorDetection = 1;
  var V8_VERSION = requireEnvironmentV8Version();
  var fails2 = requireFails();
  var globalThis2 = requireGlobalThis();
  var $String = globalThis2.String;
  symbolConstructorDetection = !!Object.getOwnPropertySymbols && !fails2(function() {
    var symbol2 = Symbol("symbol detection");
    return !$String(symbol2) || !(Object(symbol2) instanceof Symbol) || // Chrome 38-40 symbols are not inherited from DOM collections prototypes to instances
    !Symbol.sham && V8_VERSION && V8_VERSION < 41;
  });
  return symbolConstructorDetection;
}
var useSymbolAsUid;
var hasRequiredUseSymbolAsUid;
function requireUseSymbolAsUid() {
  if (hasRequiredUseSymbolAsUid) return useSymbolAsUid;
  hasRequiredUseSymbolAsUid = 1;
  var NATIVE_SYMBOL = requireSymbolConstructorDetection();
  useSymbolAsUid = NATIVE_SYMBOL && !Symbol.sham && typeof Symbol.iterator == "symbol";
  return useSymbolAsUid;
}
var isSymbol;
var hasRequiredIsSymbol;
function requireIsSymbol() {
  if (hasRequiredIsSymbol) return isSymbol;
  hasRequiredIsSymbol = 1;
  var getBuiltIn2 = requireGetBuiltIn();
  var isCallable2 = requireIsCallable();
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var USE_SYMBOL_AS_UID = requireUseSymbolAsUid();
  var $Object = Object;
  isSymbol = USE_SYMBOL_AS_UID ? function(it) {
    return typeof it == "symbol";
  } : function(it) {
    var $Symbol = getBuiltIn2("Symbol");
    return isCallable2($Symbol) && isPrototypeOf($Symbol.prototype, $Object(it));
  };
  return isSymbol;
}
var tryToString;
var hasRequiredTryToString;
function requireTryToString() {
  if (hasRequiredTryToString) return tryToString;
  hasRequiredTryToString = 1;
  var $String = String;
  tryToString = function(argument) {
    try {
      return $String(argument);
    } catch (error) {
      return "Object";
    }
  };
  return tryToString;
}
var aCallable;
var hasRequiredACallable;
function requireACallable() {
  if (hasRequiredACallable) return aCallable;
  hasRequiredACallable = 1;
  var isCallable2 = requireIsCallable();
  var tryToString2 = requireTryToString();
  var $TypeError = TypeError;
  aCallable = function(argument) {
    if (isCallable2(argument)) return argument;
    throw new $TypeError(tryToString2(argument) + " is not a function");
  };
  return aCallable;
}
var getMethod;
var hasRequiredGetMethod;
function requireGetMethod() {
  if (hasRequiredGetMethod) return getMethod;
  hasRequiredGetMethod = 1;
  var aCallable2 = requireACallable();
  var isNullOrUndefined2 = requireIsNullOrUndefined();
  getMethod = function(V, P) {
    var func = V[P];
    return isNullOrUndefined2(func) ? void 0 : aCallable2(func);
  };
  return getMethod;
}
var ordinaryToPrimitive;
var hasRequiredOrdinaryToPrimitive;
function requireOrdinaryToPrimitive() {
  if (hasRequiredOrdinaryToPrimitive) return ordinaryToPrimitive;
  hasRequiredOrdinaryToPrimitive = 1;
  var call = requireFunctionCall();
  var isCallable2 = requireIsCallable();
  var isObject2 = requireIsObject();
  var $TypeError = TypeError;
  ordinaryToPrimitive = function(input, pref) {
    var fn, val;
    if (pref === "string" && isCallable2(fn = input.toString) && !isObject2(val = call(fn, input))) return val;
    if (isCallable2(fn = input.valueOf) && !isObject2(val = call(fn, input))) return val;
    if (pref !== "string" && isCallable2(fn = input.toString) && !isObject2(val = call(fn, input))) return val;
    throw new $TypeError("Can't convert object to primitive value");
  };
  return ordinaryToPrimitive;
}
var sharedStore = { exports: {} };
var isPure;
var hasRequiredIsPure;
function requireIsPure() {
  if (hasRequiredIsPure) return isPure;
  hasRequiredIsPure = 1;
  isPure = true;
  return isPure;
}
var defineGlobalProperty;
var hasRequiredDefineGlobalProperty;
function requireDefineGlobalProperty() {
  if (hasRequiredDefineGlobalProperty) return defineGlobalProperty;
  hasRequiredDefineGlobalProperty = 1;
  var globalThis2 = requireGlobalThis();
  var defineProperty2 = Object.defineProperty;
  defineGlobalProperty = function(key, value) {
    try {
      defineProperty2(globalThis2, key, { value, configurable: true, writable: true });
    } catch (error) {
      globalThis2[key] = value;
    }
    return value;
  };
  return defineGlobalProperty;
}
var hasRequiredSharedStore;
function requireSharedStore() {
  if (hasRequiredSharedStore) return sharedStore.exports;
  hasRequiredSharedStore = 1;
  var IS_PURE = requireIsPure();
  var globalThis2 = requireGlobalThis();
  var defineGlobalProperty2 = requireDefineGlobalProperty();
  var SHARED = "__core-js_shared__";
  var store = sharedStore.exports = globalThis2[SHARED] || defineGlobalProperty2(SHARED, {});
  (store.versions || (store.versions = [])).push({
    version: "3.44.0",
    mode: IS_PURE ? "pure" : "global",
    copyright: "© 2014-2025 Denis Pushkarev (zloirock.ru)",
    license: "https://github.com/zloirock/core-js/blob/v3.44.0/LICENSE",
    source: "https://github.com/zloirock/core-js"
  });
  return sharedStore.exports;
}
var shared;
var hasRequiredShared;
function requireShared() {
  if (hasRequiredShared) return shared;
  hasRequiredShared = 1;
  var store = requireSharedStore();
  shared = function(key, value) {
    return store[key] || (store[key] = value || {});
  };
  return shared;
}
var toObject;
var hasRequiredToObject;
function requireToObject() {
  if (hasRequiredToObject) return toObject;
  hasRequiredToObject = 1;
  var requireObjectCoercible2 = requireRequireObjectCoercible();
  var $Object = Object;
  toObject = function(argument) {
    return $Object(requireObjectCoercible2(argument));
  };
  return toObject;
}
var hasOwnProperty_1;
var hasRequiredHasOwnProperty;
function requireHasOwnProperty() {
  if (hasRequiredHasOwnProperty) return hasOwnProperty_1;
  hasRequiredHasOwnProperty = 1;
  var uncurryThis = requireFunctionUncurryThis();
  var toObject2 = requireToObject();
  var hasOwnProperty = uncurryThis({}.hasOwnProperty);
  hasOwnProperty_1 = Object.hasOwn || function hasOwn(it, key) {
    return hasOwnProperty(toObject2(it), key);
  };
  return hasOwnProperty_1;
}
var uid;
var hasRequiredUid;
function requireUid() {
  if (hasRequiredUid) return uid;
  hasRequiredUid = 1;
  var uncurryThis = requireFunctionUncurryThis();
  var id = 0;
  var postfix = Math.random();
  var toString2 = uncurryThis(1.1.toString);
  uid = function(key) {
    return "Symbol(" + (key === void 0 ? "" : key) + ")_" + toString2(++id + postfix, 36);
  };
  return uid;
}
var wellKnownSymbol;
var hasRequiredWellKnownSymbol;
function requireWellKnownSymbol() {
  if (hasRequiredWellKnownSymbol) return wellKnownSymbol;
  hasRequiredWellKnownSymbol = 1;
  var globalThis2 = requireGlobalThis();
  var shared2 = requireShared();
  var hasOwn = requireHasOwnProperty();
  var uid2 = requireUid();
  var NATIVE_SYMBOL = requireSymbolConstructorDetection();
  var USE_SYMBOL_AS_UID = requireUseSymbolAsUid();
  var Symbol2 = globalThis2.Symbol;
  var WellKnownSymbolsStore = shared2("wks");
  var createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol2["for"] || Symbol2 : Symbol2 && Symbol2.withoutSetter || uid2;
  wellKnownSymbol = function(name) {
    if (!hasOwn(WellKnownSymbolsStore, name)) {
      WellKnownSymbolsStore[name] = NATIVE_SYMBOL && hasOwn(Symbol2, name) ? Symbol2[name] : createWellKnownSymbol("Symbol." + name);
    }
    return WellKnownSymbolsStore[name];
  };
  return wellKnownSymbol;
}
var toPrimitive$6;
var hasRequiredToPrimitive$5;
function requireToPrimitive$5() {
  if (hasRequiredToPrimitive$5) return toPrimitive$6;
  hasRequiredToPrimitive$5 = 1;
  var call = requireFunctionCall();
  var isObject2 = requireIsObject();
  var isSymbol2 = requireIsSymbol();
  var getMethod2 = requireGetMethod();
  var ordinaryToPrimitive2 = requireOrdinaryToPrimitive();
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var $TypeError = TypeError;
  var TO_PRIMITIVE = wellKnownSymbol2("toPrimitive");
  toPrimitive$6 = function(input, pref) {
    if (!isObject2(input) || isSymbol2(input)) return input;
    var exoticToPrim = getMethod2(input, TO_PRIMITIVE);
    var result;
    if (exoticToPrim) {
      if (pref === void 0) pref = "default";
      result = call(exoticToPrim, input, pref);
      if (!isObject2(result) || isSymbol2(result)) return result;
      throw new $TypeError("Can't convert object to primitive value");
    }
    if (pref === void 0) pref = "number";
    return ordinaryToPrimitive2(input, pref);
  };
  return toPrimitive$6;
}
var toPropertyKey$1;
var hasRequiredToPropertyKey;
function requireToPropertyKey() {
  if (hasRequiredToPropertyKey) return toPropertyKey$1;
  hasRequiredToPropertyKey = 1;
  var toPrimitive2 = requireToPrimitive$5();
  var isSymbol2 = requireIsSymbol();
  toPropertyKey$1 = function(argument) {
    var key = toPrimitive2(argument, "string");
    return isSymbol2(key) ? key : key + "";
  };
  return toPropertyKey$1;
}
var documentCreateElement;
var hasRequiredDocumentCreateElement;
function requireDocumentCreateElement() {
  if (hasRequiredDocumentCreateElement) return documentCreateElement;
  hasRequiredDocumentCreateElement = 1;
  var globalThis2 = requireGlobalThis();
  var isObject2 = requireIsObject();
  var document2 = globalThis2.document;
  var EXISTS = isObject2(document2) && isObject2(document2.createElement);
  documentCreateElement = function(it) {
    return EXISTS ? document2.createElement(it) : {};
  };
  return documentCreateElement;
}
var ie8DomDefine;
var hasRequiredIe8DomDefine;
function requireIe8DomDefine() {
  if (hasRequiredIe8DomDefine) return ie8DomDefine;
  hasRequiredIe8DomDefine = 1;
  var DESCRIPTORS = requireDescriptors();
  var fails2 = requireFails();
  var createElement = requireDocumentCreateElement();
  ie8DomDefine = !DESCRIPTORS && !fails2(function() {
    return Object.defineProperty(createElement("div"), "a", {
      get: function() {
        return 7;
      }
    }).a !== 7;
  });
  return ie8DomDefine;
}
var hasRequiredObjectGetOwnPropertyDescriptor;
function requireObjectGetOwnPropertyDescriptor() {
  if (hasRequiredObjectGetOwnPropertyDescriptor) return objectGetOwnPropertyDescriptor;
  hasRequiredObjectGetOwnPropertyDescriptor = 1;
  var DESCRIPTORS = requireDescriptors();
  var call = requireFunctionCall();
  var propertyIsEnumerableModule = requireObjectPropertyIsEnumerable();
  var createPropertyDescriptor2 = requireCreatePropertyDescriptor();
  var toIndexedObject2 = requireToIndexedObject();
  var toPropertyKey2 = requireToPropertyKey();
  var hasOwn = requireHasOwnProperty();
  var IE8_DOM_DEFINE = requireIe8DomDefine();
  var $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;
  objectGetOwnPropertyDescriptor.f = DESCRIPTORS ? $getOwnPropertyDescriptor : function getOwnPropertyDescriptor2(O, P) {
    O = toIndexedObject2(O);
    P = toPropertyKey2(P);
    if (IE8_DOM_DEFINE) try {
      return $getOwnPropertyDescriptor(O, P);
    } catch (error) {
    }
    if (hasOwn(O, P)) return createPropertyDescriptor2(!call(propertyIsEnumerableModule.f, O, P), O[P]);
  };
  return objectGetOwnPropertyDescriptor;
}
var isForced_1;
var hasRequiredIsForced;
function requireIsForced() {
  if (hasRequiredIsForced) return isForced_1;
  hasRequiredIsForced = 1;
  var fails2 = requireFails();
  var isCallable2 = requireIsCallable();
  var replacement = /#|\.prototype\./;
  var isForced = function(feature, detection) {
    var value = data[normalize(feature)];
    return value === POLYFILL ? true : value === NATIVE ? false : isCallable2(detection) ? fails2(detection) : !!detection;
  };
  var normalize = isForced.normalize = function(string) {
    return String(string).replace(replacement, ".").toLowerCase();
  };
  var data = isForced.data = {};
  var NATIVE = isForced.NATIVE = "N";
  var POLYFILL = isForced.POLYFILL = "P";
  isForced_1 = isForced;
  return isForced_1;
}
var functionBindContext;
var hasRequiredFunctionBindContext;
function requireFunctionBindContext() {
  if (hasRequiredFunctionBindContext) return functionBindContext;
  hasRequiredFunctionBindContext = 1;
  var uncurryThis = requireFunctionUncurryThisClause();
  var aCallable2 = requireACallable();
  var NATIVE_BIND = requireFunctionBindNative();
  var bind2 = uncurryThis(uncurryThis.bind);
  functionBindContext = function(fn, that) {
    aCallable2(fn);
    return that === void 0 ? fn : NATIVE_BIND ? bind2(fn, that) : function() {
      return fn.apply(that, arguments);
    };
  };
  return functionBindContext;
}
var objectDefineProperty = {};
var v8PrototypeDefineBug;
var hasRequiredV8PrototypeDefineBug;
function requireV8PrototypeDefineBug() {
  if (hasRequiredV8PrototypeDefineBug) return v8PrototypeDefineBug;
  hasRequiredV8PrototypeDefineBug = 1;
  var DESCRIPTORS = requireDescriptors();
  var fails2 = requireFails();
  v8PrototypeDefineBug = DESCRIPTORS && fails2(function() {
    return Object.defineProperty(function() {
    }, "prototype", {
      value: 42,
      writable: false
    }).prototype !== 42;
  });
  return v8PrototypeDefineBug;
}
var anObject;
var hasRequiredAnObject;
function requireAnObject() {
  if (hasRequiredAnObject) return anObject;
  hasRequiredAnObject = 1;
  var isObject2 = requireIsObject();
  var $String = String;
  var $TypeError = TypeError;
  anObject = function(argument) {
    if (isObject2(argument)) return argument;
    throw new $TypeError($String(argument) + " is not an object");
  };
  return anObject;
}
var hasRequiredObjectDefineProperty;
function requireObjectDefineProperty() {
  if (hasRequiredObjectDefineProperty) return objectDefineProperty;
  hasRequiredObjectDefineProperty = 1;
  var DESCRIPTORS = requireDescriptors();
  var IE8_DOM_DEFINE = requireIe8DomDefine();
  var V8_PROTOTYPE_DEFINE_BUG = requireV8PrototypeDefineBug();
  var anObject2 = requireAnObject();
  var toPropertyKey2 = requireToPropertyKey();
  var $TypeError = TypeError;
  var $defineProperty = Object.defineProperty;
  var $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;
  var ENUMERABLE = "enumerable";
  var CONFIGURABLE = "configurable";
  var WRITABLE = "writable";
  objectDefineProperty.f = DESCRIPTORS ? V8_PROTOTYPE_DEFINE_BUG ? function defineProperty2(O, P, Attributes) {
    anObject2(O);
    P = toPropertyKey2(P);
    anObject2(Attributes);
    if (typeof O === "function" && P === "prototype" && "value" in Attributes && WRITABLE in Attributes && !Attributes[WRITABLE]) {
      var current = $getOwnPropertyDescriptor(O, P);
      if (current && current[WRITABLE]) {
        O[P] = Attributes.value;
        Attributes = {
          configurable: CONFIGURABLE in Attributes ? Attributes[CONFIGURABLE] : current[CONFIGURABLE],
          enumerable: ENUMERABLE in Attributes ? Attributes[ENUMERABLE] : current[ENUMERABLE],
          writable: false
        };
      }
    }
    return $defineProperty(O, P, Attributes);
  } : $defineProperty : function defineProperty2(O, P, Attributes) {
    anObject2(O);
    P = toPropertyKey2(P);
    anObject2(Attributes);
    if (IE8_DOM_DEFINE) try {
      return $defineProperty(O, P, Attributes);
    } catch (error) {
    }
    if ("get" in Attributes || "set" in Attributes) throw new $TypeError("Accessors not supported");
    if ("value" in Attributes) O[P] = Attributes.value;
    return O;
  };
  return objectDefineProperty;
}
var createNonEnumerableProperty;
var hasRequiredCreateNonEnumerableProperty;
function requireCreateNonEnumerableProperty() {
  if (hasRequiredCreateNonEnumerableProperty) return createNonEnumerableProperty;
  hasRequiredCreateNonEnumerableProperty = 1;
  var DESCRIPTORS = requireDescriptors();
  var definePropertyModule = requireObjectDefineProperty();
  var createPropertyDescriptor2 = requireCreatePropertyDescriptor();
  createNonEnumerableProperty = DESCRIPTORS ? function(object, key, value) {
    return definePropertyModule.f(object, key, createPropertyDescriptor2(1, value));
  } : function(object, key, value) {
    object[key] = value;
    return object;
  };
  return createNonEnumerableProperty;
}
var _export;
var hasRequired_export;
function require_export() {
  if (hasRequired_export) return _export;
  hasRequired_export = 1;
  var globalThis2 = requireGlobalThis();
  var apply = requireFunctionApply();
  var uncurryThis = requireFunctionUncurryThisClause();
  var isCallable2 = requireIsCallable();
  var getOwnPropertyDescriptor2 = requireObjectGetOwnPropertyDescriptor().f;
  var isForced = requireIsForced();
  var path2 = requirePath();
  var bind2 = requireFunctionBindContext();
  var createNonEnumerableProperty2 = requireCreateNonEnumerableProperty();
  var hasOwn = requireHasOwnProperty();
  var wrapConstructor = function(NativeConstructor) {
    var Wrapper = function(a, b, c) {
      if (this instanceof Wrapper) {
        switch (arguments.length) {
          case 0:
            return new NativeConstructor();
          case 1:
            return new NativeConstructor(a);
          case 2:
            return new NativeConstructor(a, b);
        }
        return new NativeConstructor(a, b, c);
      }
      return apply(NativeConstructor, this, arguments);
    };
    Wrapper.prototype = NativeConstructor.prototype;
    return Wrapper;
  };
  _export = function(options, source) {
    var TARGET = options.target;
    var GLOBAL = options.global;
    var STATIC = options.stat;
    var PROTO = options.proto;
    var nativeSource = GLOBAL ? globalThis2 : STATIC ? globalThis2[TARGET] : globalThis2[TARGET] && globalThis2[TARGET].prototype;
    var target = GLOBAL ? path2 : path2[TARGET] || createNonEnumerableProperty2(path2, TARGET, {})[TARGET];
    var targetPrototype = target.prototype;
    var FORCED, USE_NATIVE, VIRTUAL_PROTOTYPE;
    var key, sourceProperty, targetProperty, nativeProperty, resultProperty, descriptor;
    for (key in source) {
      FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? "." : "#") + key, options.forced);
      USE_NATIVE = !FORCED && nativeSource && hasOwn(nativeSource, key);
      targetProperty = target[key];
      if (USE_NATIVE) if (options.dontCallGetSet) {
        descriptor = getOwnPropertyDescriptor2(nativeSource, key);
        nativeProperty = descriptor && descriptor.value;
      } else nativeProperty = nativeSource[key];
      sourceProperty = USE_NATIVE && nativeProperty ? nativeProperty : source[key];
      if (!FORCED && !PROTO && typeof targetProperty == typeof sourceProperty) continue;
      if (options.bind && USE_NATIVE) resultProperty = bind2(sourceProperty, globalThis2);
      else if (options.wrap && USE_NATIVE) resultProperty = wrapConstructor(sourceProperty);
      else if (PROTO && isCallable2(sourceProperty)) resultProperty = uncurryThis(sourceProperty);
      else resultProperty = sourceProperty;
      if (options.sham || sourceProperty && sourceProperty.sham || targetProperty && targetProperty.sham) {
        createNonEnumerableProperty2(resultProperty, "sham", true);
      }
      createNonEnumerableProperty2(target, key, resultProperty);
      if (PROTO) {
        VIRTUAL_PROTOTYPE = TARGET + "Prototype";
        if (!hasOwn(path2, VIRTUAL_PROTOTYPE)) {
          createNonEnumerableProperty2(path2, VIRTUAL_PROTOTYPE, {});
        }
        createNonEnumerableProperty2(path2[VIRTUAL_PROTOTYPE], key, sourceProperty);
        if (options.real && targetPrototype && (FORCED || !targetPrototype[key])) {
          createNonEnumerableProperty2(targetPrototype, key, sourceProperty);
        }
      }
    }
  };
  return _export;
}
var hasRequiredEs_object_defineProperty;
function requireEs_object_defineProperty() {
  if (hasRequiredEs_object_defineProperty) return es_object_defineProperty;
  hasRequiredEs_object_defineProperty = 1;
  var $ = require_export();
  var DESCRIPTORS = requireDescriptors();
  var defineProperty2 = requireObjectDefineProperty().f;
  $({ target: "Object", stat: true, forced: Object.defineProperty !== defineProperty2, sham: !DESCRIPTORS }, {
    defineProperty: defineProperty2
  });
  return es_object_defineProperty;
}
var hasRequiredDefineProperty$5;
function requireDefineProperty$5() {
  if (hasRequiredDefineProperty$5) return defineProperty$5.exports;
  hasRequiredDefineProperty$5 = 1;
  requireEs_object_defineProperty();
  var path2 = requirePath();
  var Object2 = path2.Object;
  var defineProperty2 = defineProperty$5.exports = function defineProperty3(it, key, desc) {
    return Object2.defineProperty(it, key, desc);
  };
  if (Object2.defineProperty.sham) defineProperty2.sham = true;
  return defineProperty$5.exports;
}
var defineProperty$4;
var hasRequiredDefineProperty$4;
function requireDefineProperty$4() {
  if (hasRequiredDefineProperty$4) return defineProperty$4;
  hasRequiredDefineProperty$4 = 1;
  var parent = requireDefineProperty$5();
  defineProperty$4 = parent;
  return defineProperty$4;
}
var defineProperty$3;
var hasRequiredDefineProperty$3;
function requireDefineProperty$3() {
  if (hasRequiredDefineProperty$3) return defineProperty$3;
  hasRequiredDefineProperty$3 = 1;
  var parent = requireDefineProperty$4();
  defineProperty$3 = parent;
  return defineProperty$3;
}
var defineProperty$2;
var hasRequiredDefineProperty$2;
function requireDefineProperty$2() {
  if (hasRequiredDefineProperty$2) return defineProperty$2;
  hasRequiredDefineProperty$2 = 1;
  var parent = requireDefineProperty$3();
  defineProperty$2 = parent;
  return defineProperty$2;
}
var defineProperty$1;
var hasRequiredDefineProperty$1;
function requireDefineProperty$1() {
  if (hasRequiredDefineProperty$1) return defineProperty$1;
  hasRequiredDefineProperty$1 = 1;
  defineProperty$1 = requireDefineProperty$2();
  return defineProperty$1;
}
var definePropertyExports$1 = requireDefineProperty$1();
var _Object$defineProperty$1 = getDefaultExportFromCjs(definePropertyExports$1);
var es_array_concat = {};
var isArray$3;
var hasRequiredIsArray$3;
function requireIsArray$3() {
  if (hasRequiredIsArray$3) return isArray$3;
  hasRequiredIsArray$3 = 1;
  var classof2 = requireClassofRaw();
  isArray$3 = Array.isArray || function isArray2(argument) {
    return classof2(argument) === "Array";
  };
  return isArray$3;
}
var mathTrunc;
var hasRequiredMathTrunc;
function requireMathTrunc() {
  if (hasRequiredMathTrunc) return mathTrunc;
  hasRequiredMathTrunc = 1;
  var ceil = Math.ceil;
  var floor = Math.floor;
  mathTrunc = Math.trunc || function trunc(x) {
    var n = +x;
    return (n > 0 ? floor : ceil)(n);
  };
  return mathTrunc;
}
var toIntegerOrInfinity;
var hasRequiredToIntegerOrInfinity;
function requireToIntegerOrInfinity() {
  if (hasRequiredToIntegerOrInfinity) return toIntegerOrInfinity;
  hasRequiredToIntegerOrInfinity = 1;
  var trunc = requireMathTrunc();
  toIntegerOrInfinity = function(argument) {
    var number = +argument;
    return number !== number || number === 0 ? 0 : trunc(number);
  };
  return toIntegerOrInfinity;
}
var toLength;
var hasRequiredToLength;
function requireToLength() {
  if (hasRequiredToLength) return toLength;
  hasRequiredToLength = 1;
  var toIntegerOrInfinity2 = requireToIntegerOrInfinity();
  var min = Math.min;
  toLength = function(argument) {
    var len = toIntegerOrInfinity2(argument);
    return len > 0 ? min(len, 9007199254740991) : 0;
  };
  return toLength;
}
var lengthOfArrayLike;
var hasRequiredLengthOfArrayLike;
function requireLengthOfArrayLike() {
  if (hasRequiredLengthOfArrayLike) return lengthOfArrayLike;
  hasRequiredLengthOfArrayLike = 1;
  var toLength2 = requireToLength();
  lengthOfArrayLike = function(obj) {
    return toLength2(obj.length);
  };
  return lengthOfArrayLike;
}
var doesNotExceedSafeInteger;
var hasRequiredDoesNotExceedSafeInteger;
function requireDoesNotExceedSafeInteger() {
  if (hasRequiredDoesNotExceedSafeInteger) return doesNotExceedSafeInteger;
  hasRequiredDoesNotExceedSafeInteger = 1;
  var $TypeError = TypeError;
  var MAX_SAFE_INTEGER = 9007199254740991;
  doesNotExceedSafeInteger = function(it) {
    if (it > MAX_SAFE_INTEGER) throw $TypeError("Maximum allowed index exceeded");
    return it;
  };
  return doesNotExceedSafeInteger;
}
var createProperty;
var hasRequiredCreateProperty;
function requireCreateProperty() {
  if (hasRequiredCreateProperty) return createProperty;
  hasRequiredCreateProperty = 1;
  var DESCRIPTORS = requireDescriptors();
  var definePropertyModule = requireObjectDefineProperty();
  var createPropertyDescriptor2 = requireCreatePropertyDescriptor();
  createProperty = function(object, key, value) {
    if (DESCRIPTORS) definePropertyModule.f(object, key, createPropertyDescriptor2(0, value));
    else object[key] = value;
  };
  return createProperty;
}
var toStringTagSupport;
var hasRequiredToStringTagSupport;
function requireToStringTagSupport() {
  if (hasRequiredToStringTagSupport) return toStringTagSupport;
  hasRequiredToStringTagSupport = 1;
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var TO_STRING_TAG = wellKnownSymbol2("toStringTag");
  var test = {};
  test[TO_STRING_TAG] = "z";
  toStringTagSupport = String(test) === "[object z]";
  return toStringTagSupport;
}
var classof;
var hasRequiredClassof;
function requireClassof() {
  if (hasRequiredClassof) return classof;
  hasRequiredClassof = 1;
  var TO_STRING_TAG_SUPPORT = requireToStringTagSupport();
  var isCallable2 = requireIsCallable();
  var classofRaw2 = requireClassofRaw();
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var TO_STRING_TAG = wellKnownSymbol2("toStringTag");
  var $Object = Object;
  var CORRECT_ARGUMENTS = classofRaw2(/* @__PURE__ */ (function() {
    return arguments;
  })()) === "Arguments";
  var tryGet = function(it, key) {
    try {
      return it[key];
    } catch (error) {
    }
  };
  classof = TO_STRING_TAG_SUPPORT ? classofRaw2 : function(it) {
    var O, tag, result;
    return it === void 0 ? "Undefined" : it === null ? "Null" : typeof (tag = tryGet(O = $Object(it), TO_STRING_TAG)) == "string" ? tag : CORRECT_ARGUMENTS ? classofRaw2(O) : (result = classofRaw2(O)) === "Object" && isCallable2(O.callee) ? "Arguments" : result;
  };
  return classof;
}
var inspectSource;
var hasRequiredInspectSource;
function requireInspectSource() {
  if (hasRequiredInspectSource) return inspectSource;
  hasRequiredInspectSource = 1;
  var uncurryThis = requireFunctionUncurryThis();
  var isCallable2 = requireIsCallable();
  var store = requireSharedStore();
  var functionToString = uncurryThis(Function.toString);
  if (!isCallable2(store.inspectSource)) {
    store.inspectSource = function(it) {
      return functionToString(it);
    };
  }
  inspectSource = store.inspectSource;
  return inspectSource;
}
var isConstructor;
var hasRequiredIsConstructor;
function requireIsConstructor() {
  if (hasRequiredIsConstructor) return isConstructor;
  hasRequiredIsConstructor = 1;
  var uncurryThis = requireFunctionUncurryThis();
  var fails2 = requireFails();
  var isCallable2 = requireIsCallable();
  var classof2 = requireClassof();
  var getBuiltIn2 = requireGetBuiltIn();
  var inspectSource2 = requireInspectSource();
  var noop = function() {
  };
  var construct = getBuiltIn2("Reflect", "construct");
  var constructorRegExp = /^\s*(?:class|function)\b/;
  var exec = uncurryThis(constructorRegExp.exec);
  var INCORRECT_TO_STRING = !constructorRegExp.test(noop);
  var isConstructorModern = function isConstructor2(argument) {
    if (!isCallable2(argument)) return false;
    try {
      construct(noop, [], argument);
      return true;
    } catch (error) {
      return false;
    }
  };
  var isConstructorLegacy = function isConstructor2(argument) {
    if (!isCallable2(argument)) return false;
    switch (classof2(argument)) {
      case "AsyncFunction":
      case "GeneratorFunction":
      case "AsyncGeneratorFunction":
        return false;
    }
    try {
      return INCORRECT_TO_STRING || !!exec(constructorRegExp, inspectSource2(argument));
    } catch (error) {
      return true;
    }
  };
  isConstructorLegacy.sham = true;
  isConstructor = !construct || fails2(function() {
    var called;
    return isConstructorModern(isConstructorModern.call) || !isConstructorModern(Object) || !isConstructorModern(function() {
      called = true;
    }) || called;
  }) ? isConstructorLegacy : isConstructorModern;
  return isConstructor;
}
var arraySpeciesConstructor;
var hasRequiredArraySpeciesConstructor;
function requireArraySpeciesConstructor() {
  if (hasRequiredArraySpeciesConstructor) return arraySpeciesConstructor;
  hasRequiredArraySpeciesConstructor = 1;
  var isArray2 = requireIsArray$3();
  var isConstructor2 = requireIsConstructor();
  var isObject2 = requireIsObject();
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var SPECIES = wellKnownSymbol2("species");
  var $Array = Array;
  arraySpeciesConstructor = function(originalArray) {
    var C;
    if (isArray2(originalArray)) {
      C = originalArray.constructor;
      if (isConstructor2(C) && (C === $Array || isArray2(C.prototype))) C = void 0;
      else if (isObject2(C)) {
        C = C[SPECIES];
        if (C === null) C = void 0;
      }
    }
    return C === void 0 ? $Array : C;
  };
  return arraySpeciesConstructor;
}
var arraySpeciesCreate;
var hasRequiredArraySpeciesCreate;
function requireArraySpeciesCreate() {
  if (hasRequiredArraySpeciesCreate) return arraySpeciesCreate;
  hasRequiredArraySpeciesCreate = 1;
  var arraySpeciesConstructor2 = requireArraySpeciesConstructor();
  arraySpeciesCreate = function(originalArray, length) {
    return new (arraySpeciesConstructor2(originalArray))(length === 0 ? 0 : length);
  };
  return arraySpeciesCreate;
}
var arrayMethodHasSpeciesSupport;
var hasRequiredArrayMethodHasSpeciesSupport;
function requireArrayMethodHasSpeciesSupport() {
  if (hasRequiredArrayMethodHasSpeciesSupport) return arrayMethodHasSpeciesSupport;
  hasRequiredArrayMethodHasSpeciesSupport = 1;
  var fails2 = requireFails();
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var V8_VERSION = requireEnvironmentV8Version();
  var SPECIES = wellKnownSymbol2("species");
  arrayMethodHasSpeciesSupport = function(METHOD_NAME) {
    return V8_VERSION >= 51 || !fails2(function() {
      var array = [];
      var constructor = array.constructor = {};
      constructor[SPECIES] = function() {
        return { foo: 1 };
      };
      return array[METHOD_NAME](Boolean).foo !== 1;
    });
  };
  return arrayMethodHasSpeciesSupport;
}
var hasRequiredEs_array_concat;
function requireEs_array_concat() {
  if (hasRequiredEs_array_concat) return es_array_concat;
  hasRequiredEs_array_concat = 1;
  var $ = require_export();
  var fails2 = requireFails();
  var isArray2 = requireIsArray$3();
  var isObject2 = requireIsObject();
  var toObject2 = requireToObject();
  var lengthOfArrayLike2 = requireLengthOfArrayLike();
  var doesNotExceedSafeInteger2 = requireDoesNotExceedSafeInteger();
  var createProperty2 = requireCreateProperty();
  var arraySpeciesCreate2 = requireArraySpeciesCreate();
  var arrayMethodHasSpeciesSupport2 = requireArrayMethodHasSpeciesSupport();
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var V8_VERSION = requireEnvironmentV8Version();
  var IS_CONCAT_SPREADABLE = wellKnownSymbol2("isConcatSpreadable");
  var IS_CONCAT_SPREADABLE_SUPPORT = V8_VERSION >= 51 || !fails2(function() {
    var array = [];
    array[IS_CONCAT_SPREADABLE] = false;
    return array.concat()[0] !== array;
  });
  var isConcatSpreadable = function(O) {
    if (!isObject2(O)) return false;
    var spreadable = O[IS_CONCAT_SPREADABLE];
    return spreadable !== void 0 ? !!spreadable : isArray2(O);
  };
  var FORCED = !IS_CONCAT_SPREADABLE_SUPPORT || !arrayMethodHasSpeciesSupport2("concat");
  $({ target: "Array", proto: true, arity: 1, forced: FORCED }, {
    // eslint-disable-next-line no-unused-vars -- required for `.length`
    concat: function concat2(arg) {
      var O = toObject2(this);
      var A = arraySpeciesCreate2(O, 0);
      var n = 0;
      var i, k, length, len, E;
      for (i = -1, length = arguments.length; i < length; i++) {
        E = i === -1 ? O : arguments[i];
        if (isConcatSpreadable(E)) {
          len = lengthOfArrayLike2(E);
          doesNotExceedSafeInteger2(n + len);
          for (k = 0; k < len; k++, n++) if (k in E) createProperty2(A, n, E[k]);
        } else {
          doesNotExceedSafeInteger2(n + 1);
          createProperty2(A, n++, E);
        }
      }
      A.length = n;
      return A;
    }
  });
  return es_array_concat;
}
var es_symbol = {};
var es_symbol_constructor = {};
var toString;
var hasRequiredToString;
function requireToString() {
  if (hasRequiredToString) return toString;
  hasRequiredToString = 1;
  var classof2 = requireClassof();
  var $String = String;
  toString = function(argument) {
    if (classof2(argument) === "Symbol") throw new TypeError("Cannot convert a Symbol value to a string");
    return $String(argument);
  };
  return toString;
}
var objectDefineProperties = {};
var toAbsoluteIndex;
var hasRequiredToAbsoluteIndex;
function requireToAbsoluteIndex() {
  if (hasRequiredToAbsoluteIndex) return toAbsoluteIndex;
  hasRequiredToAbsoluteIndex = 1;
  var toIntegerOrInfinity2 = requireToIntegerOrInfinity();
  var max = Math.max;
  var min = Math.min;
  toAbsoluteIndex = function(index, length) {
    var integer = toIntegerOrInfinity2(index);
    return integer < 0 ? max(integer + length, 0) : min(integer, length);
  };
  return toAbsoluteIndex;
}
var arrayIncludes;
var hasRequiredArrayIncludes;
function requireArrayIncludes() {
  if (hasRequiredArrayIncludes) return arrayIncludes;
  hasRequiredArrayIncludes = 1;
  var toIndexedObject2 = requireToIndexedObject();
  var toAbsoluteIndex2 = requireToAbsoluteIndex();
  var lengthOfArrayLike2 = requireLengthOfArrayLike();
  var createMethod = function(IS_INCLUDES) {
    return function($this, el, fromIndex) {
      var O = toIndexedObject2($this);
      var length = lengthOfArrayLike2(O);
      if (length === 0) return !IS_INCLUDES && -1;
      var index = toAbsoluteIndex2(fromIndex, length);
      var value;
      if (IS_INCLUDES && el !== el) while (length > index) {
        value = O[index++];
        if (value !== value) return true;
      }
      else for (; length > index; index++) {
        if ((IS_INCLUDES || index in O) && O[index] === el) return IS_INCLUDES || index || 0;
      }
      return !IS_INCLUDES && -1;
    };
  };
  arrayIncludes = {
    // `Array.prototype.includes` method
    // https://tc39.es/ecma262/#sec-array.prototype.includes
    includes: createMethod(true),
    // `Array.prototype.indexOf` method
    // https://tc39.es/ecma262/#sec-array.prototype.indexof
    indexOf: createMethod(false)
  };
  return arrayIncludes;
}
var hiddenKeys;
var hasRequiredHiddenKeys;
function requireHiddenKeys() {
  if (hasRequiredHiddenKeys) return hiddenKeys;
  hasRequiredHiddenKeys = 1;
  hiddenKeys = {};
  return hiddenKeys;
}
var objectKeysInternal;
var hasRequiredObjectKeysInternal;
function requireObjectKeysInternal() {
  if (hasRequiredObjectKeysInternal) return objectKeysInternal;
  hasRequiredObjectKeysInternal = 1;
  var uncurryThis = requireFunctionUncurryThis();
  var hasOwn = requireHasOwnProperty();
  var toIndexedObject2 = requireToIndexedObject();
  var indexOf = requireArrayIncludes().indexOf;
  var hiddenKeys2 = requireHiddenKeys();
  var push = uncurryThis([].push);
  objectKeysInternal = function(object, names) {
    var O = toIndexedObject2(object);
    var i = 0;
    var result = [];
    var key;
    for (key in O) !hasOwn(hiddenKeys2, key) && hasOwn(O, key) && push(result, key);
    while (names.length > i) if (hasOwn(O, key = names[i++])) {
      ~indexOf(result, key) || push(result, key);
    }
    return result;
  };
  return objectKeysInternal;
}
var enumBugKeys;
var hasRequiredEnumBugKeys;
function requireEnumBugKeys() {
  if (hasRequiredEnumBugKeys) return enumBugKeys;
  hasRequiredEnumBugKeys = 1;
  enumBugKeys = [
    "constructor",
    "hasOwnProperty",
    "isPrototypeOf",
    "propertyIsEnumerable",
    "toLocaleString",
    "toString",
    "valueOf"
  ];
  return enumBugKeys;
}
var objectKeys;
var hasRequiredObjectKeys;
function requireObjectKeys() {
  if (hasRequiredObjectKeys) return objectKeys;
  hasRequiredObjectKeys = 1;
  var internalObjectKeys = requireObjectKeysInternal();
  var enumBugKeys2 = requireEnumBugKeys();
  objectKeys = Object.keys || function keys2(O) {
    return internalObjectKeys(O, enumBugKeys2);
  };
  return objectKeys;
}
var hasRequiredObjectDefineProperties;
function requireObjectDefineProperties() {
  if (hasRequiredObjectDefineProperties) return objectDefineProperties;
  hasRequiredObjectDefineProperties = 1;
  var DESCRIPTORS = requireDescriptors();
  var V8_PROTOTYPE_DEFINE_BUG = requireV8PrototypeDefineBug();
  var definePropertyModule = requireObjectDefineProperty();
  var anObject2 = requireAnObject();
  var toIndexedObject2 = requireToIndexedObject();
  var objectKeys2 = requireObjectKeys();
  objectDefineProperties.f = DESCRIPTORS && !V8_PROTOTYPE_DEFINE_BUG ? Object.defineProperties : function defineProperties2(O, Properties) {
    anObject2(O);
    var props = toIndexedObject2(Properties);
    var keys2 = objectKeys2(Properties);
    var length = keys2.length;
    var index = 0;
    var key;
    while (length > index) definePropertyModule.f(O, key = keys2[index++], props[key]);
    return O;
  };
  return objectDefineProperties;
}
var html;
var hasRequiredHtml;
function requireHtml() {
  if (hasRequiredHtml) return html;
  hasRequiredHtml = 1;
  var getBuiltIn2 = requireGetBuiltIn();
  html = getBuiltIn2("document", "documentElement");
  return html;
}
var sharedKey;
var hasRequiredSharedKey;
function requireSharedKey() {
  if (hasRequiredSharedKey) return sharedKey;
  hasRequiredSharedKey = 1;
  var shared2 = requireShared();
  var uid2 = requireUid();
  var keys2 = shared2("keys");
  sharedKey = function(key) {
    return keys2[key] || (keys2[key] = uid2(key));
  };
  return sharedKey;
}
var objectCreate;
var hasRequiredObjectCreate;
function requireObjectCreate() {
  if (hasRequiredObjectCreate) return objectCreate;
  hasRequiredObjectCreate = 1;
  var anObject2 = requireAnObject();
  var definePropertiesModule = requireObjectDefineProperties();
  var enumBugKeys2 = requireEnumBugKeys();
  var hiddenKeys2 = requireHiddenKeys();
  var html2 = requireHtml();
  var documentCreateElement2 = requireDocumentCreateElement();
  var sharedKey2 = requireSharedKey();
  var GT = ">";
  var LT = "<";
  var PROTOTYPE = "prototype";
  var SCRIPT = "script";
  var IE_PROTO = sharedKey2("IE_PROTO");
  var EmptyConstructor = function() {
  };
  var scriptTag = function(content) {
    return LT + SCRIPT + GT + content + LT + "/" + SCRIPT + GT;
  };
  var NullProtoObjectViaActiveX = function(activeXDocument2) {
    activeXDocument2.write(scriptTag(""));
    activeXDocument2.close();
    var temp = activeXDocument2.parentWindow.Object;
    activeXDocument2 = null;
    return temp;
  };
  var NullProtoObjectViaIFrame = function() {
    var iframe = documentCreateElement2("iframe");
    var JS = "java" + SCRIPT + ":";
    var iframeDocument;
    iframe.style.display = "none";
    html2.appendChild(iframe);
    iframe.src = String(JS);
    iframeDocument = iframe.contentWindow.document;
    iframeDocument.open();
    iframeDocument.write(scriptTag("document.F=Object"));
    iframeDocument.close();
    return iframeDocument.F;
  };
  var activeXDocument;
  var NullProtoObject = function() {
    try {
      activeXDocument = new ActiveXObject("htmlfile");
    } catch (error) {
    }
    NullProtoObject = typeof document != "undefined" ? document.domain && activeXDocument ? NullProtoObjectViaActiveX(activeXDocument) : NullProtoObjectViaIFrame() : NullProtoObjectViaActiveX(activeXDocument);
    var length = enumBugKeys2.length;
    while (length--) delete NullProtoObject[PROTOTYPE][enumBugKeys2[length]];
    return NullProtoObject();
  };
  hiddenKeys2[IE_PROTO] = true;
  objectCreate = Object.create || function create2(O, Properties) {
    var result;
    if (O !== null) {
      EmptyConstructor[PROTOTYPE] = anObject2(O);
      result = new EmptyConstructor();
      EmptyConstructor[PROTOTYPE] = null;
      result[IE_PROTO] = O;
    } else result = NullProtoObject();
    return Properties === void 0 ? result : definePropertiesModule.f(result, Properties);
  };
  return objectCreate;
}
var objectGetOwnPropertyNames = {};
var hasRequiredObjectGetOwnPropertyNames;
function requireObjectGetOwnPropertyNames() {
  if (hasRequiredObjectGetOwnPropertyNames) return objectGetOwnPropertyNames;
  hasRequiredObjectGetOwnPropertyNames = 1;
  var internalObjectKeys = requireObjectKeysInternal();
  var enumBugKeys2 = requireEnumBugKeys();
  var hiddenKeys2 = enumBugKeys2.concat("length", "prototype");
  objectGetOwnPropertyNames.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {
    return internalObjectKeys(O, hiddenKeys2);
  };
  return objectGetOwnPropertyNames;
}
var objectGetOwnPropertyNamesExternal = {};
var arraySlice;
var hasRequiredArraySlice;
function requireArraySlice() {
  if (hasRequiredArraySlice) return arraySlice;
  hasRequiredArraySlice = 1;
  var uncurryThis = requireFunctionUncurryThis();
  arraySlice = uncurryThis([].slice);
  return arraySlice;
}
var hasRequiredObjectGetOwnPropertyNamesExternal;
function requireObjectGetOwnPropertyNamesExternal() {
  if (hasRequiredObjectGetOwnPropertyNamesExternal) return objectGetOwnPropertyNamesExternal;
  hasRequiredObjectGetOwnPropertyNamesExternal = 1;
  var classof2 = requireClassofRaw();
  var toIndexedObject2 = requireToIndexedObject();
  var $getOwnPropertyNames = requireObjectGetOwnPropertyNames().f;
  var arraySlice2 = requireArraySlice();
  var windowNames = typeof window == "object" && window && Object.getOwnPropertyNames ? Object.getOwnPropertyNames(window) : [];
  var getWindowNames = function(it) {
    try {
      return $getOwnPropertyNames(it);
    } catch (error) {
      return arraySlice2(windowNames);
    }
  };
  objectGetOwnPropertyNamesExternal.f = function getOwnPropertyNames(it) {
    return windowNames && classof2(it) === "Window" ? getWindowNames(it) : $getOwnPropertyNames(toIndexedObject2(it));
  };
  return objectGetOwnPropertyNamesExternal;
}
var objectGetOwnPropertySymbols = {};
var hasRequiredObjectGetOwnPropertySymbols;
function requireObjectGetOwnPropertySymbols() {
  if (hasRequiredObjectGetOwnPropertySymbols) return objectGetOwnPropertySymbols;
  hasRequiredObjectGetOwnPropertySymbols = 1;
  objectGetOwnPropertySymbols.f = Object.getOwnPropertySymbols;
  return objectGetOwnPropertySymbols;
}
var defineBuiltIn;
var hasRequiredDefineBuiltIn;
function requireDefineBuiltIn() {
  if (hasRequiredDefineBuiltIn) return defineBuiltIn;
  hasRequiredDefineBuiltIn = 1;
  var createNonEnumerableProperty2 = requireCreateNonEnumerableProperty();
  defineBuiltIn = function(target, key, value, options) {
    if (options && options.enumerable) target[key] = value;
    else createNonEnumerableProperty2(target, key, value);
    return target;
  };
  return defineBuiltIn;
}
var defineBuiltInAccessor;
var hasRequiredDefineBuiltInAccessor;
function requireDefineBuiltInAccessor() {
  if (hasRequiredDefineBuiltInAccessor) return defineBuiltInAccessor;
  hasRequiredDefineBuiltInAccessor = 1;
  var defineProperty2 = requireObjectDefineProperty();
  defineBuiltInAccessor = function(target, name, descriptor) {
    return defineProperty2.f(target, name, descriptor);
  };
  return defineBuiltInAccessor;
}
var wellKnownSymbolWrapped = {};
var hasRequiredWellKnownSymbolWrapped;
function requireWellKnownSymbolWrapped() {
  if (hasRequiredWellKnownSymbolWrapped) return wellKnownSymbolWrapped;
  hasRequiredWellKnownSymbolWrapped = 1;
  var wellKnownSymbol2 = requireWellKnownSymbol();
  wellKnownSymbolWrapped.f = wellKnownSymbol2;
  return wellKnownSymbolWrapped;
}
var wellKnownSymbolDefine;
var hasRequiredWellKnownSymbolDefine;
function requireWellKnownSymbolDefine() {
  if (hasRequiredWellKnownSymbolDefine) return wellKnownSymbolDefine;
  hasRequiredWellKnownSymbolDefine = 1;
  var path2 = requirePath();
  var hasOwn = requireHasOwnProperty();
  var wrappedWellKnownSymbolModule = requireWellKnownSymbolWrapped();
  var defineProperty2 = requireObjectDefineProperty().f;
  wellKnownSymbolDefine = function(NAME) {
    var Symbol2 = path2.Symbol || (path2.Symbol = {});
    if (!hasOwn(Symbol2, NAME)) defineProperty2(Symbol2, NAME, {
      value: wrappedWellKnownSymbolModule.f(NAME)
    });
  };
  return wellKnownSymbolDefine;
}
var symbolDefineToPrimitive;
var hasRequiredSymbolDefineToPrimitive;
function requireSymbolDefineToPrimitive() {
  if (hasRequiredSymbolDefineToPrimitive) return symbolDefineToPrimitive;
  hasRequiredSymbolDefineToPrimitive = 1;
  var call = requireFunctionCall();
  var getBuiltIn2 = requireGetBuiltIn();
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var defineBuiltIn2 = requireDefineBuiltIn();
  symbolDefineToPrimitive = function() {
    var Symbol2 = getBuiltIn2("Symbol");
    var SymbolPrototype = Symbol2 && Symbol2.prototype;
    var valueOf = SymbolPrototype && SymbolPrototype.valueOf;
    var TO_PRIMITIVE = wellKnownSymbol2("toPrimitive");
    if (SymbolPrototype && !SymbolPrototype[TO_PRIMITIVE]) {
      defineBuiltIn2(SymbolPrototype, TO_PRIMITIVE, function(hint) {
        return call(valueOf, this);
      }, { arity: 1 });
    }
  };
  return symbolDefineToPrimitive;
}
var objectToString;
var hasRequiredObjectToString;
function requireObjectToString() {
  if (hasRequiredObjectToString) return objectToString;
  hasRequiredObjectToString = 1;
  var TO_STRING_TAG_SUPPORT = requireToStringTagSupport();
  var classof2 = requireClassof();
  objectToString = TO_STRING_TAG_SUPPORT ? {}.toString : function toString2() {
    return "[object " + classof2(this) + "]";
  };
  return objectToString;
}
var setToStringTag;
var hasRequiredSetToStringTag;
function requireSetToStringTag() {
  if (hasRequiredSetToStringTag) return setToStringTag;
  hasRequiredSetToStringTag = 1;
  var TO_STRING_TAG_SUPPORT = requireToStringTagSupport();
  var defineProperty2 = requireObjectDefineProperty().f;
  var createNonEnumerableProperty2 = requireCreateNonEnumerableProperty();
  var hasOwn = requireHasOwnProperty();
  var toString2 = requireObjectToString();
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var TO_STRING_TAG = wellKnownSymbol2("toStringTag");
  setToStringTag = function(it, TAG, STATIC, SET_METHOD) {
    var target = STATIC ? it : it && it.prototype;
    if (target) {
      if (!hasOwn(target, TO_STRING_TAG)) {
        defineProperty2(target, TO_STRING_TAG, { configurable: true, value: TAG });
      }
      if (SET_METHOD && !TO_STRING_TAG_SUPPORT) {
        createNonEnumerableProperty2(target, "toString", toString2);
      }
    }
  };
  return setToStringTag;
}
var weakMapBasicDetection;
var hasRequiredWeakMapBasicDetection;
function requireWeakMapBasicDetection() {
  if (hasRequiredWeakMapBasicDetection) return weakMapBasicDetection;
  hasRequiredWeakMapBasicDetection = 1;
  var globalThis2 = requireGlobalThis();
  var isCallable2 = requireIsCallable();
  var WeakMap = globalThis2.WeakMap;
  weakMapBasicDetection = isCallable2(WeakMap) && /native code/.test(String(WeakMap));
  return weakMapBasicDetection;
}
var internalState;
var hasRequiredInternalState;
function requireInternalState() {
  if (hasRequiredInternalState) return internalState;
  hasRequiredInternalState = 1;
  var NATIVE_WEAK_MAP = requireWeakMapBasicDetection();
  var globalThis2 = requireGlobalThis();
  var isObject2 = requireIsObject();
  var createNonEnumerableProperty2 = requireCreateNonEnumerableProperty();
  var hasOwn = requireHasOwnProperty();
  var shared2 = requireSharedStore();
  var sharedKey2 = requireSharedKey();
  var hiddenKeys2 = requireHiddenKeys();
  var OBJECT_ALREADY_INITIALIZED = "Object already initialized";
  var TypeError2 = globalThis2.TypeError;
  var WeakMap = globalThis2.WeakMap;
  var set2, get, has;
  var enforce = function(it) {
    return has(it) ? get(it) : set2(it, {});
  };
  var getterFor = function(TYPE) {
    return function(it) {
      var state;
      if (!isObject2(it) || (state = get(it)).type !== TYPE) {
        throw new TypeError2("Incompatible receiver, " + TYPE + " required");
      }
      return state;
    };
  };
  if (NATIVE_WEAK_MAP || shared2.state) {
    var store = shared2.state || (shared2.state = new WeakMap());
    store.get = store.get;
    store.has = store.has;
    store.set = store.set;
    set2 = function(it, metadata) {
      if (store.has(it)) throw new TypeError2(OBJECT_ALREADY_INITIALIZED);
      metadata.facade = it;
      store.set(it, metadata);
      return metadata;
    };
    get = function(it) {
      return store.get(it) || {};
    };
    has = function(it) {
      return store.has(it);
    };
  } else {
    var STATE = sharedKey2("state");
    hiddenKeys2[STATE] = true;
    set2 = function(it, metadata) {
      if (hasOwn(it, STATE)) throw new TypeError2(OBJECT_ALREADY_INITIALIZED);
      metadata.facade = it;
      createNonEnumerableProperty2(it, STATE, metadata);
      return metadata;
    };
    get = function(it) {
      return hasOwn(it, STATE) ? it[STATE] : {};
    };
    has = function(it) {
      return hasOwn(it, STATE);
    };
  }
  internalState = {
    set: set2,
    get,
    has,
    enforce,
    getterFor
  };
  return internalState;
}
var arrayIteration;
var hasRequiredArrayIteration;
function requireArrayIteration() {
  if (hasRequiredArrayIteration) return arrayIteration;
  hasRequiredArrayIteration = 1;
  var bind2 = requireFunctionBindContext();
  var uncurryThis = requireFunctionUncurryThis();
  var IndexedObject = requireIndexedObject();
  var toObject2 = requireToObject();
  var lengthOfArrayLike2 = requireLengthOfArrayLike();
  var arraySpeciesCreate2 = requireArraySpeciesCreate();
  var push = uncurryThis([].push);
  var createMethod = function(TYPE) {
    var IS_MAP = TYPE === 1;
    var IS_FILTER = TYPE === 2;
    var IS_SOME = TYPE === 3;
    var IS_EVERY = TYPE === 4;
    var IS_FIND_INDEX = TYPE === 6;
    var IS_FILTER_REJECT = TYPE === 7;
    var NO_HOLES = TYPE === 5 || IS_FIND_INDEX;
    return function($this, callbackfn, that, specificCreate) {
      var O = toObject2($this);
      var self2 = IndexedObject(O);
      var length = lengthOfArrayLike2(self2);
      var boundFunction = bind2(callbackfn, that);
      var index = 0;
      var create2 = specificCreate || arraySpeciesCreate2;
      var target = IS_MAP ? create2($this, length) : IS_FILTER || IS_FILTER_REJECT ? create2($this, 0) : void 0;
      var value, result;
      for (; length > index; index++) if (NO_HOLES || index in self2) {
        value = self2[index];
        result = boundFunction(value, index, O);
        if (TYPE) {
          if (IS_MAP) target[index] = result;
          else if (result) switch (TYPE) {
            case 3:
              return true;
            // some
            case 5:
              return value;
            // find
            case 6:
              return index;
            // findIndex
            case 2:
              push(target, value);
          }
          else switch (TYPE) {
            case 4:
              return false;
            // every
            case 7:
              push(target, value);
          }
        }
      }
      return IS_FIND_INDEX ? -1 : IS_SOME || IS_EVERY ? IS_EVERY : target;
    };
  };
  arrayIteration = {
    // `Array.prototype.forEach` method
    // https://tc39.es/ecma262/#sec-array.prototype.foreach
    forEach: createMethod(0),
    // `Array.prototype.map` method
    // https://tc39.es/ecma262/#sec-array.prototype.map
    map: createMethod(1),
    // `Array.prototype.filter` method
    // https://tc39.es/ecma262/#sec-array.prototype.filter
    filter: createMethod(2),
    // `Array.prototype.some` method
    // https://tc39.es/ecma262/#sec-array.prototype.some
    some: createMethod(3),
    // `Array.prototype.every` method
    // https://tc39.es/ecma262/#sec-array.prototype.every
    every: createMethod(4),
    // `Array.prototype.find` method
    // https://tc39.es/ecma262/#sec-array.prototype.find
    find: createMethod(5),
    // `Array.prototype.findIndex` method
    // https://tc39.es/ecma262/#sec-array.prototype.findIndex
    findIndex: createMethod(6),
    // `Array.prototype.filterReject` method
    // https://github.com/tc39/proposal-array-filtering
    filterReject: createMethod(7)
  };
  return arrayIteration;
}
var hasRequiredEs_symbol_constructor;
function requireEs_symbol_constructor() {
  if (hasRequiredEs_symbol_constructor) return es_symbol_constructor;
  hasRequiredEs_symbol_constructor = 1;
  var $ = require_export();
  var globalThis2 = requireGlobalThis();
  var call = requireFunctionCall();
  var uncurryThis = requireFunctionUncurryThis();
  var IS_PURE = requireIsPure();
  var DESCRIPTORS = requireDescriptors();
  var NATIVE_SYMBOL = requireSymbolConstructorDetection();
  var fails2 = requireFails();
  var hasOwn = requireHasOwnProperty();
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var anObject2 = requireAnObject();
  var toIndexedObject2 = requireToIndexedObject();
  var toPropertyKey2 = requireToPropertyKey();
  var $toString = requireToString();
  var createPropertyDescriptor2 = requireCreatePropertyDescriptor();
  var nativeObjectCreate = requireObjectCreate();
  var objectKeys2 = requireObjectKeys();
  var getOwnPropertyNamesModule = requireObjectGetOwnPropertyNames();
  var getOwnPropertyNamesExternal = requireObjectGetOwnPropertyNamesExternal();
  var getOwnPropertySymbolsModule = requireObjectGetOwnPropertySymbols();
  var getOwnPropertyDescriptorModule = requireObjectGetOwnPropertyDescriptor();
  var definePropertyModule = requireObjectDefineProperty();
  var definePropertiesModule = requireObjectDefineProperties();
  var propertyIsEnumerableModule = requireObjectPropertyIsEnumerable();
  var defineBuiltIn2 = requireDefineBuiltIn();
  var defineBuiltInAccessor2 = requireDefineBuiltInAccessor();
  var shared2 = requireShared();
  var sharedKey2 = requireSharedKey();
  var hiddenKeys2 = requireHiddenKeys();
  var uid2 = requireUid();
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var wrappedWellKnownSymbolModule = requireWellKnownSymbolWrapped();
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  var defineSymbolToPrimitive = requireSymbolDefineToPrimitive();
  var setToStringTag2 = requireSetToStringTag();
  var InternalStateModule = requireInternalState();
  var $forEach = requireArrayIteration().forEach;
  var HIDDEN = sharedKey2("hidden");
  var SYMBOL = "Symbol";
  var PROTOTYPE = "prototype";
  var setInternalState = InternalStateModule.set;
  var getInternalState = InternalStateModule.getterFor(SYMBOL);
  var ObjectPrototype = Object[PROTOTYPE];
  var $Symbol = globalThis2.Symbol;
  var SymbolPrototype = $Symbol && $Symbol[PROTOTYPE];
  var RangeError2 = globalThis2.RangeError;
  var TypeError2 = globalThis2.TypeError;
  var QObject = globalThis2.QObject;
  var nativeGetOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;
  var nativeDefineProperty = definePropertyModule.f;
  var nativeGetOwnPropertyNames = getOwnPropertyNamesExternal.f;
  var nativePropertyIsEnumerable = propertyIsEnumerableModule.f;
  var push = uncurryThis([].push);
  var AllSymbols = shared2("symbols");
  var ObjectPrototypeSymbols = shared2("op-symbols");
  var WellKnownSymbolsStore = shared2("wks");
  var USE_SETTER = !QObject || !QObject[PROTOTYPE] || !QObject[PROTOTYPE].findChild;
  var fallbackDefineProperty = function(O, P, Attributes) {
    var ObjectPrototypeDescriptor = nativeGetOwnPropertyDescriptor(ObjectPrototype, P);
    if (ObjectPrototypeDescriptor) delete ObjectPrototype[P];
    nativeDefineProperty(O, P, Attributes);
    if (ObjectPrototypeDescriptor && O !== ObjectPrototype) {
      nativeDefineProperty(ObjectPrototype, P, ObjectPrototypeDescriptor);
    }
  };
  var setSymbolDescriptor = DESCRIPTORS && fails2(function() {
    return nativeObjectCreate(nativeDefineProperty({}, "a", {
      get: function() {
        return nativeDefineProperty(this, "a", { value: 7 }).a;
      }
    })).a !== 7;
  }) ? fallbackDefineProperty : nativeDefineProperty;
  var wrap = function(tag, description) {
    var symbol2 = AllSymbols[tag] = nativeObjectCreate(SymbolPrototype);
    setInternalState(symbol2, {
      type: SYMBOL,
      tag,
      description
    });
    if (!DESCRIPTORS) symbol2.description = description;
    return symbol2;
  };
  var $defineProperty = function defineProperty2(O, P, Attributes) {
    if (O === ObjectPrototype) $defineProperty(ObjectPrototypeSymbols, P, Attributes);
    anObject2(O);
    var key = toPropertyKey2(P);
    anObject2(Attributes);
    if (hasOwn(AllSymbols, key)) {
      if (!Attributes.enumerable) {
        if (!hasOwn(O, HIDDEN)) nativeDefineProperty(O, HIDDEN, createPropertyDescriptor2(1, nativeObjectCreate(null)));
        O[HIDDEN][key] = true;
      } else {
        if (hasOwn(O, HIDDEN) && O[HIDDEN][key]) O[HIDDEN][key] = false;
        Attributes = nativeObjectCreate(Attributes, { enumerable: createPropertyDescriptor2(0, false) });
      }
      return setSymbolDescriptor(O, key, Attributes);
    }
    return nativeDefineProperty(O, key, Attributes);
  };
  var $defineProperties = function defineProperties2(O, Properties) {
    anObject2(O);
    var properties = toIndexedObject2(Properties);
    var keys2 = objectKeys2(properties).concat($getOwnPropertySymbols(properties));
    $forEach(keys2, function(key) {
      if (!DESCRIPTORS || call($propertyIsEnumerable, properties, key)) $defineProperty(O, key, properties[key]);
    });
    return O;
  };
  var $create = function create2(O, Properties) {
    return Properties === void 0 ? nativeObjectCreate(O) : $defineProperties(nativeObjectCreate(O), Properties);
  };
  var $propertyIsEnumerable = function propertyIsEnumerable(V) {
    var P = toPropertyKey2(V);
    var enumerable = call(nativePropertyIsEnumerable, this, P);
    if (this === ObjectPrototype && hasOwn(AllSymbols, P) && !hasOwn(ObjectPrototypeSymbols, P)) return false;
    return enumerable || !hasOwn(this, P) || !hasOwn(AllSymbols, P) || hasOwn(this, HIDDEN) && this[HIDDEN][P] ? enumerable : true;
  };
  var $getOwnPropertyDescriptor = function getOwnPropertyDescriptor2(O, P) {
    var it = toIndexedObject2(O);
    var key = toPropertyKey2(P);
    if (it === ObjectPrototype && hasOwn(AllSymbols, key) && !hasOwn(ObjectPrototypeSymbols, key)) return;
    var descriptor = nativeGetOwnPropertyDescriptor(it, key);
    if (descriptor && hasOwn(AllSymbols, key) && !(hasOwn(it, HIDDEN) && it[HIDDEN][key])) {
      descriptor.enumerable = true;
    }
    return descriptor;
  };
  var $getOwnPropertyNames = function getOwnPropertyNames(O) {
    var names = nativeGetOwnPropertyNames(toIndexedObject2(O));
    var result = [];
    $forEach(names, function(key) {
      if (!hasOwn(AllSymbols, key) && !hasOwn(hiddenKeys2, key)) push(result, key);
    });
    return result;
  };
  var $getOwnPropertySymbols = function(O) {
    var IS_OBJECT_PROTOTYPE = O === ObjectPrototype;
    var names = nativeGetOwnPropertyNames(IS_OBJECT_PROTOTYPE ? ObjectPrototypeSymbols : toIndexedObject2(O));
    var result = [];
    $forEach(names, function(key) {
      if (hasOwn(AllSymbols, key) && (!IS_OBJECT_PROTOTYPE || hasOwn(ObjectPrototype, key))) {
        push(result, AllSymbols[key]);
      }
    });
    return result;
  };
  if (!NATIVE_SYMBOL) {
    $Symbol = function Symbol2() {
      if (isPrototypeOf(SymbolPrototype, this)) throw new TypeError2("Symbol is not a constructor");
      var description = !arguments.length || arguments[0] === void 0 ? void 0 : $toString(arguments[0]);
      var tag = uid2(description);
      var setter = function(value) {
        var $this = this === void 0 ? globalThis2 : this;
        if ($this === ObjectPrototype) call(setter, ObjectPrototypeSymbols, value);
        if (hasOwn($this, HIDDEN) && hasOwn($this[HIDDEN], tag)) $this[HIDDEN][tag] = false;
        var descriptor = createPropertyDescriptor2(1, value);
        try {
          setSymbolDescriptor($this, tag, descriptor);
        } catch (error) {
          if (!(error instanceof RangeError2)) throw error;
          fallbackDefineProperty($this, tag, descriptor);
        }
      };
      if (DESCRIPTORS && USE_SETTER) setSymbolDescriptor(ObjectPrototype, tag, { configurable: true, set: setter });
      return wrap(tag, description);
    };
    SymbolPrototype = $Symbol[PROTOTYPE];
    defineBuiltIn2(SymbolPrototype, "toString", function toString2() {
      return getInternalState(this).tag;
    });
    defineBuiltIn2($Symbol, "withoutSetter", function(description) {
      return wrap(uid2(description), description);
    });
    propertyIsEnumerableModule.f = $propertyIsEnumerable;
    definePropertyModule.f = $defineProperty;
    definePropertiesModule.f = $defineProperties;
    getOwnPropertyDescriptorModule.f = $getOwnPropertyDescriptor;
    getOwnPropertyNamesModule.f = getOwnPropertyNamesExternal.f = $getOwnPropertyNames;
    getOwnPropertySymbolsModule.f = $getOwnPropertySymbols;
    wrappedWellKnownSymbolModule.f = function(name) {
      return wrap(wellKnownSymbol2(name), name);
    };
    if (DESCRIPTORS) {
      defineBuiltInAccessor2(SymbolPrototype, "description", {
        configurable: true,
        get: function description() {
          return getInternalState(this).description;
        }
      });
      if (!IS_PURE) {
        defineBuiltIn2(ObjectPrototype, "propertyIsEnumerable", $propertyIsEnumerable, { unsafe: true });
      }
    }
  }
  $({ global: true, constructor: true, wrap: true, forced: !NATIVE_SYMBOL, sham: !NATIVE_SYMBOL }, {
    Symbol: $Symbol
  });
  $forEach(objectKeys2(WellKnownSymbolsStore), function(name) {
    defineWellKnownSymbol(name);
  });
  $({ target: SYMBOL, stat: true, forced: !NATIVE_SYMBOL }, {
    useSetter: function() {
      USE_SETTER = true;
    },
    useSimple: function() {
      USE_SETTER = false;
    }
  });
  $({ target: "Object", stat: true, forced: !NATIVE_SYMBOL, sham: !DESCRIPTORS }, {
    // `Object.create` method
    // https://tc39.es/ecma262/#sec-object.create
    create: $create,
    // `Object.defineProperty` method
    // https://tc39.es/ecma262/#sec-object.defineproperty
    defineProperty: $defineProperty,
    // `Object.defineProperties` method
    // https://tc39.es/ecma262/#sec-object.defineproperties
    defineProperties: $defineProperties,
    // `Object.getOwnPropertyDescriptor` method
    // https://tc39.es/ecma262/#sec-object.getownpropertydescriptors
    getOwnPropertyDescriptor: $getOwnPropertyDescriptor
  });
  $({ target: "Object", stat: true, forced: !NATIVE_SYMBOL }, {
    // `Object.getOwnPropertyNames` method
    // https://tc39.es/ecma262/#sec-object.getownpropertynames
    getOwnPropertyNames: $getOwnPropertyNames
  });
  defineSymbolToPrimitive();
  setToStringTag2($Symbol, SYMBOL);
  hiddenKeys2[HIDDEN] = true;
  return es_symbol_constructor;
}
var es_symbol_for = {};
var symbolRegistryDetection;
var hasRequiredSymbolRegistryDetection;
function requireSymbolRegistryDetection() {
  if (hasRequiredSymbolRegistryDetection) return symbolRegistryDetection;
  hasRequiredSymbolRegistryDetection = 1;
  var NATIVE_SYMBOL = requireSymbolConstructorDetection();
  symbolRegistryDetection = NATIVE_SYMBOL && !!Symbol["for"] && !!Symbol.keyFor;
  return symbolRegistryDetection;
}
var hasRequiredEs_symbol_for;
function requireEs_symbol_for() {
  if (hasRequiredEs_symbol_for) return es_symbol_for;
  hasRequiredEs_symbol_for = 1;
  var $ = require_export();
  var getBuiltIn2 = requireGetBuiltIn();
  var hasOwn = requireHasOwnProperty();
  var toString2 = requireToString();
  var shared2 = requireShared();
  var NATIVE_SYMBOL_REGISTRY = requireSymbolRegistryDetection();
  var StringToSymbolRegistry = shared2("string-to-symbol-registry");
  var SymbolToStringRegistry = shared2("symbol-to-string-registry");
  $({ target: "Symbol", stat: true, forced: !NATIVE_SYMBOL_REGISTRY }, {
    "for": function(key) {
      var string = toString2(key);
      if (hasOwn(StringToSymbolRegistry, string)) return StringToSymbolRegistry[string];
      var symbol2 = getBuiltIn2("Symbol")(string);
      StringToSymbolRegistry[string] = symbol2;
      SymbolToStringRegistry[symbol2] = string;
      return symbol2;
    }
  });
  return es_symbol_for;
}
var es_symbol_keyFor = {};
var hasRequiredEs_symbol_keyFor;
function requireEs_symbol_keyFor() {
  if (hasRequiredEs_symbol_keyFor) return es_symbol_keyFor;
  hasRequiredEs_symbol_keyFor = 1;
  var $ = require_export();
  var hasOwn = requireHasOwnProperty();
  var isSymbol2 = requireIsSymbol();
  var tryToString2 = requireTryToString();
  var shared2 = requireShared();
  var NATIVE_SYMBOL_REGISTRY = requireSymbolRegistryDetection();
  var SymbolToStringRegistry = shared2("symbol-to-string-registry");
  $({ target: "Symbol", stat: true, forced: !NATIVE_SYMBOL_REGISTRY }, {
    keyFor: function keyFor(sym) {
      if (!isSymbol2(sym)) throw new TypeError(tryToString2(sym) + " is not a symbol");
      if (hasOwn(SymbolToStringRegistry, sym)) return SymbolToStringRegistry[sym];
    }
  });
  return es_symbol_keyFor;
}
var es_json_stringify = {};
var getJsonReplacerFunction;
var hasRequiredGetJsonReplacerFunction;
function requireGetJsonReplacerFunction() {
  if (hasRequiredGetJsonReplacerFunction) return getJsonReplacerFunction;
  hasRequiredGetJsonReplacerFunction = 1;
  var uncurryThis = requireFunctionUncurryThis();
  var isArray2 = requireIsArray$3();
  var isCallable2 = requireIsCallable();
  var classof2 = requireClassofRaw();
  var toString2 = requireToString();
  var push = uncurryThis([].push);
  getJsonReplacerFunction = function(replacer) {
    if (isCallable2(replacer)) return replacer;
    if (!isArray2(replacer)) return;
    var rawLength = replacer.length;
    var keys2 = [];
    for (var i = 0; i < rawLength; i++) {
      var element = replacer[i];
      if (typeof element == "string") push(keys2, element);
      else if (typeof element == "number" || classof2(element) === "Number" || classof2(element) === "String") push(keys2, toString2(element));
    }
    var keysLength = keys2.length;
    var root = true;
    return function(key, value) {
      if (root) {
        root = false;
        return value;
      }
      if (isArray2(this)) return value;
      for (var j = 0; j < keysLength; j++) if (keys2[j] === key) return value;
    };
  };
  return getJsonReplacerFunction;
}
var hasRequiredEs_json_stringify;
function requireEs_json_stringify() {
  if (hasRequiredEs_json_stringify) return es_json_stringify;
  hasRequiredEs_json_stringify = 1;
  var $ = require_export();
  var getBuiltIn2 = requireGetBuiltIn();
  var apply = requireFunctionApply();
  var call = requireFunctionCall();
  var uncurryThis = requireFunctionUncurryThis();
  var fails2 = requireFails();
  var isCallable2 = requireIsCallable();
  var isSymbol2 = requireIsSymbol();
  var arraySlice2 = requireArraySlice();
  var getReplacerFunction = requireGetJsonReplacerFunction();
  var NATIVE_SYMBOL = requireSymbolConstructorDetection();
  var $String = String;
  var $stringify = getBuiltIn2("JSON", "stringify");
  var exec = uncurryThis(/./.exec);
  var charAt = uncurryThis("".charAt);
  var charCodeAt = uncurryThis("".charCodeAt);
  var replace = uncurryThis("".replace);
  var numberToString = uncurryThis(1.1.toString);
  var tester = /[\uD800-\uDFFF]/g;
  var low = /^[\uD800-\uDBFF]$/;
  var hi = /^[\uDC00-\uDFFF]$/;
  var WRONG_SYMBOLS_CONVERSION = !NATIVE_SYMBOL || fails2(function() {
    var symbol2 = getBuiltIn2("Symbol")("stringify detection");
    return $stringify([symbol2]) !== "[null]" || $stringify({ a: symbol2 }) !== "{}" || $stringify(Object(symbol2)) !== "{}";
  });
  var ILL_FORMED_UNICODE = fails2(function() {
    return $stringify("\uDF06\uD834") !== '"\\udf06\\ud834"' || $stringify("\uDEAD") !== '"\\udead"';
  });
  var stringifyWithSymbolsFix = function(it, replacer) {
    var args = arraySlice2(arguments);
    var $replacer = getReplacerFunction(replacer);
    if (!isCallable2($replacer) && (it === void 0 || isSymbol2(it))) return;
    args[1] = function(key, value) {
      if (isCallable2($replacer)) value = call($replacer, this, $String(key), value);
      if (!isSymbol2(value)) return value;
    };
    return apply($stringify, null, args);
  };
  var fixIllFormed = function(match, offset, string) {
    var prev = charAt(string, offset - 1);
    var next = charAt(string, offset + 1);
    if (exec(low, match) && !exec(hi, next) || exec(hi, match) && !exec(low, prev)) {
      return "\\u" + numberToString(charCodeAt(match, 0), 16);
    }
    return match;
  };
  if ($stringify) {
    $({ target: "JSON", stat: true, arity: 3, forced: WRONG_SYMBOLS_CONVERSION || ILL_FORMED_UNICODE }, {
      // eslint-disable-next-line no-unused-vars -- required for `.length`
      stringify: function stringify2(it, replacer, space) {
        var args = arraySlice2(arguments);
        var result = apply(WRONG_SYMBOLS_CONVERSION ? stringifyWithSymbolsFix : $stringify, null, args);
        return ILL_FORMED_UNICODE && typeof result == "string" ? replace(result, tester, fixIllFormed) : result;
      }
    });
  }
  return es_json_stringify;
}
var es_object_getOwnPropertySymbols = {};
var hasRequiredEs_object_getOwnPropertySymbols;
function requireEs_object_getOwnPropertySymbols() {
  if (hasRequiredEs_object_getOwnPropertySymbols) return es_object_getOwnPropertySymbols;
  hasRequiredEs_object_getOwnPropertySymbols = 1;
  var $ = require_export();
  var NATIVE_SYMBOL = requireSymbolConstructorDetection();
  var fails2 = requireFails();
  var getOwnPropertySymbolsModule = requireObjectGetOwnPropertySymbols();
  var toObject2 = requireToObject();
  var FORCED = !NATIVE_SYMBOL || fails2(function() {
    getOwnPropertySymbolsModule.f(1);
  });
  $({ target: "Object", stat: true, forced: FORCED }, {
    getOwnPropertySymbols: function getOwnPropertySymbols2(it) {
      var $getOwnPropertySymbols = getOwnPropertySymbolsModule.f;
      return $getOwnPropertySymbols ? $getOwnPropertySymbols(toObject2(it)) : [];
    }
  });
  return es_object_getOwnPropertySymbols;
}
var hasRequiredEs_symbol;
function requireEs_symbol() {
  if (hasRequiredEs_symbol) return es_symbol;
  hasRequiredEs_symbol = 1;
  requireEs_symbol_constructor();
  requireEs_symbol_for();
  requireEs_symbol_keyFor();
  requireEs_json_stringify();
  requireEs_object_getOwnPropertySymbols();
  return es_symbol;
}
var es_symbol_asyncDispose = {};
var hasRequiredEs_symbol_asyncDispose;
function requireEs_symbol_asyncDispose() {
  if (hasRequiredEs_symbol_asyncDispose) return es_symbol_asyncDispose;
  hasRequiredEs_symbol_asyncDispose = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("asyncDispose");
  return es_symbol_asyncDispose;
}
var es_symbol_asyncIterator = {};
var hasRequiredEs_symbol_asyncIterator;
function requireEs_symbol_asyncIterator() {
  if (hasRequiredEs_symbol_asyncIterator) return es_symbol_asyncIterator;
  hasRequiredEs_symbol_asyncIterator = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("asyncIterator");
  return es_symbol_asyncIterator;
}
var es_symbol_dispose = {};
var hasRequiredEs_symbol_dispose;
function requireEs_symbol_dispose() {
  if (hasRequiredEs_symbol_dispose) return es_symbol_dispose;
  hasRequiredEs_symbol_dispose = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("dispose");
  return es_symbol_dispose;
}
var es_symbol_hasInstance = {};
var hasRequiredEs_symbol_hasInstance;
function requireEs_symbol_hasInstance() {
  if (hasRequiredEs_symbol_hasInstance) return es_symbol_hasInstance;
  hasRequiredEs_symbol_hasInstance = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("hasInstance");
  return es_symbol_hasInstance;
}
var es_symbol_isConcatSpreadable = {};
var hasRequiredEs_symbol_isConcatSpreadable;
function requireEs_symbol_isConcatSpreadable() {
  if (hasRequiredEs_symbol_isConcatSpreadable) return es_symbol_isConcatSpreadable;
  hasRequiredEs_symbol_isConcatSpreadable = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("isConcatSpreadable");
  return es_symbol_isConcatSpreadable;
}
var es_symbol_iterator = {};
var hasRequiredEs_symbol_iterator;
function requireEs_symbol_iterator() {
  if (hasRequiredEs_symbol_iterator) return es_symbol_iterator;
  hasRequiredEs_symbol_iterator = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("iterator");
  return es_symbol_iterator;
}
var es_symbol_match = {};
var hasRequiredEs_symbol_match;
function requireEs_symbol_match() {
  if (hasRequiredEs_symbol_match) return es_symbol_match;
  hasRequiredEs_symbol_match = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("match");
  return es_symbol_match;
}
var es_symbol_matchAll = {};
var hasRequiredEs_symbol_matchAll;
function requireEs_symbol_matchAll() {
  if (hasRequiredEs_symbol_matchAll) return es_symbol_matchAll;
  hasRequiredEs_symbol_matchAll = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("matchAll");
  return es_symbol_matchAll;
}
var es_symbol_replace = {};
var hasRequiredEs_symbol_replace;
function requireEs_symbol_replace() {
  if (hasRequiredEs_symbol_replace) return es_symbol_replace;
  hasRequiredEs_symbol_replace = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("replace");
  return es_symbol_replace;
}
var es_symbol_search = {};
var hasRequiredEs_symbol_search;
function requireEs_symbol_search() {
  if (hasRequiredEs_symbol_search) return es_symbol_search;
  hasRequiredEs_symbol_search = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("search");
  return es_symbol_search;
}
var es_symbol_species = {};
var hasRequiredEs_symbol_species;
function requireEs_symbol_species() {
  if (hasRequiredEs_symbol_species) return es_symbol_species;
  hasRequiredEs_symbol_species = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("species");
  return es_symbol_species;
}
var es_symbol_split = {};
var hasRequiredEs_symbol_split;
function requireEs_symbol_split() {
  if (hasRequiredEs_symbol_split) return es_symbol_split;
  hasRequiredEs_symbol_split = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("split");
  return es_symbol_split;
}
var es_symbol_toPrimitive = {};
var hasRequiredEs_symbol_toPrimitive;
function requireEs_symbol_toPrimitive() {
  if (hasRequiredEs_symbol_toPrimitive) return es_symbol_toPrimitive;
  hasRequiredEs_symbol_toPrimitive = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  var defineSymbolToPrimitive = requireSymbolDefineToPrimitive();
  defineWellKnownSymbol("toPrimitive");
  defineSymbolToPrimitive();
  return es_symbol_toPrimitive;
}
var es_symbol_toStringTag = {};
var hasRequiredEs_symbol_toStringTag;
function requireEs_symbol_toStringTag() {
  if (hasRequiredEs_symbol_toStringTag) return es_symbol_toStringTag;
  hasRequiredEs_symbol_toStringTag = 1;
  var getBuiltIn2 = requireGetBuiltIn();
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  var setToStringTag2 = requireSetToStringTag();
  defineWellKnownSymbol("toStringTag");
  setToStringTag2(getBuiltIn2("Symbol"), "Symbol");
  return es_symbol_toStringTag;
}
var es_symbol_unscopables = {};
var hasRequiredEs_symbol_unscopables;
function requireEs_symbol_unscopables() {
  if (hasRequiredEs_symbol_unscopables) return es_symbol_unscopables;
  hasRequiredEs_symbol_unscopables = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("unscopables");
  return es_symbol_unscopables;
}
var es_json_toStringTag = {};
var hasRequiredEs_json_toStringTag;
function requireEs_json_toStringTag() {
  if (hasRequiredEs_json_toStringTag) return es_json_toStringTag;
  hasRequiredEs_json_toStringTag = 1;
  var globalThis2 = requireGlobalThis();
  var setToStringTag2 = requireSetToStringTag();
  setToStringTag2(globalThis2.JSON, "JSON", true);
  return es_json_toStringTag;
}
var symbol$4;
var hasRequiredSymbol$4;
function requireSymbol$4() {
  if (hasRequiredSymbol$4) return symbol$4;
  hasRequiredSymbol$4 = 1;
  requireEs_array_concat();
  requireEs_symbol();
  requireEs_symbol_asyncDispose();
  requireEs_symbol_asyncIterator();
  requireEs_symbol_dispose();
  requireEs_symbol_hasInstance();
  requireEs_symbol_isConcatSpreadable();
  requireEs_symbol_iterator();
  requireEs_symbol_match();
  requireEs_symbol_matchAll();
  requireEs_symbol_replace();
  requireEs_symbol_search();
  requireEs_symbol_species();
  requireEs_symbol_split();
  requireEs_symbol_toPrimitive();
  requireEs_symbol_toStringTag();
  requireEs_symbol_unscopables();
  requireEs_json_toStringTag();
  var path2 = requirePath();
  symbol$4 = path2.Symbol;
  return symbol$4;
}
var web_domCollections_iterator = {};
var addToUnscopables;
var hasRequiredAddToUnscopables;
function requireAddToUnscopables() {
  if (hasRequiredAddToUnscopables) return addToUnscopables;
  hasRequiredAddToUnscopables = 1;
  addToUnscopables = function() {
  };
  return addToUnscopables;
}
var iterators;
var hasRequiredIterators;
function requireIterators() {
  if (hasRequiredIterators) return iterators;
  hasRequiredIterators = 1;
  iterators = {};
  return iterators;
}
var functionName;
var hasRequiredFunctionName;
function requireFunctionName() {
  if (hasRequiredFunctionName) return functionName;
  hasRequiredFunctionName = 1;
  var DESCRIPTORS = requireDescriptors();
  var hasOwn = requireHasOwnProperty();
  var FunctionPrototype = Function.prototype;
  var getDescriptor = DESCRIPTORS && Object.getOwnPropertyDescriptor;
  var EXISTS = hasOwn(FunctionPrototype, "name");
  var PROPER = EXISTS && (function something() {
  }).name === "something";
  var CONFIGURABLE = EXISTS && (!DESCRIPTORS || DESCRIPTORS && getDescriptor(FunctionPrototype, "name").configurable);
  functionName = {
    EXISTS,
    PROPER,
    CONFIGURABLE
  };
  return functionName;
}
var correctPrototypeGetter;
var hasRequiredCorrectPrototypeGetter;
function requireCorrectPrototypeGetter() {
  if (hasRequiredCorrectPrototypeGetter) return correctPrototypeGetter;
  hasRequiredCorrectPrototypeGetter = 1;
  var fails2 = requireFails();
  correctPrototypeGetter = !fails2(function() {
    function F() {
    }
    F.prototype.constructor = null;
    return Object.getPrototypeOf(new F()) !== F.prototype;
  });
  return correctPrototypeGetter;
}
var objectGetPrototypeOf;
var hasRequiredObjectGetPrototypeOf;
function requireObjectGetPrototypeOf() {
  if (hasRequiredObjectGetPrototypeOf) return objectGetPrototypeOf;
  hasRequiredObjectGetPrototypeOf = 1;
  var hasOwn = requireHasOwnProperty();
  var isCallable2 = requireIsCallable();
  var toObject2 = requireToObject();
  var sharedKey2 = requireSharedKey();
  var CORRECT_PROTOTYPE_GETTER = requireCorrectPrototypeGetter();
  var IE_PROTO = sharedKey2("IE_PROTO");
  var $Object = Object;
  var ObjectPrototype = $Object.prototype;
  objectGetPrototypeOf = CORRECT_PROTOTYPE_GETTER ? $Object.getPrototypeOf : function(O) {
    var object = toObject2(O);
    if (hasOwn(object, IE_PROTO)) return object[IE_PROTO];
    var constructor = object.constructor;
    if (isCallable2(constructor) && object instanceof constructor) {
      return constructor.prototype;
    }
    return object instanceof $Object ? ObjectPrototype : null;
  };
  return objectGetPrototypeOf;
}
var iteratorsCore;
var hasRequiredIteratorsCore;
function requireIteratorsCore() {
  if (hasRequiredIteratorsCore) return iteratorsCore;
  hasRequiredIteratorsCore = 1;
  var fails2 = requireFails();
  var isCallable2 = requireIsCallable();
  var isObject2 = requireIsObject();
  var create2 = requireObjectCreate();
  var getPrototypeOf = requireObjectGetPrototypeOf();
  var defineBuiltIn2 = requireDefineBuiltIn();
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var IS_PURE = requireIsPure();
  var ITERATOR = wellKnownSymbol2("iterator");
  var BUGGY_SAFARI_ITERATORS = false;
  var IteratorPrototype, PrototypeOfArrayIteratorPrototype, arrayIterator;
  if ([].keys) {
    arrayIterator = [].keys();
    if (!("next" in arrayIterator)) BUGGY_SAFARI_ITERATORS = true;
    else {
      PrototypeOfArrayIteratorPrototype = getPrototypeOf(getPrototypeOf(arrayIterator));
      if (PrototypeOfArrayIteratorPrototype !== Object.prototype) IteratorPrototype = PrototypeOfArrayIteratorPrototype;
    }
  }
  var NEW_ITERATOR_PROTOTYPE = !isObject2(IteratorPrototype) || fails2(function() {
    var test = {};
    return IteratorPrototype[ITERATOR].call(test) !== test;
  });
  if (NEW_ITERATOR_PROTOTYPE) IteratorPrototype = {};
  else if (IS_PURE) IteratorPrototype = create2(IteratorPrototype);
  if (!isCallable2(IteratorPrototype[ITERATOR])) {
    defineBuiltIn2(IteratorPrototype, ITERATOR, function() {
      return this;
    });
  }
  iteratorsCore = {
    IteratorPrototype,
    BUGGY_SAFARI_ITERATORS
  };
  return iteratorsCore;
}
var iteratorCreateConstructor;
var hasRequiredIteratorCreateConstructor;
function requireIteratorCreateConstructor() {
  if (hasRequiredIteratorCreateConstructor) return iteratorCreateConstructor;
  hasRequiredIteratorCreateConstructor = 1;
  var IteratorPrototype = requireIteratorsCore().IteratorPrototype;
  var create2 = requireObjectCreate();
  var createPropertyDescriptor2 = requireCreatePropertyDescriptor();
  var setToStringTag2 = requireSetToStringTag();
  var Iterators = requireIterators();
  var returnThis = function() {
    return this;
  };
  iteratorCreateConstructor = function(IteratorConstructor, NAME, next, ENUMERABLE_NEXT) {
    var TO_STRING_TAG = NAME + " Iterator";
    IteratorConstructor.prototype = create2(IteratorPrototype, { next: createPropertyDescriptor2(+!ENUMERABLE_NEXT, next) });
    setToStringTag2(IteratorConstructor, TO_STRING_TAG, false, true);
    Iterators[TO_STRING_TAG] = returnThis;
    return IteratorConstructor;
  };
  return iteratorCreateConstructor;
}
var functionUncurryThisAccessor;
var hasRequiredFunctionUncurryThisAccessor;
function requireFunctionUncurryThisAccessor() {
  if (hasRequiredFunctionUncurryThisAccessor) return functionUncurryThisAccessor;
  hasRequiredFunctionUncurryThisAccessor = 1;
  var uncurryThis = requireFunctionUncurryThis();
  var aCallable2 = requireACallable();
  functionUncurryThisAccessor = function(object, key, method) {
    try {
      return uncurryThis(aCallable2(Object.getOwnPropertyDescriptor(object, key)[method]));
    } catch (error) {
    }
  };
  return functionUncurryThisAccessor;
}
var isPossiblePrototype;
var hasRequiredIsPossiblePrototype;
function requireIsPossiblePrototype() {
  if (hasRequiredIsPossiblePrototype) return isPossiblePrototype;
  hasRequiredIsPossiblePrototype = 1;
  var isObject2 = requireIsObject();
  isPossiblePrototype = function(argument) {
    return isObject2(argument) || argument === null;
  };
  return isPossiblePrototype;
}
var aPossiblePrototype;
var hasRequiredAPossiblePrototype;
function requireAPossiblePrototype() {
  if (hasRequiredAPossiblePrototype) return aPossiblePrototype;
  hasRequiredAPossiblePrototype = 1;
  var isPossiblePrototype2 = requireIsPossiblePrototype();
  var $String = String;
  var $TypeError = TypeError;
  aPossiblePrototype = function(argument) {
    if (isPossiblePrototype2(argument)) return argument;
    throw new $TypeError("Can't set " + $String(argument) + " as a prototype");
  };
  return aPossiblePrototype;
}
var objectSetPrototypeOf;
var hasRequiredObjectSetPrototypeOf;
function requireObjectSetPrototypeOf() {
  if (hasRequiredObjectSetPrototypeOf) return objectSetPrototypeOf;
  hasRequiredObjectSetPrototypeOf = 1;
  var uncurryThisAccessor = requireFunctionUncurryThisAccessor();
  var isObject2 = requireIsObject();
  var requireObjectCoercible2 = requireRequireObjectCoercible();
  var aPossiblePrototype2 = requireAPossiblePrototype();
  objectSetPrototypeOf = Object.setPrototypeOf || ("__proto__" in {} ? (function() {
    var CORRECT_SETTER = false;
    var test = {};
    var setter;
    try {
      setter = uncurryThisAccessor(Object.prototype, "__proto__", "set");
      setter(test, []);
      CORRECT_SETTER = test instanceof Array;
    } catch (error) {
    }
    return function setPrototypeOf(O, proto) {
      requireObjectCoercible2(O);
      aPossiblePrototype2(proto);
      if (!isObject2(O)) return O;
      if (CORRECT_SETTER) setter(O, proto);
      else O.__proto__ = proto;
      return O;
    };
  })() : void 0);
  return objectSetPrototypeOf;
}
var iteratorDefine;
var hasRequiredIteratorDefine;
function requireIteratorDefine() {
  if (hasRequiredIteratorDefine) return iteratorDefine;
  hasRequiredIteratorDefine = 1;
  var $ = require_export();
  var call = requireFunctionCall();
  var IS_PURE = requireIsPure();
  var FunctionName = requireFunctionName();
  var isCallable2 = requireIsCallable();
  var createIteratorConstructor = requireIteratorCreateConstructor();
  var getPrototypeOf = requireObjectGetPrototypeOf();
  var setPrototypeOf = requireObjectSetPrototypeOf();
  var setToStringTag2 = requireSetToStringTag();
  var createNonEnumerableProperty2 = requireCreateNonEnumerableProperty();
  var defineBuiltIn2 = requireDefineBuiltIn();
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var Iterators = requireIterators();
  var IteratorsCore = requireIteratorsCore();
  var PROPER_FUNCTION_NAME = FunctionName.PROPER;
  var CONFIGURABLE_FUNCTION_NAME = FunctionName.CONFIGURABLE;
  var IteratorPrototype = IteratorsCore.IteratorPrototype;
  var BUGGY_SAFARI_ITERATORS = IteratorsCore.BUGGY_SAFARI_ITERATORS;
  var ITERATOR = wellKnownSymbol2("iterator");
  var KEYS = "keys";
  var VALUES = "values";
  var ENTRIES = "entries";
  var returnThis = function() {
    return this;
  };
  iteratorDefine = function(Iterable, NAME, IteratorConstructor, next, DEFAULT, IS_SET, FORCED) {
    createIteratorConstructor(IteratorConstructor, NAME, next);
    var getIterationMethod = function(KIND) {
      if (KIND === DEFAULT && defaultIterator) return defaultIterator;
      if (!BUGGY_SAFARI_ITERATORS && KIND && KIND in IterablePrototype) return IterablePrototype[KIND];
      switch (KIND) {
        case KEYS:
          return function keys2() {
            return new IteratorConstructor(this, KIND);
          };
        case VALUES:
          return function values2() {
            return new IteratorConstructor(this, KIND);
          };
        case ENTRIES:
          return function entries2() {
            return new IteratorConstructor(this, KIND);
          };
      }
      return function() {
        return new IteratorConstructor(this);
      };
    };
    var TO_STRING_TAG = NAME + " Iterator";
    var INCORRECT_VALUES_NAME = false;
    var IterablePrototype = Iterable.prototype;
    var nativeIterator = IterablePrototype[ITERATOR] || IterablePrototype["@@iterator"] || DEFAULT && IterablePrototype[DEFAULT];
    var defaultIterator = !BUGGY_SAFARI_ITERATORS && nativeIterator || getIterationMethod(DEFAULT);
    var anyNativeIterator = NAME === "Array" ? IterablePrototype.entries || nativeIterator : nativeIterator;
    var CurrentIteratorPrototype, methods, KEY;
    if (anyNativeIterator) {
      CurrentIteratorPrototype = getPrototypeOf(anyNativeIterator.call(new Iterable()));
      if (CurrentIteratorPrototype !== Object.prototype && CurrentIteratorPrototype.next) {
        if (!IS_PURE && getPrototypeOf(CurrentIteratorPrototype) !== IteratorPrototype) {
          if (setPrototypeOf) {
            setPrototypeOf(CurrentIteratorPrototype, IteratorPrototype);
          } else if (!isCallable2(CurrentIteratorPrototype[ITERATOR])) {
            defineBuiltIn2(CurrentIteratorPrototype, ITERATOR, returnThis);
          }
        }
        setToStringTag2(CurrentIteratorPrototype, TO_STRING_TAG, true, true);
        if (IS_PURE) Iterators[TO_STRING_TAG] = returnThis;
      }
    }
    if (PROPER_FUNCTION_NAME && DEFAULT === VALUES && nativeIterator && nativeIterator.name !== VALUES) {
      if (!IS_PURE && CONFIGURABLE_FUNCTION_NAME) {
        createNonEnumerableProperty2(IterablePrototype, "name", VALUES);
      } else {
        INCORRECT_VALUES_NAME = true;
        defaultIterator = function values2() {
          return call(nativeIterator, this);
        };
      }
    }
    if (DEFAULT) {
      methods = {
        values: getIterationMethod(VALUES),
        keys: IS_SET ? defaultIterator : getIterationMethod(KEYS),
        entries: getIterationMethod(ENTRIES)
      };
      if (FORCED) for (KEY in methods) {
        if (BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME || !(KEY in IterablePrototype)) {
          defineBuiltIn2(IterablePrototype, KEY, methods[KEY]);
        }
      }
      else $({ target: NAME, proto: true, forced: BUGGY_SAFARI_ITERATORS || INCORRECT_VALUES_NAME }, methods);
    }
    if ((!IS_PURE || FORCED) && IterablePrototype[ITERATOR] !== defaultIterator) {
      defineBuiltIn2(IterablePrototype, ITERATOR, defaultIterator, { name: DEFAULT });
    }
    Iterators[NAME] = defaultIterator;
    return methods;
  };
  return iteratorDefine;
}
var createIterResultObject;
var hasRequiredCreateIterResultObject;
function requireCreateIterResultObject() {
  if (hasRequiredCreateIterResultObject) return createIterResultObject;
  hasRequiredCreateIterResultObject = 1;
  createIterResultObject = function(value, done) {
    return { value, done };
  };
  return createIterResultObject;
}
var es_array_iterator;
var hasRequiredEs_array_iterator;
function requireEs_array_iterator() {
  if (hasRequiredEs_array_iterator) return es_array_iterator;
  hasRequiredEs_array_iterator = 1;
  var toIndexedObject2 = requireToIndexedObject();
  var addToUnscopables2 = requireAddToUnscopables();
  var Iterators = requireIterators();
  var InternalStateModule = requireInternalState();
  var defineProperty2 = requireObjectDefineProperty().f;
  var defineIterator = requireIteratorDefine();
  var createIterResultObject2 = requireCreateIterResultObject();
  var IS_PURE = requireIsPure();
  var DESCRIPTORS = requireDescriptors();
  var ARRAY_ITERATOR = "Array Iterator";
  var setInternalState = InternalStateModule.set;
  var getInternalState = InternalStateModule.getterFor(ARRAY_ITERATOR);
  es_array_iterator = defineIterator(Array, "Array", function(iterated, kind) {
    setInternalState(this, {
      type: ARRAY_ITERATOR,
      target: toIndexedObject2(iterated),
      // target
      index: 0,
      // next index
      kind
      // kind
    });
  }, function() {
    var state = getInternalState(this);
    var target = state.target;
    var index = state.index++;
    if (!target || index >= target.length) {
      state.target = null;
      return createIterResultObject2(void 0, true);
    }
    switch (state.kind) {
      case "keys":
        return createIterResultObject2(index, false);
      case "values":
        return createIterResultObject2(target[index], false);
    }
    return createIterResultObject2([index, target[index]], false);
  }, "values");
  var values2 = Iterators.Arguments = Iterators.Array;
  addToUnscopables2("keys");
  addToUnscopables2("values");
  addToUnscopables2("entries");
  if (!IS_PURE && DESCRIPTORS && values2.name !== "values") try {
    defineProperty2(values2, "name", { value: "values" });
  } catch (error) {
  }
  return es_array_iterator;
}
var domIterables;
var hasRequiredDomIterables;
function requireDomIterables() {
  if (hasRequiredDomIterables) return domIterables;
  hasRequiredDomIterables = 1;
  domIterables = {
    CSSRuleList: 0,
    CSSStyleDeclaration: 0,
    CSSValueList: 0,
    ClientRectList: 0,
    DOMRectList: 0,
    DOMStringList: 0,
    DOMTokenList: 1,
    DataTransferItemList: 0,
    FileList: 0,
    HTMLAllCollection: 0,
    HTMLCollection: 0,
    HTMLFormElement: 0,
    HTMLSelectElement: 0,
    MediaList: 0,
    MimeTypeArray: 0,
    NamedNodeMap: 0,
    NodeList: 1,
    PaintRequestList: 0,
    Plugin: 0,
    PluginArray: 0,
    SVGLengthList: 0,
    SVGNumberList: 0,
    SVGPathSegList: 0,
    SVGPointList: 0,
    SVGStringList: 0,
    SVGTransformList: 0,
    SourceBufferList: 0,
    StyleSheetList: 0,
    TextTrackCueList: 0,
    TextTrackList: 0,
    TouchList: 0
  };
  return domIterables;
}
var hasRequiredWeb_domCollections_iterator;
function requireWeb_domCollections_iterator() {
  if (hasRequiredWeb_domCollections_iterator) return web_domCollections_iterator;
  hasRequiredWeb_domCollections_iterator = 1;
  requireEs_array_iterator();
  var DOMIterables = requireDomIterables();
  var globalThis2 = requireGlobalThis();
  var setToStringTag2 = requireSetToStringTag();
  var Iterators = requireIterators();
  for (var COLLECTION_NAME in DOMIterables) {
    setToStringTag2(globalThis2[COLLECTION_NAME], COLLECTION_NAME);
    Iterators[COLLECTION_NAME] = Iterators.Array;
  }
  return web_domCollections_iterator;
}
var symbol$3;
var hasRequiredSymbol$3;
function requireSymbol$3() {
  if (hasRequiredSymbol$3) return symbol$3;
  hasRequiredSymbol$3 = 1;
  var parent = requireSymbol$4();
  requireWeb_domCollections_iterator();
  symbol$3 = parent;
  return symbol$3;
}
var esnext_function_metadata = {};
var hasRequiredEsnext_function_metadata;
function requireEsnext_function_metadata() {
  if (hasRequiredEsnext_function_metadata) return esnext_function_metadata;
  hasRequiredEsnext_function_metadata = 1;
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var defineProperty2 = requireObjectDefineProperty().f;
  var METADATA = wellKnownSymbol2("metadata");
  var FunctionPrototype = Function.prototype;
  if (FunctionPrototype[METADATA] === void 0) {
    defineProperty2(FunctionPrototype, METADATA, {
      value: null
    });
  }
  return esnext_function_metadata;
}
var esnext_symbol_asyncDispose = {};
var hasRequiredEsnext_symbol_asyncDispose;
function requireEsnext_symbol_asyncDispose() {
  if (hasRequiredEsnext_symbol_asyncDispose) return esnext_symbol_asyncDispose;
  hasRequiredEsnext_symbol_asyncDispose = 1;
  requireEs_symbol_asyncDispose();
  return esnext_symbol_asyncDispose;
}
var esnext_symbol_dispose = {};
var hasRequiredEsnext_symbol_dispose;
function requireEsnext_symbol_dispose() {
  if (hasRequiredEsnext_symbol_dispose) return esnext_symbol_dispose;
  hasRequiredEsnext_symbol_dispose = 1;
  requireEs_symbol_dispose();
  return esnext_symbol_dispose;
}
var esnext_symbol_metadata = {};
var hasRequiredEsnext_symbol_metadata;
function requireEsnext_symbol_metadata() {
  if (hasRequiredEsnext_symbol_metadata) return esnext_symbol_metadata;
  hasRequiredEsnext_symbol_metadata = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("metadata");
  return esnext_symbol_metadata;
}
var symbol$2;
var hasRequiredSymbol$2;
function requireSymbol$2() {
  if (hasRequiredSymbol$2) return symbol$2;
  hasRequiredSymbol$2 = 1;
  var parent = requireSymbol$3();
  requireEsnext_function_metadata();
  requireEsnext_symbol_asyncDispose();
  requireEsnext_symbol_dispose();
  requireEsnext_symbol_metadata();
  symbol$2 = parent;
  return symbol$2;
}
var esnext_symbol_isRegisteredSymbol = {};
var symbolIsRegistered;
var hasRequiredSymbolIsRegistered;
function requireSymbolIsRegistered() {
  if (hasRequiredSymbolIsRegistered) return symbolIsRegistered;
  hasRequiredSymbolIsRegistered = 1;
  var getBuiltIn2 = requireGetBuiltIn();
  var uncurryThis = requireFunctionUncurryThis();
  var Symbol2 = getBuiltIn2("Symbol");
  var keyFor = Symbol2.keyFor;
  var thisSymbolValue = uncurryThis(Symbol2.prototype.valueOf);
  symbolIsRegistered = Symbol2.isRegisteredSymbol || function isRegisteredSymbol(value) {
    try {
      return keyFor(thisSymbolValue(value)) !== void 0;
    } catch (error) {
      return false;
    }
  };
  return symbolIsRegistered;
}
var hasRequiredEsnext_symbol_isRegisteredSymbol;
function requireEsnext_symbol_isRegisteredSymbol() {
  if (hasRequiredEsnext_symbol_isRegisteredSymbol) return esnext_symbol_isRegisteredSymbol;
  hasRequiredEsnext_symbol_isRegisteredSymbol = 1;
  var $ = require_export();
  var isRegisteredSymbol = requireSymbolIsRegistered();
  $({ target: "Symbol", stat: true }, {
    isRegisteredSymbol
  });
  return esnext_symbol_isRegisteredSymbol;
}
var esnext_symbol_isWellKnownSymbol = {};
var symbolIsWellKnown;
var hasRequiredSymbolIsWellKnown;
function requireSymbolIsWellKnown() {
  if (hasRequiredSymbolIsWellKnown) return symbolIsWellKnown;
  hasRequiredSymbolIsWellKnown = 1;
  var shared2 = requireShared();
  var getBuiltIn2 = requireGetBuiltIn();
  var uncurryThis = requireFunctionUncurryThis();
  var isSymbol2 = requireIsSymbol();
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var Symbol2 = getBuiltIn2("Symbol");
  var $isWellKnownSymbol = Symbol2.isWellKnownSymbol;
  var getOwnPropertyNames = getBuiltIn2("Object", "getOwnPropertyNames");
  var thisSymbolValue = uncurryThis(Symbol2.prototype.valueOf);
  var WellKnownSymbolsStore = shared2("wks");
  for (var i = 0, symbolKeys = getOwnPropertyNames(Symbol2), symbolKeysLength = symbolKeys.length; i < symbolKeysLength; i++) {
    try {
      var symbolKey = symbolKeys[i];
      if (isSymbol2(Symbol2[symbolKey])) wellKnownSymbol2(symbolKey);
    } catch (error) {
    }
  }
  symbolIsWellKnown = function isWellKnownSymbol(value) {
    if ($isWellKnownSymbol && $isWellKnownSymbol(value)) return true;
    try {
      var symbol2 = thisSymbolValue(value);
      for (var j = 0, keys2 = getOwnPropertyNames(WellKnownSymbolsStore), keysLength = keys2.length; j < keysLength; j++) {
        if (WellKnownSymbolsStore[keys2[j]] == symbol2) return true;
      }
    } catch (error) {
    }
    return false;
  };
  return symbolIsWellKnown;
}
var hasRequiredEsnext_symbol_isWellKnownSymbol;
function requireEsnext_symbol_isWellKnownSymbol() {
  if (hasRequiredEsnext_symbol_isWellKnownSymbol) return esnext_symbol_isWellKnownSymbol;
  hasRequiredEsnext_symbol_isWellKnownSymbol = 1;
  var $ = require_export();
  var isWellKnownSymbol = requireSymbolIsWellKnown();
  $({ target: "Symbol", stat: true, forced: true }, {
    isWellKnownSymbol
  });
  return esnext_symbol_isWellKnownSymbol;
}
var esnext_symbol_customMatcher = {};
var hasRequiredEsnext_symbol_customMatcher;
function requireEsnext_symbol_customMatcher() {
  if (hasRequiredEsnext_symbol_customMatcher) return esnext_symbol_customMatcher;
  hasRequiredEsnext_symbol_customMatcher = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("customMatcher");
  return esnext_symbol_customMatcher;
}
var esnext_symbol_observable = {};
var hasRequiredEsnext_symbol_observable;
function requireEsnext_symbol_observable() {
  if (hasRequiredEsnext_symbol_observable) return esnext_symbol_observable;
  hasRequiredEsnext_symbol_observable = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("observable");
  return esnext_symbol_observable;
}
var esnext_symbol_isRegistered = {};
var hasRequiredEsnext_symbol_isRegistered;
function requireEsnext_symbol_isRegistered() {
  if (hasRequiredEsnext_symbol_isRegistered) return esnext_symbol_isRegistered;
  hasRequiredEsnext_symbol_isRegistered = 1;
  var $ = require_export();
  var isRegisteredSymbol = requireSymbolIsRegistered();
  $({ target: "Symbol", stat: true, name: "isRegisteredSymbol" }, {
    isRegistered: isRegisteredSymbol
  });
  return esnext_symbol_isRegistered;
}
var esnext_symbol_isWellKnown = {};
var hasRequiredEsnext_symbol_isWellKnown;
function requireEsnext_symbol_isWellKnown() {
  if (hasRequiredEsnext_symbol_isWellKnown) return esnext_symbol_isWellKnown;
  hasRequiredEsnext_symbol_isWellKnown = 1;
  var $ = require_export();
  var isWellKnownSymbol = requireSymbolIsWellKnown();
  $({ target: "Symbol", stat: true, name: "isWellKnownSymbol", forced: true }, {
    isWellKnown: isWellKnownSymbol
  });
  return esnext_symbol_isWellKnown;
}
var esnext_symbol_matcher = {};
var hasRequiredEsnext_symbol_matcher;
function requireEsnext_symbol_matcher() {
  if (hasRequiredEsnext_symbol_matcher) return esnext_symbol_matcher;
  hasRequiredEsnext_symbol_matcher = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("matcher");
  return esnext_symbol_matcher;
}
var esnext_symbol_metadataKey = {};
var hasRequiredEsnext_symbol_metadataKey;
function requireEsnext_symbol_metadataKey() {
  if (hasRequiredEsnext_symbol_metadataKey) return esnext_symbol_metadataKey;
  hasRequiredEsnext_symbol_metadataKey = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("metadataKey");
  return esnext_symbol_metadataKey;
}
var esnext_symbol_patternMatch = {};
var hasRequiredEsnext_symbol_patternMatch;
function requireEsnext_symbol_patternMatch() {
  if (hasRequiredEsnext_symbol_patternMatch) return esnext_symbol_patternMatch;
  hasRequiredEsnext_symbol_patternMatch = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("patternMatch");
  return esnext_symbol_patternMatch;
}
var esnext_symbol_replaceAll = {};
var hasRequiredEsnext_symbol_replaceAll;
function requireEsnext_symbol_replaceAll() {
  if (hasRequiredEsnext_symbol_replaceAll) return esnext_symbol_replaceAll;
  hasRequiredEsnext_symbol_replaceAll = 1;
  var defineWellKnownSymbol = requireWellKnownSymbolDefine();
  defineWellKnownSymbol("replaceAll");
  return esnext_symbol_replaceAll;
}
var symbol$1;
var hasRequiredSymbol$1;
function requireSymbol$1() {
  if (hasRequiredSymbol$1) return symbol$1;
  hasRequiredSymbol$1 = 1;
  var parent = requireSymbol$2();
  requireEsnext_symbol_isRegisteredSymbol();
  requireEsnext_symbol_isWellKnownSymbol();
  requireEsnext_symbol_customMatcher();
  requireEsnext_symbol_observable();
  requireEsnext_symbol_isRegistered();
  requireEsnext_symbol_isWellKnown();
  requireEsnext_symbol_matcher();
  requireEsnext_symbol_metadataKey();
  requireEsnext_symbol_patternMatch();
  requireEsnext_symbol_replaceAll();
  symbol$1 = parent;
  return symbol$1;
}
var symbol;
var hasRequiredSymbol;
function requireSymbol() {
  if (hasRequiredSymbol) return symbol;
  hasRequiredSymbol = 1;
  symbol = requireSymbol$1();
  return symbol;
}
var symbolExports = requireSymbol();
var _Symbol = getDefaultExportFromCjs(symbolExports);
var es_string_iterator = {};
var stringMultibyte;
var hasRequiredStringMultibyte;
function requireStringMultibyte() {
  if (hasRequiredStringMultibyte) return stringMultibyte;
  hasRequiredStringMultibyte = 1;
  var uncurryThis = requireFunctionUncurryThis();
  var toIntegerOrInfinity2 = requireToIntegerOrInfinity();
  var toString2 = requireToString();
  var requireObjectCoercible2 = requireRequireObjectCoercible();
  var charAt = uncurryThis("".charAt);
  var charCodeAt = uncurryThis("".charCodeAt);
  var stringSlice = uncurryThis("".slice);
  var createMethod = function(CONVERT_TO_STRING) {
    return function($this, pos) {
      var S = toString2(requireObjectCoercible2($this));
      var position = toIntegerOrInfinity2(pos);
      var size = S.length;
      var first, second;
      if (position < 0 || position >= size) return CONVERT_TO_STRING ? "" : void 0;
      first = charCodeAt(S, position);
      return first < 55296 || first > 56319 || position + 1 === size || (second = charCodeAt(S, position + 1)) < 56320 || second > 57343 ? CONVERT_TO_STRING ? charAt(S, position) : first : CONVERT_TO_STRING ? stringSlice(S, position, position + 2) : (first - 55296 << 10) + (second - 56320) + 65536;
    };
  };
  stringMultibyte = {
    // `String.prototype.codePointAt` method
    // https://tc39.es/ecma262/#sec-string.prototype.codepointat
    codeAt: createMethod(false),
    // `String.prototype.at` method
    // https://github.com/mathiasbynens/String.prototype.at
    charAt: createMethod(true)
  };
  return stringMultibyte;
}
var hasRequiredEs_string_iterator;
function requireEs_string_iterator() {
  if (hasRequiredEs_string_iterator) return es_string_iterator;
  hasRequiredEs_string_iterator = 1;
  var charAt = requireStringMultibyte().charAt;
  var toString2 = requireToString();
  var InternalStateModule = requireInternalState();
  var defineIterator = requireIteratorDefine();
  var createIterResultObject2 = requireCreateIterResultObject();
  var STRING_ITERATOR = "String Iterator";
  var setInternalState = InternalStateModule.set;
  var getInternalState = InternalStateModule.getterFor(STRING_ITERATOR);
  defineIterator(String, "String", function(iterated) {
    setInternalState(this, {
      type: STRING_ITERATOR,
      string: toString2(iterated),
      index: 0
    });
  }, function next() {
    var state = getInternalState(this);
    var string = state.string;
    var index = state.index;
    var point;
    if (index >= string.length) return createIterResultObject2(void 0, true);
    point = charAt(string, index);
    state.index += point.length;
    return createIterResultObject2(point, false);
  });
  return es_string_iterator;
}
var iterator$5;
var hasRequiredIterator$5;
function requireIterator$5() {
  if (hasRequiredIterator$5) return iterator$5;
  hasRequiredIterator$5 = 1;
  requireEs_array_iterator();
  requireEs_string_iterator();
  requireEs_symbol_iterator();
  var WrappedWellKnownSymbolModule = requireWellKnownSymbolWrapped();
  iterator$5 = WrappedWellKnownSymbolModule.f("iterator");
  return iterator$5;
}
var iterator$4;
var hasRequiredIterator$4;
function requireIterator$4() {
  if (hasRequiredIterator$4) return iterator$4;
  hasRequiredIterator$4 = 1;
  var parent = requireIterator$5();
  requireWeb_domCollections_iterator();
  iterator$4 = parent;
  return iterator$4;
}
var iterator$3;
var hasRequiredIterator$3;
function requireIterator$3() {
  if (hasRequiredIterator$3) return iterator$3;
  hasRequiredIterator$3 = 1;
  var parent = requireIterator$4();
  iterator$3 = parent;
  return iterator$3;
}
var iterator$2;
var hasRequiredIterator$2;
function requireIterator$2() {
  if (hasRequiredIterator$2) return iterator$2;
  hasRequiredIterator$2 = 1;
  var parent = requireIterator$3();
  iterator$2 = parent;
  return iterator$2;
}
var iterator$1;
var hasRequiredIterator$1;
function requireIterator$1() {
  if (hasRequiredIterator$1) return iterator$1;
  hasRequiredIterator$1 = 1;
  iterator$1 = requireIterator$2();
  return iterator$1;
}
var iteratorExports$1 = requireIterator$1();
var _Symbol$iterator$1 = getDefaultExportFromCjs(iteratorExports$1);
function _typeof(o) {
  "@babel/helpers - typeof";
  return _typeof = "function" == typeof _Symbol && "symbol" == typeof _Symbol$iterator$1 ? function(o2) {
    return typeof o2;
  } : function(o2) {
    return o2 && "function" == typeof _Symbol && o2.constructor === _Symbol && o2 !== _Symbol.prototype ? "symbol" : typeof o2;
  }, _typeof(o);
}
var toPrimitive$5;
var hasRequiredToPrimitive$4;
function requireToPrimitive$4() {
  if (hasRequiredToPrimitive$4) return toPrimitive$5;
  hasRequiredToPrimitive$4 = 1;
  requireEs_symbol_toPrimitive();
  var WrappedWellKnownSymbolModule = requireWellKnownSymbolWrapped();
  toPrimitive$5 = WrappedWellKnownSymbolModule.f("toPrimitive");
  return toPrimitive$5;
}
var toPrimitive$4;
var hasRequiredToPrimitive$3;
function requireToPrimitive$3() {
  if (hasRequiredToPrimitive$3) return toPrimitive$4;
  hasRequiredToPrimitive$3 = 1;
  var parent = requireToPrimitive$4();
  toPrimitive$4 = parent;
  return toPrimitive$4;
}
var toPrimitive$3;
var hasRequiredToPrimitive$2;
function requireToPrimitive$2() {
  if (hasRequiredToPrimitive$2) return toPrimitive$3;
  hasRequiredToPrimitive$2 = 1;
  var parent = requireToPrimitive$3();
  toPrimitive$3 = parent;
  return toPrimitive$3;
}
var toPrimitive$2;
var hasRequiredToPrimitive$1;
function requireToPrimitive$1() {
  if (hasRequiredToPrimitive$1) return toPrimitive$2;
  hasRequiredToPrimitive$1 = 1;
  var parent = requireToPrimitive$2();
  toPrimitive$2 = parent;
  return toPrimitive$2;
}
var toPrimitive$1;
var hasRequiredToPrimitive;
function requireToPrimitive() {
  if (hasRequiredToPrimitive) return toPrimitive$1;
  hasRequiredToPrimitive = 1;
  toPrimitive$1 = requireToPrimitive$1();
  return toPrimitive$1;
}
var toPrimitiveExports = requireToPrimitive();
var _Symbol$toPrimitive = getDefaultExportFromCjs(toPrimitiveExports);
function toPrimitive(t, r) {
  if ("object" != _typeof(t) || !t) return t;
  var e = t[_Symbol$toPrimitive];
  if (void 0 !== e) {
    var i = e.call(t, r);
    if ("object" != _typeof(i)) return i;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r ? String : Number)(t);
}
function toPropertyKey(t) {
  var i = toPrimitive(t, "string");
  return "symbol" == _typeof(i) ? i : i + "";
}
function _defineProperty(e, r, t) {
  return (r = toPropertyKey(r)) in e ? _Object$defineProperty$1(e, r, {
    value: t,
    enumerable: true,
    configurable: true,
    writable: true
  }) : e[r] = t, e;
}
var es_function_bind = {};
var functionBind;
var hasRequiredFunctionBind;
function requireFunctionBind() {
  if (hasRequiredFunctionBind) return functionBind;
  hasRequiredFunctionBind = 1;
  var uncurryThis = requireFunctionUncurryThis();
  var aCallable2 = requireACallable();
  var isObject2 = requireIsObject();
  var hasOwn = requireHasOwnProperty();
  var arraySlice2 = requireArraySlice();
  var NATIVE_BIND = requireFunctionBindNative();
  var $Function = Function;
  var concat2 = uncurryThis([].concat);
  var join = uncurryThis([].join);
  var factories = {};
  var construct = function(C, argsLength, args) {
    if (!hasOwn(factories, argsLength)) {
      var list = [];
      var i = 0;
      for (; i < argsLength; i++) list[i] = "a[" + i + "]";
      factories[argsLength] = $Function("C,a", "return new C(" + join(list, ",") + ")");
    }
    return factories[argsLength](C, args);
  };
  functionBind = NATIVE_BIND ? $Function.bind : function bind2(that) {
    var F = aCallable2(this);
    var Prototype = F.prototype;
    var partArgs = arraySlice2(arguments, 1);
    var boundFunction = function bound() {
      var args = concat2(partArgs, arraySlice2(arguments));
      return this instanceof boundFunction ? construct(F, args.length, args) : F.apply(that, args);
    };
    if (isObject2(Prototype)) boundFunction.prototype = Prototype;
    return boundFunction;
  };
  return functionBind;
}
var hasRequiredEs_function_bind;
function requireEs_function_bind() {
  if (hasRequiredEs_function_bind) return es_function_bind;
  hasRequiredEs_function_bind = 1;
  var $ = require_export();
  var bind2 = requireFunctionBind();
  $({ target: "Function", proto: true, forced: Function.bind !== bind2 }, {
    bind: bind2
  });
  return es_function_bind;
}
var getBuiltInPrototypeMethod;
var hasRequiredGetBuiltInPrototypeMethod;
function requireGetBuiltInPrototypeMethod() {
  if (hasRequiredGetBuiltInPrototypeMethod) return getBuiltInPrototypeMethod;
  hasRequiredGetBuiltInPrototypeMethod = 1;
  var globalThis2 = requireGlobalThis();
  var path2 = requirePath();
  getBuiltInPrototypeMethod = function(CONSTRUCTOR, METHOD) {
    var Namespace = path2[CONSTRUCTOR + "Prototype"];
    var pureMethod = Namespace && Namespace[METHOD];
    if (pureMethod) return pureMethod;
    var NativeConstructor = globalThis2[CONSTRUCTOR];
    var NativePrototype = NativeConstructor && NativeConstructor.prototype;
    return NativePrototype && NativePrototype[METHOD];
  };
  return getBuiltInPrototypeMethod;
}
var bind$3;
var hasRequiredBind$3;
function requireBind$3() {
  if (hasRequiredBind$3) return bind$3;
  hasRequiredBind$3 = 1;
  requireEs_function_bind();
  var getBuiltInPrototypeMethod2 = requireGetBuiltInPrototypeMethod();
  bind$3 = getBuiltInPrototypeMethod2("Function", "bind");
  return bind$3;
}
var bind$2;
var hasRequiredBind$2;
function requireBind$2() {
  if (hasRequiredBind$2) return bind$2;
  hasRequiredBind$2 = 1;
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var method = requireBind$3();
  var FunctionPrototype = Function.prototype;
  bind$2 = function(it) {
    var own = it.bind;
    return it === FunctionPrototype || isPrototypeOf(FunctionPrototype, it) && own === FunctionPrototype.bind ? method : own;
  };
  return bind$2;
}
var bind$1;
var hasRequiredBind$1;
function requireBind$1() {
  if (hasRequiredBind$1) return bind$1;
  hasRequiredBind$1 = 1;
  var parent = requireBind$2();
  bind$1 = parent;
  return bind$1;
}
var bind;
var hasRequiredBind;
function requireBind() {
  if (hasRequiredBind) return bind;
  hasRequiredBind = 1;
  bind = requireBind$1();
  return bind;
}
var bindExports = requireBind();
var _bindInstanceProperty = getDefaultExportFromCjs(bindExports);
var es_array_reduce = {};
var arrayReduce;
var hasRequiredArrayReduce;
function requireArrayReduce() {
  if (hasRequiredArrayReduce) return arrayReduce;
  hasRequiredArrayReduce = 1;
  var aCallable2 = requireACallable();
  var toObject2 = requireToObject();
  var IndexedObject = requireIndexedObject();
  var lengthOfArrayLike2 = requireLengthOfArrayLike();
  var $TypeError = TypeError;
  var REDUCE_EMPTY = "Reduce of empty array with no initial value";
  var createMethod = function(IS_RIGHT) {
    return function(that, callbackfn, argumentsLength, memo) {
      var O = toObject2(that);
      var self2 = IndexedObject(O);
      var length = lengthOfArrayLike2(O);
      aCallable2(callbackfn);
      if (length === 0 && argumentsLength < 2) throw new $TypeError(REDUCE_EMPTY);
      var index = IS_RIGHT ? length - 1 : 0;
      var i = IS_RIGHT ? -1 : 1;
      if (argumentsLength < 2) while (true) {
        if (index in self2) {
          memo = self2[index];
          index += i;
          break;
        }
        index += i;
        if (IS_RIGHT ? index < 0 : length <= index) {
          throw new $TypeError(REDUCE_EMPTY);
        }
      }
      for (; IS_RIGHT ? index >= 0 : length > index; index += i) if (index in self2) {
        memo = callbackfn(memo, self2[index], index, O);
      }
      return memo;
    };
  };
  arrayReduce = {
    // `Array.prototype.reduce` method
    // https://tc39.es/ecma262/#sec-array.prototype.reduce
    left: createMethod(false),
    // `Array.prototype.reduceRight` method
    // https://tc39.es/ecma262/#sec-array.prototype.reduceright
    right: createMethod(true)
  };
  return arrayReduce;
}
var arrayMethodIsStrict;
var hasRequiredArrayMethodIsStrict;
function requireArrayMethodIsStrict() {
  if (hasRequiredArrayMethodIsStrict) return arrayMethodIsStrict;
  hasRequiredArrayMethodIsStrict = 1;
  var fails2 = requireFails();
  arrayMethodIsStrict = function(METHOD_NAME, argument) {
    var method = [][METHOD_NAME];
    return !!method && fails2(function() {
      method.call(null, argument || function() {
        return 1;
      }, 1);
    });
  };
  return arrayMethodIsStrict;
}
var environment;
var hasRequiredEnvironment;
function requireEnvironment() {
  if (hasRequiredEnvironment) return environment;
  hasRequiredEnvironment = 1;
  var globalThis2 = requireGlobalThis();
  var userAgent = requireEnvironmentUserAgent();
  var classof2 = requireClassofRaw();
  var userAgentStartsWith = function(string) {
    return userAgent.slice(0, string.length) === string;
  };
  environment = (function() {
    if (userAgentStartsWith("Bun/")) return "BUN";
    if (userAgentStartsWith("Cloudflare-Workers")) return "CLOUDFLARE";
    if (userAgentStartsWith("Deno/")) return "DENO";
    if (userAgentStartsWith("Node.js/")) return "NODE";
    if (globalThis2.Bun && typeof Bun.version == "string") return "BUN";
    if (globalThis2.Deno && typeof Deno.version == "object") return "DENO";
    if (classof2(globalThis2.process) === "process") return "NODE";
    if (globalThis2.window && globalThis2.document) return "BROWSER";
    return "REST";
  })();
  return environment;
}
var environmentIsNode;
var hasRequiredEnvironmentIsNode;
function requireEnvironmentIsNode() {
  if (hasRequiredEnvironmentIsNode) return environmentIsNode;
  hasRequiredEnvironmentIsNode = 1;
  var ENVIRONMENT = requireEnvironment();
  environmentIsNode = ENVIRONMENT === "NODE";
  return environmentIsNode;
}
var hasRequiredEs_array_reduce;
function requireEs_array_reduce() {
  if (hasRequiredEs_array_reduce) return es_array_reduce;
  hasRequiredEs_array_reduce = 1;
  var $ = require_export();
  var $reduce = requireArrayReduce().left;
  var arrayMethodIsStrict2 = requireArrayMethodIsStrict();
  var CHROME_VERSION = requireEnvironmentV8Version();
  var IS_NODE = requireEnvironmentIsNode();
  var CHROME_BUG = !IS_NODE && CHROME_VERSION > 79 && CHROME_VERSION < 83;
  var FORCED = CHROME_BUG || !arrayMethodIsStrict2("reduce");
  $({ target: "Array", proto: true, forced: FORCED }, {
    reduce: function reduce2(callbackfn) {
      var length = arguments.length;
      return $reduce(this, callbackfn, length, length > 1 ? arguments[1] : void 0);
    }
  });
  return es_array_reduce;
}
var reduce$3;
var hasRequiredReduce$3;
function requireReduce$3() {
  if (hasRequiredReduce$3) return reduce$3;
  hasRequiredReduce$3 = 1;
  requireEs_array_reduce();
  var getBuiltInPrototypeMethod2 = requireGetBuiltInPrototypeMethod();
  reduce$3 = getBuiltInPrototypeMethod2("Array", "reduce");
  return reduce$3;
}
var reduce$2;
var hasRequiredReduce$2;
function requireReduce$2() {
  if (hasRequiredReduce$2) return reduce$2;
  hasRequiredReduce$2 = 1;
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var method = requireReduce$3();
  var ArrayPrototype = Array.prototype;
  reduce$2 = function(it) {
    var own = it.reduce;
    return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.reduce ? method : own;
  };
  return reduce$2;
}
var reduce$1;
var hasRequiredReduce$1;
function requireReduce$1() {
  if (hasRequiredReduce$1) return reduce$1;
  hasRequiredReduce$1 = 1;
  var parent = requireReduce$2();
  reduce$1 = parent;
  return reduce$1;
}
var reduce;
var hasRequiredReduce;
function requireReduce() {
  if (hasRequiredReduce) return reduce;
  hasRequiredReduce = 1;
  reduce = requireReduce$1();
  return reduce;
}
var reduceExports = requireReduce();
var _reduceInstanceProperty = getDefaultExportFromCjs(reduceExports);
var es_array_filter = {};
var hasRequiredEs_array_filter;
function requireEs_array_filter() {
  if (hasRequiredEs_array_filter) return es_array_filter;
  hasRequiredEs_array_filter = 1;
  var $ = require_export();
  var $filter = requireArrayIteration().filter;
  var arrayMethodHasSpeciesSupport2 = requireArrayMethodHasSpeciesSupport();
  var HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport2("filter");
  $({ target: "Array", proto: true, forced: !HAS_SPECIES_SUPPORT }, {
    filter: function filter2(callbackfn) {
      return $filter(this, callbackfn, arguments.length > 1 ? arguments[1] : void 0);
    }
  });
  return es_array_filter;
}
var filter$3;
var hasRequiredFilter$3;
function requireFilter$3() {
  if (hasRequiredFilter$3) return filter$3;
  hasRequiredFilter$3 = 1;
  requireEs_array_filter();
  var getBuiltInPrototypeMethod2 = requireGetBuiltInPrototypeMethod();
  filter$3 = getBuiltInPrototypeMethod2("Array", "filter");
  return filter$3;
}
var filter$2;
var hasRequiredFilter$2;
function requireFilter$2() {
  if (hasRequiredFilter$2) return filter$2;
  hasRequiredFilter$2 = 1;
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var method = requireFilter$3();
  var ArrayPrototype = Array.prototype;
  filter$2 = function(it) {
    var own = it.filter;
    return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.filter ? method : own;
  };
  return filter$2;
}
var filter$1;
var hasRequiredFilter$1;
function requireFilter$1() {
  if (hasRequiredFilter$1) return filter$1;
  hasRequiredFilter$1 = 1;
  var parent = requireFilter$2();
  filter$1 = parent;
  return filter$1;
}
var filter;
var hasRequiredFilter;
function requireFilter() {
  if (hasRequiredFilter) return filter;
  hasRequiredFilter = 1;
  filter = requireFilter$1();
  return filter;
}
var filterExports = requireFilter();
var _filterInstanceProperty = getDefaultExportFromCjs(filterExports);
var es_array_map = {};
var hasRequiredEs_array_map;
function requireEs_array_map() {
  if (hasRequiredEs_array_map) return es_array_map;
  hasRequiredEs_array_map = 1;
  var $ = require_export();
  var $map = requireArrayIteration().map;
  var arrayMethodHasSpeciesSupport2 = requireArrayMethodHasSpeciesSupport();
  var HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport2("map");
  $({ target: "Array", proto: true, forced: !HAS_SPECIES_SUPPORT }, {
    map: function map2(callbackfn) {
      return $map(this, callbackfn, arguments.length > 1 ? arguments[1] : void 0);
    }
  });
  return es_array_map;
}
var map$6;
var hasRequiredMap$6;
function requireMap$6() {
  if (hasRequiredMap$6) return map$6;
  hasRequiredMap$6 = 1;
  requireEs_array_map();
  var getBuiltInPrototypeMethod2 = requireGetBuiltInPrototypeMethod();
  map$6 = getBuiltInPrototypeMethod2("Array", "map");
  return map$6;
}
var map$5;
var hasRequiredMap$5;
function requireMap$5() {
  if (hasRequiredMap$5) return map$5;
  hasRequiredMap$5 = 1;
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var method = requireMap$6();
  var ArrayPrototype = Array.prototype;
  map$5 = function(it) {
    var own = it.map;
    return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.map ? method : own;
  };
  return map$5;
}
var map$4;
var hasRequiredMap$4;
function requireMap$4() {
  if (hasRequiredMap$4) return map$4;
  hasRequiredMap$4 = 1;
  var parent = requireMap$5();
  map$4 = parent;
  return map$4;
}
var map$3;
var hasRequiredMap$3;
function requireMap$3() {
  if (hasRequiredMap$3) return map$3;
  hasRequiredMap$3 = 1;
  map$3 = requireMap$4();
  return map$3;
}
var mapExports$1 = requireMap$3();
var _mapInstanceProperty = getDefaultExportFromCjs(mapExports$1);
var es_array_flatMap = {};
var flattenIntoArray_1;
var hasRequiredFlattenIntoArray;
function requireFlattenIntoArray() {
  if (hasRequiredFlattenIntoArray) return flattenIntoArray_1;
  hasRequiredFlattenIntoArray = 1;
  var isArray2 = requireIsArray$3();
  var lengthOfArrayLike2 = requireLengthOfArrayLike();
  var doesNotExceedSafeInteger2 = requireDoesNotExceedSafeInteger();
  var bind2 = requireFunctionBindContext();
  var flattenIntoArray = function(target, original, source, sourceLen, start, depth, mapper, thisArg) {
    var targetIndex = start;
    var sourceIndex = 0;
    var mapFn = mapper ? bind2(mapper, thisArg) : false;
    var element, elementLen;
    while (sourceIndex < sourceLen) {
      if (sourceIndex in source) {
        element = mapFn ? mapFn(source[sourceIndex], sourceIndex, original) : source[sourceIndex];
        if (depth > 0 && isArray2(element)) {
          elementLen = lengthOfArrayLike2(element);
          targetIndex = flattenIntoArray(target, original, element, elementLen, targetIndex, depth - 1) - 1;
        } else {
          doesNotExceedSafeInteger2(targetIndex + 1);
          target[targetIndex] = element;
        }
        targetIndex++;
      }
      sourceIndex++;
    }
    return targetIndex;
  };
  flattenIntoArray_1 = flattenIntoArray;
  return flattenIntoArray_1;
}
var hasRequiredEs_array_flatMap;
function requireEs_array_flatMap() {
  if (hasRequiredEs_array_flatMap) return es_array_flatMap;
  hasRequiredEs_array_flatMap = 1;
  var $ = require_export();
  var flattenIntoArray = requireFlattenIntoArray();
  var aCallable2 = requireACallable();
  var toObject2 = requireToObject();
  var lengthOfArrayLike2 = requireLengthOfArrayLike();
  var arraySpeciesCreate2 = requireArraySpeciesCreate();
  $({ target: "Array", proto: true }, {
    flatMap: function flatMap2(callbackfn) {
      var O = toObject2(this);
      var sourceLen = lengthOfArrayLike2(O);
      var A;
      aCallable2(callbackfn);
      A = arraySpeciesCreate2(O, 0);
      A.length = flattenIntoArray(A, O, O, sourceLen, 0, 1, callbackfn, arguments.length > 1 ? arguments[1] : void 0);
      return A;
    }
  });
  return es_array_flatMap;
}
var es_array_unscopables_flatMap = {};
var hasRequiredEs_array_unscopables_flatMap;
function requireEs_array_unscopables_flatMap() {
  if (hasRequiredEs_array_unscopables_flatMap) return es_array_unscopables_flatMap;
  hasRequiredEs_array_unscopables_flatMap = 1;
  var addToUnscopables2 = requireAddToUnscopables();
  addToUnscopables2("flatMap");
  return es_array_unscopables_flatMap;
}
var flatMap$3;
var hasRequiredFlatMap$3;
function requireFlatMap$3() {
  if (hasRequiredFlatMap$3) return flatMap$3;
  hasRequiredFlatMap$3 = 1;
  requireEs_array_flatMap();
  requireEs_array_unscopables_flatMap();
  var getBuiltInPrototypeMethod2 = requireGetBuiltInPrototypeMethod();
  flatMap$3 = getBuiltInPrototypeMethod2("Array", "flatMap");
  return flatMap$3;
}
var flatMap$2;
var hasRequiredFlatMap$2;
function requireFlatMap$2() {
  if (hasRequiredFlatMap$2) return flatMap$2;
  hasRequiredFlatMap$2 = 1;
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var method = requireFlatMap$3();
  var ArrayPrototype = Array.prototype;
  flatMap$2 = function(it) {
    var own = it.flatMap;
    return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.flatMap ? method : own;
  };
  return flatMap$2;
}
var flatMap$1;
var hasRequiredFlatMap$1;
function requireFlatMap$1() {
  if (hasRequiredFlatMap$1) return flatMap$1;
  hasRequiredFlatMap$1 = 1;
  var parent = requireFlatMap$2();
  flatMap$1 = parent;
  return flatMap$1;
}
var flatMap;
var hasRequiredFlatMap;
function requireFlatMap() {
  if (hasRequiredFlatMap) return flatMap;
  hasRequiredFlatMap = 1;
  flatMap = requireFlatMap$1();
  return flatMap;
}
var flatMapExports = requireFlatMap();
var _flatMapInstanceProperty = getDefaultExportFromCjs(flatMapExports);
function createNewDataPipeFrom(from) {
  return new DataPipeUnderConstruction(from);
}
var SimpleDataPipe = class {
  /**
   * Create a new data pipe.
   * @param source - The data set or data view that will be observed.
   * @param transformers - An array of transforming functions to be used to
   * filter or transform the items in the pipe.
   * @param target - The data set or data view that will receive the items.
   */
  constructor(source, transformers, target) {
    var _context, _context2, _context3;
    _defineProperty(this, "_listeners", {
      add: _bindInstanceProperty(_context = this._add).call(_context, this),
      remove: _bindInstanceProperty(_context2 = this._remove).call(_context2, this),
      update: _bindInstanceProperty(_context3 = this._update).call(_context3, this)
    });
    this._source = source;
    this._transformers = transformers;
    this._target = target;
  }
  /** @inheritDoc */
  all() {
    this._target.update(this._transformItems(this._source.get()));
    return this;
  }
  /** @inheritDoc */
  start() {
    this._source.on("add", this._listeners.add);
    this._source.on("remove", this._listeners.remove);
    this._source.on("update", this._listeners.update);
    return this;
  }
  /** @inheritDoc */
  stop() {
    this._source.off("add", this._listeners.add);
    this._source.off("remove", this._listeners.remove);
    this._source.off("update", this._listeners.update);
    return this;
  }
  /**
   * Apply the transformers to the items.
   * @param items - The items to be transformed.
   * @returns The transformed items.
   */
  _transformItems(items) {
    var _context4;
    return _reduceInstanceProperty(_context4 = this._transformers).call(_context4, (items2, transform) => {
      return transform(items2);
    }, items);
  }
  /**
   * Handle an add event.
   * @param _name - Ignored.
   * @param payload - The payload containing the ids of the added items.
   */
  _add(_name, payload) {
    if (payload == null) {
      return;
    }
    this._target.add(this._transformItems(this._source.get(payload.items)));
  }
  /**
   * Handle an update event.
   * @param _name - Ignored.
   * @param payload - The payload containing the ids of the updated items.
   */
  _update(_name, payload) {
    if (payload == null) {
      return;
    }
    this._target.update(this._transformItems(this._source.get(payload.items)));
  }
  /**
   * Handle a remove event.
   * @param _name - Ignored.
   * @param payload - The payload containing the data of the removed items.
   */
  _remove(_name, payload) {
    if (payload == null) {
      return;
    }
    this._target.remove(this._transformItems(payload.oldData));
  }
};
var DataPipeUnderConstruction = class {
  /**
   * Create a new data pipe factory. This is an internal constructor that
   * should never be called from outside of this file.
   * @param source - The source data set or data view for this pipe.
   */
  constructor(source) {
    _defineProperty(this, "_transformers", []);
    this._source = source;
  }
  /**
   * Filter the items.
   * @param callback - A filtering function that returns true if given item
   * should be piped and false if not.
   * @returns This factory for further configuration.
   */
  filter(callback) {
    this._transformers.push((input) => _filterInstanceProperty(input).call(input, callback));
    return this;
  }
  /**
   * Map each source item to a new type.
   * @param callback - A mapping function that takes a source item and returns
   * corresponding mapped item.
   * @typeParam TI - Target item type.
   * @typeParam TP - Target item type's id property name.
   * @returns This factory for further configuration.
   */
  map(callback) {
    this._transformers.push((input) => _mapInstanceProperty(input).call(input, callback));
    return this;
  }
  /**
   * Map each source item to zero or more items of a new type.
   * @param callback - A mapping function that takes a source item and returns
   * an array of corresponding mapped items.
   * @typeParam TI - Target item type.
   * @typeParam TP - Target item type's id property name.
   * @returns This factory for further configuration.
   */
  flatMap(callback) {
    this._transformers.push((input) => _flatMapInstanceProperty(input).call(input, callback));
    return this;
  }
  /**
   * Connect this pipe to given data set.
   * @param target - The data set that will receive the items from this pipe.
   * @returns The pipe connected between given data sets and performing
   * configured transformation on the processed items.
   */
  to(target) {
    return new SimpleDataPipe(this._source, this._transformers, target);
  }
};
var componentEmitter = { exports: {} };
var hasRequiredComponentEmitter;
function requireComponentEmitter() {
  if (hasRequiredComponentEmitter) return componentEmitter.exports;
  hasRequiredComponentEmitter = 1;
  (function(module) {
    function Emitter2(object) {
      if (object) {
        return mixin(object);
      }
      this._callbacks = /* @__PURE__ */ new Map();
    }
    function mixin(object) {
      Object.assign(object, Emitter2.prototype);
      object._callbacks = /* @__PURE__ */ new Map();
      return object;
    }
    Emitter2.prototype.on = function(event, listener) {
      const callbacks = this._callbacks.get(event) ?? [];
      callbacks.push(listener);
      this._callbacks.set(event, callbacks);
      return this;
    };
    Emitter2.prototype.once = function(event, listener) {
      const on = (...arguments_) => {
        this.off(event, on);
        listener.apply(this, arguments_);
      };
      on.fn = listener;
      this.on(event, on);
      return this;
    };
    Emitter2.prototype.off = function(event, listener) {
      if (event === void 0 && listener === void 0) {
        this._callbacks.clear();
        return this;
      }
      if (listener === void 0) {
        this._callbacks.delete(event);
        return this;
      }
      const callbacks = this._callbacks.get(event);
      if (callbacks) {
        for (const [index, callback] of callbacks.entries()) {
          if (callback === listener || callback.fn === listener) {
            callbacks.splice(index, 1);
            break;
          }
        }
        if (callbacks.length === 0) {
          this._callbacks.delete(event);
        } else {
          this._callbacks.set(event, callbacks);
        }
      }
      return this;
    };
    Emitter2.prototype.emit = function(event, ...arguments_) {
      const callbacks = this._callbacks.get(event);
      if (callbacks) {
        const callbacksCopy = [...callbacks];
        for (const callback of callbacksCopy) {
          callback.apply(this, arguments_);
        }
      }
      return this;
    };
    Emitter2.prototype.listeners = function(event) {
      return this._callbacks.get(event) ?? [];
    };
    Emitter2.prototype.listenerCount = function(event) {
      if (event) {
        return this.listeners(event).length;
      }
      let totalCount = 0;
      for (const callbacks of this._callbacks.values()) {
        totalCount += callbacks.length;
      }
      return totalCount;
    };
    Emitter2.prototype.hasListeners = function(event) {
      return this.listenerCount(event) > 0;
    };
    Emitter2.prototype.addEventListener = Emitter2.prototype.on;
    Emitter2.prototype.removeListener = Emitter2.prototype.off;
    Emitter2.prototype.removeEventListener = Emitter2.prototype.off;
    Emitter2.prototype.removeAllListeners = Emitter2.prototype.off;
    {
      module.exports = Emitter2;
    }
  })(componentEmitter);
  return componentEmitter.exports;
}
var componentEmitterExports = requireComponentEmitter();
var Emitter = getDefaultExportFromCjs(componentEmitterExports);
function _extends() {
  _extends = Object.assign || function(target) {
    for (var i = 1; i < arguments.length; i++) {
      var source = arguments[i];
      for (var key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
          target[key] = source[key];
        }
      }
    }
    return target;
  };
  return _extends.apply(this, arguments);
}
function _inheritsLoose(subClass, superClass) {
  subClass.prototype = Object.create(superClass.prototype);
  subClass.prototype.constructor = subClass;
  subClass.__proto__ = superClass;
}
function _assertThisInitialized(self2) {
  if (self2 === void 0) {
    throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
  }
  return self2;
}
var assign$3;
if (typeof Object.assign !== "function") {
  assign$3 = function assign2(target) {
    if (target === void 0 || target === null) {
      throw new TypeError("Cannot convert undefined or null to object");
    }
    var output = Object(target);
    for (var index = 1; index < arguments.length; index++) {
      var source = arguments[index];
      if (source !== void 0 && source !== null) {
        for (var nextKey in source) {
          if (source.hasOwnProperty(nextKey)) {
            output[nextKey] = source[nextKey];
          }
        }
      }
    }
    return output;
  };
} else {
  assign$3 = Object.assign;
}
var assign$1$1 = assign$3;
var VENDOR_PREFIXES = ["", "webkit", "Moz", "MS", "ms", "o"];
var TEST_ELEMENT = typeof document === "undefined" ? {
  style: {}
} : document.createElement("div");
var TYPE_FUNCTION = "function";
var round = Math.round;
var abs = Math.abs;
var now = Date.now;
function prefixed(obj, property) {
  var prefix;
  var prop;
  var camelProp = property[0].toUpperCase() + property.slice(1);
  var i = 0;
  while (i < VENDOR_PREFIXES.length) {
    prefix = VENDOR_PREFIXES[i];
    prop = prefix ? prefix + camelProp : property;
    if (prop in obj) {
      return prop;
    }
    i++;
  }
  return void 0;
}
var win;
if (typeof window === "undefined") {
  win = {};
} else {
  win = window;
}
var PREFIXED_TOUCH_ACTION = prefixed(TEST_ELEMENT.style, "touchAction");
var NATIVE_TOUCH_ACTION = PREFIXED_TOUCH_ACTION !== void 0;
function getTouchActionProps() {
  if (!NATIVE_TOUCH_ACTION) {
    return false;
  }
  var touchMap = {};
  var cssSupports = win.CSS && win.CSS.supports;
  ["auto", "manipulation", "pan-y", "pan-x", "pan-x pan-y", "none"].forEach(function(val) {
    return touchMap[val] = cssSupports ? win.CSS.supports("touch-action", val) : true;
  });
  return touchMap;
}
var TOUCH_ACTION_COMPUTE = "compute";
var TOUCH_ACTION_AUTO = "auto";
var TOUCH_ACTION_MANIPULATION = "manipulation";
var TOUCH_ACTION_NONE = "none";
var TOUCH_ACTION_PAN_X = "pan-x";
var TOUCH_ACTION_PAN_Y = "pan-y";
var TOUCH_ACTION_MAP = getTouchActionProps();
var MOBILE_REGEX = /mobile|tablet|ip(ad|hone|od)|android/i;
var SUPPORT_TOUCH = "ontouchstart" in win;
var SUPPORT_POINTER_EVENTS = prefixed(win, "PointerEvent") !== void 0;
var SUPPORT_ONLY_TOUCH = SUPPORT_TOUCH && MOBILE_REGEX.test(navigator.userAgent);
var INPUT_TYPE_TOUCH = "touch";
var INPUT_TYPE_PEN = "pen";
var INPUT_TYPE_MOUSE = "mouse";
var INPUT_TYPE_KINECT = "kinect";
var COMPUTE_INTERVAL = 25;
var INPUT_START = 1;
var INPUT_MOVE = 2;
var INPUT_END = 4;
var INPUT_CANCEL = 8;
var DIRECTION_NONE = 1;
var DIRECTION_LEFT = 2;
var DIRECTION_RIGHT = 4;
var DIRECTION_UP = 8;
var DIRECTION_DOWN = 16;
var DIRECTION_HORIZONTAL = DIRECTION_LEFT | DIRECTION_RIGHT;
var DIRECTION_VERTICAL = DIRECTION_UP | DIRECTION_DOWN;
var DIRECTION_ALL = DIRECTION_HORIZONTAL | DIRECTION_VERTICAL;
var PROPS_XY = ["x", "y"];
var PROPS_CLIENT_XY = ["clientX", "clientY"];
function each(obj, iterator2, context) {
  var i;
  if (!obj) {
    return;
  }
  if (obj.forEach) {
    obj.forEach(iterator2, context);
  } else if (obj.length !== void 0) {
    i = 0;
    while (i < obj.length) {
      iterator2.call(context, obj[i], i, obj);
      i++;
    }
  } else {
    for (i in obj) {
      obj.hasOwnProperty(i) && iterator2.call(context, obj[i], i, obj);
    }
  }
}
function boolOrFn(val, args) {
  if (typeof val === TYPE_FUNCTION) {
    return val.apply(args ? args[0] || void 0 : void 0, args);
  }
  return val;
}
function inStr(str, find) {
  return str.indexOf(find) > -1;
}
function cleanTouchActions(actions) {
  if (inStr(actions, TOUCH_ACTION_NONE)) {
    return TOUCH_ACTION_NONE;
  }
  var hasPanX = inStr(actions, TOUCH_ACTION_PAN_X);
  var hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y);
  if (hasPanX && hasPanY) {
    return TOUCH_ACTION_NONE;
  }
  if (hasPanX || hasPanY) {
    return hasPanX ? TOUCH_ACTION_PAN_X : TOUCH_ACTION_PAN_Y;
  }
  if (inStr(actions, TOUCH_ACTION_MANIPULATION)) {
    return TOUCH_ACTION_MANIPULATION;
  }
  return TOUCH_ACTION_AUTO;
}
var TouchAction = (function() {
  function TouchAction2(manager, value) {
    this.manager = manager;
    this.set(value);
  }
  var _proto = TouchAction2.prototype;
  _proto.set = function set2(value) {
    if (value === TOUCH_ACTION_COMPUTE) {
      value = this.compute();
    }
    if (NATIVE_TOUCH_ACTION && this.manager.element.style && TOUCH_ACTION_MAP[value]) {
      this.manager.element.style[PREFIXED_TOUCH_ACTION] = value;
    }
    this.actions = value.toLowerCase().trim();
  };
  _proto.update = function update() {
    this.set(this.manager.options.touchAction);
  };
  _proto.compute = function compute() {
    var actions = [];
    each(this.manager.recognizers, function(recognizer) {
      if (boolOrFn(recognizer.options.enable, [recognizer])) {
        actions = actions.concat(recognizer.getTouchAction());
      }
    });
    return cleanTouchActions(actions.join(" "));
  };
  _proto.preventDefaults = function preventDefaults(input) {
    var srcEvent = input.srcEvent;
    var direction = input.offsetDirection;
    if (this.manager.session.prevented) {
      srcEvent.preventDefault();
      return;
    }
    var actions = this.actions;
    var hasNone = inStr(actions, TOUCH_ACTION_NONE) && !TOUCH_ACTION_MAP[TOUCH_ACTION_NONE];
    var hasPanY = inStr(actions, TOUCH_ACTION_PAN_Y) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_Y];
    var hasPanX = inStr(actions, TOUCH_ACTION_PAN_X) && !TOUCH_ACTION_MAP[TOUCH_ACTION_PAN_X];
    if (hasNone) {
      var isTapPointer = input.pointers.length === 1;
      var isTapMovement = input.distance < 2;
      var isTapTouchTime = input.deltaTime < 250;
      if (isTapPointer && isTapMovement && isTapTouchTime) {
        return;
      }
    }
    if (hasPanX && hasPanY) {
      return;
    }
    if (hasNone || hasPanY && direction & DIRECTION_HORIZONTAL || hasPanX && direction & DIRECTION_VERTICAL) {
      return this.preventSrc(srcEvent);
    }
  };
  _proto.preventSrc = function preventSrc(srcEvent) {
    this.manager.session.prevented = true;
    srcEvent.preventDefault();
  };
  return TouchAction2;
})();
function hasParent(node, parent) {
  while (node) {
    if (node === parent) {
      return true;
    }
    node = node.parentNode;
  }
  return false;
}
function getCenter(pointers) {
  var pointersLength = pointers.length;
  if (pointersLength === 1) {
    return {
      x: round(pointers[0].clientX),
      y: round(pointers[0].clientY)
    };
  }
  var x = 0;
  var y = 0;
  var i = 0;
  while (i < pointersLength) {
    x += pointers[i].clientX;
    y += pointers[i].clientY;
    i++;
  }
  return {
    x: round(x / pointersLength),
    y: round(y / pointersLength)
  };
}
function simpleCloneInputData(input) {
  var pointers = [];
  var i = 0;
  while (i < input.pointers.length) {
    pointers[i] = {
      clientX: round(input.pointers[i].clientX),
      clientY: round(input.pointers[i].clientY)
    };
    i++;
  }
  return {
    timeStamp: now(),
    pointers,
    center: getCenter(pointers),
    deltaX: input.deltaX,
    deltaY: input.deltaY
  };
}
function getDistance(p1, p2, props) {
  if (!props) {
    props = PROPS_XY;
  }
  var x = p2[props[0]] - p1[props[0]];
  var y = p2[props[1]] - p1[props[1]];
  return Math.sqrt(x * x + y * y);
}
function getAngle(p1, p2, props) {
  if (!props) {
    props = PROPS_XY;
  }
  var x = p2[props[0]] - p1[props[0]];
  var y = p2[props[1]] - p1[props[1]];
  return Math.atan2(y, x) * 180 / Math.PI;
}
function getDirection(x, y) {
  if (x === y) {
    return DIRECTION_NONE;
  }
  if (abs(x) >= abs(y)) {
    return x < 0 ? DIRECTION_LEFT : DIRECTION_RIGHT;
  }
  return y < 0 ? DIRECTION_UP : DIRECTION_DOWN;
}
function computeDeltaXY(session, input) {
  var center = input.center;
  var offset = session.offsetDelta || {};
  var prevDelta = session.prevDelta || {};
  var prevInput = session.prevInput || {};
  if (input.eventType === INPUT_START || prevInput.eventType === INPUT_END) {
    prevDelta = session.prevDelta = {
      x: prevInput.deltaX || 0,
      y: prevInput.deltaY || 0
    };
    offset = session.offsetDelta = {
      x: center.x,
      y: center.y
    };
  }
  input.deltaX = prevDelta.x + (center.x - offset.x);
  input.deltaY = prevDelta.y + (center.y - offset.y);
}
function getVelocity(deltaTime, x, y) {
  return {
    x: x / deltaTime || 0,
    y: y / deltaTime || 0
  };
}
function getScale(start, end) {
  return getDistance(end[0], end[1], PROPS_CLIENT_XY) / getDistance(start[0], start[1], PROPS_CLIENT_XY);
}
function getRotation(start, end) {
  return getAngle(end[1], end[0], PROPS_CLIENT_XY) + getAngle(start[1], start[0], PROPS_CLIENT_XY);
}
function computeIntervalInputData(session, input) {
  var last = session.lastInterval || input;
  var deltaTime = input.timeStamp - last.timeStamp;
  var velocity;
  var velocityX;
  var velocityY;
  var direction;
  if (input.eventType !== INPUT_CANCEL && (deltaTime > COMPUTE_INTERVAL || last.velocity === void 0)) {
    var deltaX = input.deltaX - last.deltaX;
    var deltaY = input.deltaY - last.deltaY;
    var v = getVelocity(deltaTime, deltaX, deltaY);
    velocityX = v.x;
    velocityY = v.y;
    velocity = abs(v.x) > abs(v.y) ? v.x : v.y;
    direction = getDirection(deltaX, deltaY);
    session.lastInterval = input;
  } else {
    velocity = last.velocity;
    velocityX = last.velocityX;
    velocityY = last.velocityY;
    direction = last.direction;
  }
  input.velocity = velocity;
  input.velocityX = velocityX;
  input.velocityY = velocityY;
  input.direction = direction;
}
function computeInputData(manager, input) {
  var session = manager.session;
  var pointers = input.pointers;
  var pointersLength = pointers.length;
  if (!session.firstInput) {
    session.firstInput = simpleCloneInputData(input);
  }
  if (pointersLength > 1 && !session.firstMultiple) {
    session.firstMultiple = simpleCloneInputData(input);
  } else if (pointersLength === 1) {
    session.firstMultiple = false;
  }
  var firstInput = session.firstInput, firstMultiple = session.firstMultiple;
  var offsetCenter = firstMultiple ? firstMultiple.center : firstInput.center;
  var center = input.center = getCenter(pointers);
  input.timeStamp = now();
  input.deltaTime = input.timeStamp - firstInput.timeStamp;
  input.angle = getAngle(offsetCenter, center);
  input.distance = getDistance(offsetCenter, center);
  computeDeltaXY(session, input);
  input.offsetDirection = getDirection(input.deltaX, input.deltaY);
  var overallVelocity = getVelocity(input.deltaTime, input.deltaX, input.deltaY);
  input.overallVelocityX = overallVelocity.x;
  input.overallVelocityY = overallVelocity.y;
  input.overallVelocity = abs(overallVelocity.x) > abs(overallVelocity.y) ? overallVelocity.x : overallVelocity.y;
  input.scale = firstMultiple ? getScale(firstMultiple.pointers, pointers) : 1;
  input.rotation = firstMultiple ? getRotation(firstMultiple.pointers, pointers) : 0;
  input.maxPointers = !session.prevInput ? input.pointers.length : input.pointers.length > session.prevInput.maxPointers ? input.pointers.length : session.prevInput.maxPointers;
  computeIntervalInputData(session, input);
  var target = manager.element;
  var srcEvent = input.srcEvent;
  var srcEventTarget;
  if (srcEvent.composedPath) {
    srcEventTarget = srcEvent.composedPath()[0];
  } else if (srcEvent.path) {
    srcEventTarget = srcEvent.path[0];
  } else {
    srcEventTarget = srcEvent.target;
  }
  if (hasParent(srcEventTarget, target)) {
    target = srcEventTarget;
  }
  input.target = target;
}
function inputHandler(manager, eventType, input) {
  var pointersLen = input.pointers.length;
  var changedPointersLen = input.changedPointers.length;
  var isFirst = eventType & INPUT_START && pointersLen - changedPointersLen === 0;
  var isFinal = eventType & (INPUT_END | INPUT_CANCEL) && pointersLen - changedPointersLen === 0;
  input.isFirst = !!isFirst;
  input.isFinal = !!isFinal;
  if (isFirst) {
    manager.session = {};
  }
  input.eventType = eventType;
  computeInputData(manager, input);
  manager.emit("hammer.input", input);
  manager.recognize(input);
  manager.session.prevInput = input;
}
function splitStr(str) {
  return str.trim().split(/\s+/g);
}
function addEventListeners(target, types, handler) {
  each(splitStr(types), function(type) {
    target.addEventListener(type, handler, false);
  });
}
function removeEventListeners(target, types, handler) {
  each(splitStr(types), function(type) {
    target.removeEventListener(type, handler, false);
  });
}
function getWindowForElement(element) {
  var doc = element.ownerDocument || element;
  return doc.defaultView || doc.parentWindow || window;
}
var Input = (function() {
  function Input2(manager, callback) {
    var self2 = this;
    this.manager = manager;
    this.callback = callback;
    this.element = manager.element;
    this.target = manager.options.inputTarget;
    this.domHandler = function(ev) {
      if (boolOrFn(manager.options.enable, [manager])) {
        self2.handler(ev);
      }
    };
    this.init();
  }
  var _proto = Input2.prototype;
  _proto.handler = function handler() {
  };
  _proto.init = function init() {
    this.evEl && addEventListeners(this.element, this.evEl, this.domHandler);
    this.evTarget && addEventListeners(this.target, this.evTarget, this.domHandler);
    this.evWin && addEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);
  };
  _proto.destroy = function destroy() {
    this.evEl && removeEventListeners(this.element, this.evEl, this.domHandler);
    this.evTarget && removeEventListeners(this.target, this.evTarget, this.domHandler);
    this.evWin && removeEventListeners(getWindowForElement(this.element), this.evWin, this.domHandler);
  };
  return Input2;
})();
function inArray(src, find, findByKey) {
  if (src.indexOf && !findByKey) {
    return src.indexOf(find);
  } else {
    var i = 0;
    while (i < src.length) {
      if (findByKey && src[i][findByKey] == find || !findByKey && src[i] === find) {
        return i;
      }
      i++;
    }
    return -1;
  }
}
var POINTER_INPUT_MAP = {
  pointerdown: INPUT_START,
  pointermove: INPUT_MOVE,
  pointerup: INPUT_END,
  pointercancel: INPUT_CANCEL,
  pointerout: INPUT_CANCEL
};
var IE10_POINTER_TYPE_ENUM = {
  2: INPUT_TYPE_TOUCH,
  3: INPUT_TYPE_PEN,
  4: INPUT_TYPE_MOUSE,
  5: INPUT_TYPE_KINECT
  // see https://twitter.com/jacobrossi/status/480596438489890816
};
var POINTER_ELEMENT_EVENTS = "pointerdown";
var POINTER_WINDOW_EVENTS = "pointermove pointerup pointercancel";
if (win.MSPointerEvent && !win.PointerEvent) {
  POINTER_ELEMENT_EVENTS = "MSPointerDown";
  POINTER_WINDOW_EVENTS = "MSPointerMove MSPointerUp MSPointerCancel";
}
var PointerEventInput = (function(_Input) {
  _inheritsLoose(PointerEventInput2, _Input);
  function PointerEventInput2() {
    var _this;
    var proto = PointerEventInput2.prototype;
    proto.evEl = POINTER_ELEMENT_EVENTS;
    proto.evWin = POINTER_WINDOW_EVENTS;
    _this = _Input.apply(this, arguments) || this;
    _this.store = _this.manager.session.pointerEvents = [];
    return _this;
  }
  var _proto = PointerEventInput2.prototype;
  _proto.handler = function handler(ev) {
    var store = this.store;
    var removePointer = false;
    var eventTypeNormalized = ev.type.toLowerCase().replace("ms", "");
    var eventType = POINTER_INPUT_MAP[eventTypeNormalized];
    var pointerType = IE10_POINTER_TYPE_ENUM[ev.pointerType] || ev.pointerType;
    var isTouch = pointerType === INPUT_TYPE_TOUCH;
    var storeIndex = inArray(store, ev.pointerId, "pointerId");
    if (eventType & INPUT_START && (ev.button === 0 || isTouch)) {
      if (storeIndex < 0) {
        store.push(ev);
        storeIndex = store.length - 1;
      }
    } else if (eventType & (INPUT_END | INPUT_CANCEL)) {
      removePointer = true;
    }
    if (storeIndex < 0) {
      return;
    }
    store[storeIndex] = ev;
    this.callback(this.manager, eventType, {
      pointers: store,
      changedPointers: [ev],
      pointerType,
      srcEvent: ev
    });
    if (removePointer) {
      store.splice(storeIndex, 1);
    }
  };
  return PointerEventInput2;
})(Input);
function toArray(obj) {
  return Array.prototype.slice.call(obj, 0);
}
function uniqueArray(src, key, sort2) {
  var results = [];
  var values2 = [];
  var i = 0;
  while (i < src.length) {
    var val = key ? src[i][key] : src[i];
    if (inArray(values2, val) < 0) {
      results.push(src[i]);
    }
    values2[i] = val;
    i++;
  }
  if (sort2) {
    if (!key) {
      results = results.sort();
    } else {
      results = results.sort(function(a, b) {
        return a[key] > b[key];
      });
    }
  }
  return results;
}
var TOUCH_INPUT_MAP = {
  touchstart: INPUT_START,
  touchmove: INPUT_MOVE,
  touchend: INPUT_END,
  touchcancel: INPUT_CANCEL
};
var TOUCH_TARGET_EVENTS = "touchstart touchmove touchend touchcancel";
var TouchInput = (function(_Input) {
  _inheritsLoose(TouchInput2, _Input);
  function TouchInput2() {
    var _this;
    TouchInput2.prototype.evTarget = TOUCH_TARGET_EVENTS;
    _this = _Input.apply(this, arguments) || this;
    _this.targetIds = {};
    return _this;
  }
  var _proto = TouchInput2.prototype;
  _proto.handler = function handler(ev) {
    var type = TOUCH_INPUT_MAP[ev.type];
    var touches = getTouches.call(this, ev, type);
    if (!touches) {
      return;
    }
    this.callback(this.manager, type, {
      pointers: touches[0],
      changedPointers: touches[1],
      pointerType: INPUT_TYPE_TOUCH,
      srcEvent: ev
    });
  };
  return TouchInput2;
})(Input);
function getTouches(ev, type) {
  var allTouches = toArray(ev.touches);
  var targetIds = this.targetIds;
  if (type & (INPUT_START | INPUT_MOVE) && allTouches.length === 1) {
    targetIds[allTouches[0].identifier] = true;
    return [allTouches, allTouches];
  }
  var i;
  var targetTouches;
  var changedTouches = toArray(ev.changedTouches);
  var changedTargetTouches = [];
  var target = this.target;
  targetTouches = allTouches.filter(function(touch) {
    return hasParent(touch.target, target);
  });
  if (type === INPUT_START) {
    i = 0;
    while (i < targetTouches.length) {
      targetIds[targetTouches[i].identifier] = true;
      i++;
    }
  }
  i = 0;
  while (i < changedTouches.length) {
    if (targetIds[changedTouches[i].identifier]) {
      changedTargetTouches.push(changedTouches[i]);
    }
    if (type & (INPUT_END | INPUT_CANCEL)) {
      delete targetIds[changedTouches[i].identifier];
    }
    i++;
  }
  if (!changedTargetTouches.length) {
    return;
  }
  return [
    // merge targetTouches with changedTargetTouches so it contains ALL touches, including 'end' and 'cancel'
    uniqueArray(targetTouches.concat(changedTargetTouches), "identifier", true),
    changedTargetTouches
  ];
}
var MOUSE_INPUT_MAP = {
  mousedown: INPUT_START,
  mousemove: INPUT_MOVE,
  mouseup: INPUT_END
};
var MOUSE_ELEMENT_EVENTS = "mousedown";
var MOUSE_WINDOW_EVENTS = "mousemove mouseup";
var MouseInput = (function(_Input) {
  _inheritsLoose(MouseInput2, _Input);
  function MouseInput2() {
    var _this;
    var proto = MouseInput2.prototype;
    proto.evEl = MOUSE_ELEMENT_EVENTS;
    proto.evWin = MOUSE_WINDOW_EVENTS;
    _this = _Input.apply(this, arguments) || this;
    _this.pressed = false;
    return _this;
  }
  var _proto = MouseInput2.prototype;
  _proto.handler = function handler(ev) {
    var eventType = MOUSE_INPUT_MAP[ev.type];
    if (eventType & INPUT_START && ev.button === 0) {
      this.pressed = true;
    }
    if (eventType & INPUT_MOVE && ev.which !== 1) {
      eventType = INPUT_END;
    }
    if (!this.pressed) {
      return;
    }
    if (eventType & INPUT_END) {
      this.pressed = false;
    }
    this.callback(this.manager, eventType, {
      pointers: [ev],
      changedPointers: [ev],
      pointerType: INPUT_TYPE_MOUSE,
      srcEvent: ev
    });
  };
  return MouseInput2;
})(Input);
var DEDUP_TIMEOUT = 2500;
var DEDUP_DISTANCE = 25;
function setLastTouch(eventData) {
  var _eventData$changedPoi = eventData.changedPointers, touch = _eventData$changedPoi[0];
  if (touch.identifier === this.primaryTouch) {
    var lastTouch = {
      x: touch.clientX,
      y: touch.clientY
    };
    var lts = this.lastTouches;
    this.lastTouches.push(lastTouch);
    var removeLastTouch = function removeLastTouch2() {
      var i = lts.indexOf(lastTouch);
      if (i > -1) {
        lts.splice(i, 1);
      }
    };
    setTimeout(removeLastTouch, DEDUP_TIMEOUT);
  }
}
function recordTouches(eventType, eventData) {
  if (eventType & INPUT_START) {
    this.primaryTouch = eventData.changedPointers[0].identifier;
    setLastTouch.call(this, eventData);
  } else if (eventType & (INPUT_END | INPUT_CANCEL)) {
    setLastTouch.call(this, eventData);
  }
}
function isSyntheticEvent(eventData) {
  var x = eventData.srcEvent.clientX;
  var y = eventData.srcEvent.clientY;
  for (var i = 0; i < this.lastTouches.length; i++) {
    var t = this.lastTouches[i];
    var dx = Math.abs(x - t.x);
    var dy = Math.abs(y - t.y);
    if (dx <= DEDUP_DISTANCE && dy <= DEDUP_DISTANCE) {
      return true;
    }
  }
  return false;
}
var TouchMouseInput = (function() {
  var TouchMouseInput2 = (function(_Input) {
    _inheritsLoose(TouchMouseInput3, _Input);
    function TouchMouseInput3(_manager, callback) {
      var _this;
      _this = _Input.call(this, _manager, callback) || this;
      _this.handler = function(manager, inputEvent, inputData) {
        var isTouch = inputData.pointerType === INPUT_TYPE_TOUCH;
        var isMouse = inputData.pointerType === INPUT_TYPE_MOUSE;
        if (isMouse && inputData.sourceCapabilities && inputData.sourceCapabilities.firesTouchEvents) {
          return;
        }
        if (isTouch) {
          recordTouches.call(_assertThisInitialized(_assertThisInitialized(_this)), inputEvent, inputData);
        } else if (isMouse && isSyntheticEvent.call(_assertThisInitialized(_assertThisInitialized(_this)), inputData)) {
          return;
        }
        _this.callback(manager, inputEvent, inputData);
      };
      _this.touch = new TouchInput(_this.manager, _this.handler);
      _this.mouse = new MouseInput(_this.manager, _this.handler);
      _this.primaryTouch = null;
      _this.lastTouches = [];
      return _this;
    }
    var _proto = TouchMouseInput3.prototype;
    _proto.destroy = function destroy() {
      this.touch.destroy();
      this.mouse.destroy();
    };
    return TouchMouseInput3;
  })(Input);
  return TouchMouseInput2;
})();
function createInputInstance(manager) {
  var Type;
  var inputClass = manager.options.inputClass;
  if (inputClass) {
    Type = inputClass;
  } else if (SUPPORT_POINTER_EVENTS) {
    Type = PointerEventInput;
  } else if (SUPPORT_ONLY_TOUCH) {
    Type = TouchInput;
  } else if (!SUPPORT_TOUCH) {
    Type = MouseInput;
  } else {
    Type = TouchMouseInput;
  }
  return new Type(manager, inputHandler);
}
function invokeArrayArg(arg, fn, context) {
  if (Array.isArray(arg)) {
    each(arg, context[fn], context);
    return true;
  }
  return false;
}
var STATE_POSSIBLE = 1;
var STATE_BEGAN = 2;
var STATE_CHANGED = 4;
var STATE_ENDED = 8;
var STATE_RECOGNIZED = STATE_ENDED;
var STATE_CANCELLED = 16;
var STATE_FAILED = 32;
var _uniqueId = 1;
function uniqueId() {
  return _uniqueId++;
}
function getRecognizerByNameIfManager(otherRecognizer, recognizer) {
  var manager = recognizer.manager;
  if (manager) {
    return manager.get(otherRecognizer);
  }
  return otherRecognizer;
}
function stateStr(state) {
  if (state & STATE_CANCELLED) {
    return "cancel";
  } else if (state & STATE_ENDED) {
    return "end";
  } else if (state & STATE_CHANGED) {
    return "move";
  } else if (state & STATE_BEGAN) {
    return "start";
  }
  return "";
}
var Recognizer = (function() {
  function Recognizer2(options) {
    if (options === void 0) {
      options = {};
    }
    this.options = _extends({
      enable: true
    }, options);
    this.id = uniqueId();
    this.manager = null;
    this.state = STATE_POSSIBLE;
    this.simultaneous = {};
    this.requireFail = [];
  }
  var _proto = Recognizer2.prototype;
  _proto.set = function set2(options) {
    assign$1$1(this.options, options);
    this.manager && this.manager.touchAction.update();
    return this;
  };
  _proto.recognizeWith = function recognizeWith(otherRecognizer) {
    if (invokeArrayArg(otherRecognizer, "recognizeWith", this)) {
      return this;
    }
    var simultaneous = this.simultaneous;
    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);
    if (!simultaneous[otherRecognizer.id]) {
      simultaneous[otherRecognizer.id] = otherRecognizer;
      otherRecognizer.recognizeWith(this);
    }
    return this;
  };
  _proto.dropRecognizeWith = function dropRecognizeWith(otherRecognizer) {
    if (invokeArrayArg(otherRecognizer, "dropRecognizeWith", this)) {
      return this;
    }
    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);
    delete this.simultaneous[otherRecognizer.id];
    return this;
  };
  _proto.requireFailure = function requireFailure(otherRecognizer) {
    if (invokeArrayArg(otherRecognizer, "requireFailure", this)) {
      return this;
    }
    var requireFail = this.requireFail;
    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);
    if (inArray(requireFail, otherRecognizer) === -1) {
      requireFail.push(otherRecognizer);
      otherRecognizer.requireFailure(this);
    }
    return this;
  };
  _proto.dropRequireFailure = function dropRequireFailure(otherRecognizer) {
    if (invokeArrayArg(otherRecognizer, "dropRequireFailure", this)) {
      return this;
    }
    otherRecognizer = getRecognizerByNameIfManager(otherRecognizer, this);
    var index = inArray(this.requireFail, otherRecognizer);
    if (index > -1) {
      this.requireFail.splice(index, 1);
    }
    return this;
  };
  _proto.hasRequireFailures = function hasRequireFailures() {
    return this.requireFail.length > 0;
  };
  _proto.canRecognizeWith = function canRecognizeWith(otherRecognizer) {
    return !!this.simultaneous[otherRecognizer.id];
  };
  _proto.emit = function emit(input) {
    var self2 = this;
    var state = this.state;
    function emit2(event) {
      self2.manager.emit(event, input);
    }
    if (state < STATE_ENDED) {
      emit2(self2.options.event + stateStr(state));
    }
    emit2(self2.options.event);
    if (input.additionalEvent) {
      emit2(input.additionalEvent);
    }
    if (state >= STATE_ENDED) {
      emit2(self2.options.event + stateStr(state));
    }
  };
  _proto.tryEmit = function tryEmit(input) {
    if (this.canEmit()) {
      return this.emit(input);
    }
    this.state = STATE_FAILED;
  };
  _proto.canEmit = function canEmit() {
    var i = 0;
    while (i < this.requireFail.length) {
      if (!(this.requireFail[i].state & (STATE_FAILED | STATE_POSSIBLE))) {
        return false;
      }
      i++;
    }
    return true;
  };
  _proto.recognize = function recognize(inputData) {
    var inputDataClone = assign$1$1({}, inputData);
    if (!boolOrFn(this.options.enable, [this, inputDataClone])) {
      this.reset();
      this.state = STATE_FAILED;
      return;
    }
    if (this.state & (STATE_RECOGNIZED | STATE_CANCELLED | STATE_FAILED)) {
      this.state = STATE_POSSIBLE;
    }
    this.state = this.process(inputDataClone);
    if (this.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED | STATE_CANCELLED)) {
      this.tryEmit(inputDataClone);
    }
  };
  _proto.process = function process(inputData) {
  };
  _proto.getTouchAction = function getTouchAction() {
  };
  _proto.reset = function reset() {
  };
  return Recognizer2;
})();
var TapRecognizer = (function(_Recognizer) {
  _inheritsLoose(TapRecognizer2, _Recognizer);
  function TapRecognizer2(options) {
    var _this;
    if (options === void 0) {
      options = {};
    }
    _this = _Recognizer.call(this, _extends({
      event: "tap",
      pointers: 1,
      taps: 1,
      interval: 300,
      // max time between the multi-tap taps
      time: 250,
      // max time of the pointer to be down (like finger on the screen)
      threshold: 9,
      // a minimal movement is ok, but keep it low
      posThreshold: 10
    }, options)) || this;
    _this.pTime = false;
    _this.pCenter = false;
    _this._timer = null;
    _this._input = null;
    _this.count = 0;
    return _this;
  }
  var _proto = TapRecognizer2.prototype;
  _proto.getTouchAction = function getTouchAction() {
    return [TOUCH_ACTION_MANIPULATION];
  };
  _proto.process = function process(input) {
    var _this2 = this;
    var options = this.options;
    var validPointers = input.pointers.length === options.pointers;
    var validMovement = input.distance < options.threshold;
    var validTouchTime = input.deltaTime < options.time;
    this.reset();
    if (input.eventType & INPUT_START && this.count === 0) {
      return this.failTimeout();
    }
    if (validMovement && validTouchTime && validPointers) {
      if (input.eventType !== INPUT_END) {
        return this.failTimeout();
      }
      var validInterval = this.pTime ? input.timeStamp - this.pTime < options.interval : true;
      var validMultiTap = !this.pCenter || getDistance(this.pCenter, input.center) < options.posThreshold;
      this.pTime = input.timeStamp;
      this.pCenter = input.center;
      if (!validMultiTap || !validInterval) {
        this.count = 1;
      } else {
        this.count += 1;
      }
      this._input = input;
      var tapCount = this.count % options.taps;
      if (tapCount === 0) {
        if (!this.hasRequireFailures()) {
          return STATE_RECOGNIZED;
        } else {
          this._timer = setTimeout(function() {
            _this2.state = STATE_RECOGNIZED;
            _this2.tryEmit();
          }, options.interval);
          return STATE_BEGAN;
        }
      }
    }
    return STATE_FAILED;
  };
  _proto.failTimeout = function failTimeout() {
    var _this3 = this;
    this._timer = setTimeout(function() {
      _this3.state = STATE_FAILED;
    }, this.options.interval);
    return STATE_FAILED;
  };
  _proto.reset = function reset() {
    clearTimeout(this._timer);
  };
  _proto.emit = function emit() {
    if (this.state === STATE_RECOGNIZED) {
      this._input.tapCount = this.count;
      this.manager.emit(this.options.event, this._input);
    }
  };
  return TapRecognizer2;
})(Recognizer);
var AttrRecognizer = (function(_Recognizer) {
  _inheritsLoose(AttrRecognizer2, _Recognizer);
  function AttrRecognizer2(options) {
    if (options === void 0) {
      options = {};
    }
    return _Recognizer.call(this, _extends({
      pointers: 1
    }, options)) || this;
  }
  var _proto = AttrRecognizer2.prototype;
  _proto.attrTest = function attrTest(input) {
    var optionPointers = this.options.pointers;
    return optionPointers === 0 || input.pointers.length === optionPointers;
  };
  _proto.process = function process(input) {
    var state = this.state;
    var eventType = input.eventType;
    var isRecognized = state & (STATE_BEGAN | STATE_CHANGED);
    var isValid = this.attrTest(input);
    if (isRecognized && (eventType & INPUT_CANCEL || !isValid)) {
      return state | STATE_CANCELLED;
    } else if (isRecognized || isValid) {
      if (eventType & INPUT_END) {
        return state | STATE_ENDED;
      } else if (!(state & STATE_BEGAN)) {
        return STATE_BEGAN;
      }
      return state | STATE_CHANGED;
    }
    return STATE_FAILED;
  };
  return AttrRecognizer2;
})(Recognizer);
function directionStr(direction) {
  if (direction === DIRECTION_DOWN) {
    return "down";
  } else if (direction === DIRECTION_UP) {
    return "up";
  } else if (direction === DIRECTION_LEFT) {
    return "left";
  } else if (direction === DIRECTION_RIGHT) {
    return "right";
  }
  return "";
}
var PanRecognizer = (function(_AttrRecognizer) {
  _inheritsLoose(PanRecognizer2, _AttrRecognizer);
  function PanRecognizer2(options) {
    var _this;
    if (options === void 0) {
      options = {};
    }
    _this = _AttrRecognizer.call(this, _extends({
      event: "pan",
      threshold: 10,
      pointers: 1,
      direction: DIRECTION_ALL
    }, options)) || this;
    _this.pX = null;
    _this.pY = null;
    return _this;
  }
  var _proto = PanRecognizer2.prototype;
  _proto.getTouchAction = function getTouchAction() {
    var direction = this.options.direction;
    var actions = [];
    if (direction & DIRECTION_HORIZONTAL) {
      actions.push(TOUCH_ACTION_PAN_Y);
    }
    if (direction & DIRECTION_VERTICAL) {
      actions.push(TOUCH_ACTION_PAN_X);
    }
    return actions;
  };
  _proto.directionTest = function directionTest(input) {
    var options = this.options;
    var hasMoved = true;
    var distance = input.distance;
    var direction = input.direction;
    var x = input.deltaX;
    var y = input.deltaY;
    if (!(direction & options.direction)) {
      if (options.direction & DIRECTION_HORIZONTAL) {
        direction = x === 0 ? DIRECTION_NONE : x < 0 ? DIRECTION_LEFT : DIRECTION_RIGHT;
        hasMoved = x !== this.pX;
        distance = Math.abs(input.deltaX);
      } else {
        direction = y === 0 ? DIRECTION_NONE : y < 0 ? DIRECTION_UP : DIRECTION_DOWN;
        hasMoved = y !== this.pY;
        distance = Math.abs(input.deltaY);
      }
    }
    input.direction = direction;
    return hasMoved && distance > options.threshold && direction & options.direction;
  };
  _proto.attrTest = function attrTest(input) {
    return AttrRecognizer.prototype.attrTest.call(this, input) && // replace with a super call
    (this.state & STATE_BEGAN || !(this.state & STATE_BEGAN) && this.directionTest(input));
  };
  _proto.emit = function emit(input) {
    this.pX = input.deltaX;
    this.pY = input.deltaY;
    var direction = directionStr(input.direction);
    if (direction) {
      input.additionalEvent = this.options.event + direction;
    }
    _AttrRecognizer.prototype.emit.call(this, input);
  };
  return PanRecognizer2;
})(AttrRecognizer);
var SwipeRecognizer = (function(_AttrRecognizer) {
  _inheritsLoose(SwipeRecognizer2, _AttrRecognizer);
  function SwipeRecognizer2(options) {
    if (options === void 0) {
      options = {};
    }
    return _AttrRecognizer.call(this, _extends({
      event: "swipe",
      threshold: 10,
      velocity: 0.3,
      direction: DIRECTION_HORIZONTAL | DIRECTION_VERTICAL,
      pointers: 1
    }, options)) || this;
  }
  var _proto = SwipeRecognizer2.prototype;
  _proto.getTouchAction = function getTouchAction() {
    return PanRecognizer.prototype.getTouchAction.call(this);
  };
  _proto.attrTest = function attrTest(input) {
    var direction = this.options.direction;
    var velocity;
    if (direction & (DIRECTION_HORIZONTAL | DIRECTION_VERTICAL)) {
      velocity = input.overallVelocity;
    } else if (direction & DIRECTION_HORIZONTAL) {
      velocity = input.overallVelocityX;
    } else if (direction & DIRECTION_VERTICAL) {
      velocity = input.overallVelocityY;
    }
    return _AttrRecognizer.prototype.attrTest.call(this, input) && direction & input.offsetDirection && input.distance > this.options.threshold && input.maxPointers === this.options.pointers && abs(velocity) > this.options.velocity && input.eventType & INPUT_END;
  };
  _proto.emit = function emit(input) {
    var direction = directionStr(input.offsetDirection);
    if (direction) {
      this.manager.emit(this.options.event + direction, input);
    }
    this.manager.emit(this.options.event, input);
  };
  return SwipeRecognizer2;
})(AttrRecognizer);
var PinchRecognizer = (function(_AttrRecognizer) {
  _inheritsLoose(PinchRecognizer2, _AttrRecognizer);
  function PinchRecognizer2(options) {
    if (options === void 0) {
      options = {};
    }
    return _AttrRecognizer.call(this, _extends({
      event: "pinch",
      threshold: 0,
      pointers: 2
    }, options)) || this;
  }
  var _proto = PinchRecognizer2.prototype;
  _proto.getTouchAction = function getTouchAction() {
    return [TOUCH_ACTION_NONE];
  };
  _proto.attrTest = function attrTest(input) {
    return _AttrRecognizer.prototype.attrTest.call(this, input) && (Math.abs(input.scale - 1) > this.options.threshold || this.state & STATE_BEGAN);
  };
  _proto.emit = function emit(input) {
    if (input.scale !== 1) {
      var inOut = input.scale < 1 ? "in" : "out";
      input.additionalEvent = this.options.event + inOut;
    }
    _AttrRecognizer.prototype.emit.call(this, input);
  };
  return PinchRecognizer2;
})(AttrRecognizer);
var RotateRecognizer = (function(_AttrRecognizer) {
  _inheritsLoose(RotateRecognizer2, _AttrRecognizer);
  function RotateRecognizer2(options) {
    if (options === void 0) {
      options = {};
    }
    return _AttrRecognizer.call(this, _extends({
      event: "rotate",
      threshold: 0,
      pointers: 2
    }, options)) || this;
  }
  var _proto = RotateRecognizer2.prototype;
  _proto.getTouchAction = function getTouchAction() {
    return [TOUCH_ACTION_NONE];
  };
  _proto.attrTest = function attrTest(input) {
    return _AttrRecognizer.prototype.attrTest.call(this, input) && (Math.abs(input.rotation) > this.options.threshold || this.state & STATE_BEGAN);
  };
  return RotateRecognizer2;
})(AttrRecognizer);
var PressRecognizer = (function(_Recognizer) {
  _inheritsLoose(PressRecognizer2, _Recognizer);
  function PressRecognizer2(options) {
    var _this;
    if (options === void 0) {
      options = {};
    }
    _this = _Recognizer.call(this, _extends({
      event: "press",
      pointers: 1,
      time: 251,
      // minimal time of the pointer to be pressed
      threshold: 9
    }, options)) || this;
    _this._timer = null;
    _this._input = null;
    return _this;
  }
  var _proto = PressRecognizer2.prototype;
  _proto.getTouchAction = function getTouchAction() {
    return [TOUCH_ACTION_AUTO];
  };
  _proto.process = function process(input) {
    var _this2 = this;
    var options = this.options;
    var validPointers = input.pointers.length === options.pointers;
    var validMovement = input.distance < options.threshold;
    var validTime = input.deltaTime > options.time;
    this._input = input;
    if (!validMovement || !validPointers || input.eventType & (INPUT_END | INPUT_CANCEL) && !validTime) {
      this.reset();
    } else if (input.eventType & INPUT_START) {
      this.reset();
      this._timer = setTimeout(function() {
        _this2.state = STATE_RECOGNIZED;
        _this2.tryEmit();
      }, options.time);
    } else if (input.eventType & INPUT_END) {
      return STATE_RECOGNIZED;
    }
    return STATE_FAILED;
  };
  _proto.reset = function reset() {
    clearTimeout(this._timer);
  };
  _proto.emit = function emit(input) {
    if (this.state !== STATE_RECOGNIZED) {
      return;
    }
    if (input && input.eventType & INPUT_END) {
      this.manager.emit(this.options.event + "up", input);
    } else {
      this._input.timeStamp = now();
      this.manager.emit(this.options.event, this._input);
    }
  };
  return PressRecognizer2;
})(Recognizer);
var defaults = {
  /**
   * @private
   * set if DOM events are being triggered.
   * But this is slower and unused by simple implementations, so disabled by default.
   * @type {Boolean}
   * @default false
   */
  domEvents: false,
  /**
   * @private
   * The value for the touchAction property/fallback.
   * When set to `compute` it will magically set the correct value based on the added recognizers.
   * @type {String}
   * @default compute
   */
  touchAction: TOUCH_ACTION_COMPUTE,
  /**
   * @private
   * @type {Boolean}
   * @default true
   */
  enable: true,
  /**
   * @private
   * EXPERIMENTAL FEATURE -- can be removed/changed
   * Change the parent input target element.
   * If Null, then it is being set the to main element.
   * @type {Null|EventTarget}
   * @default null
   */
  inputTarget: null,
  /**
   * @private
   * force an input class
   * @type {Null|Function}
   * @default null
   */
  inputClass: null,
  /**
   * @private
   * Some CSS properties can be used to improve the working of Hammer.
   * Add them to this method and they will be set when creating a new Manager.
   * @namespace
   */
  cssProps: {
    /**
     * @private
     * Disables text selection to improve the dragging gesture. Mainly for desktop browsers.
     * @type {String}
     * @default 'none'
     */
    userSelect: "none",
    /**
     * @private
     * Disable the Windows Phone grippers when pressing an element.
     * @type {String}
     * @default 'none'
     */
    touchSelect: "none",
    /**
     * @private
     * Disables the default callout shown when you touch and hold a touch target.
     * On iOS, when you touch and hold a touch target such as a link, Safari displays
     * a callout containing information about the link. This property allows you to disable that callout.
     * @type {String}
     * @default 'none'
     */
    touchCallout: "none",
    /**
     * @private
     * Specifies whether zooming is enabled. Used by IE10>
     * @type {String}
     * @default 'none'
     */
    contentZooming: "none",
    /**
     * @private
     * Specifies that an entire element should be draggable instead of its contents. Mainly for desktop browsers.
     * @type {String}
     * @default 'none'
     */
    userDrag: "none",
    /**
     * @private
     * Overrides the highlight color shown when the user taps a link or a JavaScript
     * clickable element in iOS. This property obeys the alpha value, if specified.
     * @type {String}
     * @default 'rgba(0,0,0,0)'
     */
    tapHighlightColor: "rgba(0,0,0,0)"
  }
};
var preset = [[RotateRecognizer, {
  enable: false
}], [PinchRecognizer, {
  enable: false
}, ["rotate"]], [SwipeRecognizer, {
  direction: DIRECTION_HORIZONTAL
}], [PanRecognizer, {
  direction: DIRECTION_HORIZONTAL
}, ["swipe"]], [TapRecognizer], [TapRecognizer, {
  event: "doubletap",
  taps: 2
}, ["tap"]], [PressRecognizer]];
var STOP = 1;
var FORCED_STOP = 2;
function toggleCssProps(manager, add) {
  var element = manager.element;
  if (!element.style) {
    return;
  }
  var prop;
  each(manager.options.cssProps, function(value, name) {
    prop = prefixed(element.style, name);
    if (add) {
      manager.oldCssProps[prop] = element.style[prop];
      element.style[prop] = value;
    } else {
      element.style[prop] = manager.oldCssProps[prop] || "";
    }
  });
  if (!add) {
    manager.oldCssProps = {};
  }
}
function triggerDomEvent(event, data) {
  var gestureEvent = document.createEvent("Event");
  gestureEvent.initEvent(event, true, true);
  gestureEvent.gesture = data;
  data.target.dispatchEvent(gestureEvent);
}
var Manager = (function() {
  function Manager2(element, options) {
    var _this = this;
    this.options = assign$1$1({}, defaults, options || {});
    this.options.inputTarget = this.options.inputTarget || element;
    this.handlers = {};
    this.session = {};
    this.recognizers = [];
    this.oldCssProps = {};
    this.element = element;
    this.input = createInputInstance(this);
    this.touchAction = new TouchAction(this, this.options.touchAction);
    toggleCssProps(this, true);
    each(this.options.recognizers, function(item) {
      var recognizer = _this.add(new item[0](item[1]));
      item[2] && recognizer.recognizeWith(item[2]);
      item[3] && recognizer.requireFailure(item[3]);
    }, this);
  }
  var _proto = Manager2.prototype;
  _proto.set = function set2(options) {
    assign$1$1(this.options, options);
    if (options.touchAction) {
      this.touchAction.update();
    }
    if (options.inputTarget) {
      this.input.destroy();
      this.input.target = options.inputTarget;
      this.input.init();
    }
    return this;
  };
  _proto.stop = function stop(force) {
    this.session.stopped = force ? FORCED_STOP : STOP;
  };
  _proto.recognize = function recognize(inputData) {
    var session = this.session;
    if (session.stopped) {
      return;
    }
    this.touchAction.preventDefaults(inputData);
    var recognizer;
    var recognizers = this.recognizers;
    var curRecognizer = session.curRecognizer;
    if (!curRecognizer || curRecognizer && curRecognizer.state & STATE_RECOGNIZED) {
      session.curRecognizer = null;
      curRecognizer = null;
    }
    var i = 0;
    while (i < recognizers.length) {
      recognizer = recognizers[i];
      if (session.stopped !== FORCED_STOP && // 1
      (!curRecognizer || recognizer === curRecognizer || // 2
      recognizer.canRecognizeWith(curRecognizer))) {
        recognizer.recognize(inputData);
      } else {
        recognizer.reset();
      }
      if (!curRecognizer && recognizer.state & (STATE_BEGAN | STATE_CHANGED | STATE_ENDED)) {
        session.curRecognizer = recognizer;
        curRecognizer = recognizer;
      }
      i++;
    }
  };
  _proto.get = function get(recognizer) {
    if (recognizer instanceof Recognizer) {
      return recognizer;
    }
    var recognizers = this.recognizers;
    for (var i = 0; i < recognizers.length; i++) {
      if (recognizers[i].options.event === recognizer) {
        return recognizers[i];
      }
    }
    return null;
  };
  _proto.add = function add(recognizer) {
    if (invokeArrayArg(recognizer, "add", this)) {
      return this;
    }
    var existing = this.get(recognizer.options.event);
    if (existing) {
      this.remove(existing);
    }
    this.recognizers.push(recognizer);
    recognizer.manager = this;
    this.touchAction.update();
    return recognizer;
  };
  _proto.remove = function remove(recognizer) {
    if (invokeArrayArg(recognizer, "remove", this)) {
      return this;
    }
    var targetRecognizer = this.get(recognizer);
    if (recognizer) {
      var recognizers = this.recognizers;
      var index = inArray(recognizers, targetRecognizer);
      if (index !== -1) {
        recognizers.splice(index, 1);
        this.touchAction.update();
      }
    }
    return this;
  };
  _proto.on = function on(events, handler) {
    if (events === void 0 || handler === void 0) {
      return this;
    }
    var handlers = this.handlers;
    each(splitStr(events), function(event) {
      handlers[event] = handlers[event] || [];
      handlers[event].push(handler);
    });
    return this;
  };
  _proto.off = function off(events, handler) {
    if (events === void 0) {
      return this;
    }
    var handlers = this.handlers;
    each(splitStr(events), function(event) {
      if (!handler) {
        delete handlers[event];
      } else {
        handlers[event] && handlers[event].splice(inArray(handlers[event], handler), 1);
      }
    });
    return this;
  };
  _proto.emit = function emit(event, data) {
    if (this.options.domEvents) {
      triggerDomEvent(event, data);
    }
    var handlers = this.handlers[event] && this.handlers[event].slice();
    if (!handlers || !handlers.length) {
      return;
    }
    data.type = event;
    data.preventDefault = function() {
      data.srcEvent.preventDefault();
    };
    var i = 0;
    while (i < handlers.length) {
      handlers[i](data);
      i++;
    }
  };
  _proto.destroy = function destroy() {
    this.element && toggleCssProps(this, false);
    this.handlers = {};
    this.session = {};
    this.input.destroy();
    this.element = null;
  };
  return Manager2;
})();
var SINGLE_TOUCH_INPUT_MAP = {
  touchstart: INPUT_START,
  touchmove: INPUT_MOVE,
  touchend: INPUT_END,
  touchcancel: INPUT_CANCEL
};
var SINGLE_TOUCH_TARGET_EVENTS = "touchstart";
var SINGLE_TOUCH_WINDOW_EVENTS = "touchstart touchmove touchend touchcancel";
var SingleTouchInput = (function(_Input) {
  _inheritsLoose(SingleTouchInput2, _Input);
  function SingleTouchInput2() {
    var _this;
    var proto = SingleTouchInput2.prototype;
    proto.evTarget = SINGLE_TOUCH_TARGET_EVENTS;
    proto.evWin = SINGLE_TOUCH_WINDOW_EVENTS;
    _this = _Input.apply(this, arguments) || this;
    _this.started = false;
    return _this;
  }
  var _proto = SingleTouchInput2.prototype;
  _proto.handler = function handler(ev) {
    var type = SINGLE_TOUCH_INPUT_MAP[ev.type];
    if (type === INPUT_START) {
      this.started = true;
    }
    if (!this.started) {
      return;
    }
    var touches = normalizeSingleTouches.call(this, ev, type);
    if (type & (INPUT_END | INPUT_CANCEL) && touches[0].length - touches[1].length === 0) {
      this.started = false;
    }
    this.callback(this.manager, type, {
      pointers: touches[0],
      changedPointers: touches[1],
      pointerType: INPUT_TYPE_TOUCH,
      srcEvent: ev
    });
  };
  return SingleTouchInput2;
})(Input);
function normalizeSingleTouches(ev, type) {
  var all = toArray(ev.touches);
  var changed = toArray(ev.changedTouches);
  if (type & (INPUT_END | INPUT_CANCEL)) {
    all = uniqueArray(all.concat(changed), "identifier", true);
  }
  return [all, changed];
}
function deprecate(method, name, message) {
  var deprecationMessage = "DEPRECATED METHOD: " + name + "\n" + message + " AT \n";
  return function() {
    var e = new Error("get-stack-trace");
    var stack = e && e.stack ? e.stack.replace(/^[^\(]+?[\n$]/gm, "").replace(/^\s+at\s+/gm, "").replace(/^Object.<anonymous>\s*\(/gm, "{anonymous}()@") : "Unknown Stack Trace";
    var log = window.console && (window.console.warn || window.console.log);
    if (log) {
      log.call(window.console, deprecationMessage, stack);
    }
    return method.apply(this, arguments);
  };
}
var extend = deprecate(function(dest, src, merge2) {
  var keys2 = Object.keys(src);
  var i = 0;
  while (i < keys2.length) {
    if (!merge2 || merge2 && dest[keys2[i]] === void 0) {
      dest[keys2[i]] = src[keys2[i]];
    }
    i++;
  }
  return dest;
}, "extend", "Use `assign`.");
var merge = deprecate(function(dest, src) {
  return extend(dest, src, true);
}, "merge", "Use `assign`.");
function inherit(child, base, properties) {
  var baseP = base.prototype;
  var childP;
  childP = child.prototype = Object.create(baseP);
  childP.constructor = child;
  childP._super = baseP;
  if (properties) {
    assign$1$1(childP, properties);
  }
}
function bindFn(fn, context) {
  return function boundFn() {
    return fn.apply(context, arguments);
  };
}
var Hammer = (function() {
  var Hammer2 = (
    /**
      * @private
      * @const {string}
      */
    function Hammer3(element, options) {
      if (options === void 0) {
        options = {};
      }
      return new Manager(element, _extends({
        recognizers: preset.concat()
      }, options));
    }
  );
  Hammer2.VERSION = "2.0.17-rc";
  Hammer2.DIRECTION_ALL = DIRECTION_ALL;
  Hammer2.DIRECTION_DOWN = DIRECTION_DOWN;
  Hammer2.DIRECTION_LEFT = DIRECTION_LEFT;
  Hammer2.DIRECTION_RIGHT = DIRECTION_RIGHT;
  Hammer2.DIRECTION_UP = DIRECTION_UP;
  Hammer2.DIRECTION_HORIZONTAL = DIRECTION_HORIZONTAL;
  Hammer2.DIRECTION_VERTICAL = DIRECTION_VERTICAL;
  Hammer2.DIRECTION_NONE = DIRECTION_NONE;
  Hammer2.DIRECTION_DOWN = DIRECTION_DOWN;
  Hammer2.INPUT_START = INPUT_START;
  Hammer2.INPUT_MOVE = INPUT_MOVE;
  Hammer2.INPUT_END = INPUT_END;
  Hammer2.INPUT_CANCEL = INPUT_CANCEL;
  Hammer2.STATE_POSSIBLE = STATE_POSSIBLE;
  Hammer2.STATE_BEGAN = STATE_BEGAN;
  Hammer2.STATE_CHANGED = STATE_CHANGED;
  Hammer2.STATE_ENDED = STATE_ENDED;
  Hammer2.STATE_RECOGNIZED = STATE_RECOGNIZED;
  Hammer2.STATE_CANCELLED = STATE_CANCELLED;
  Hammer2.STATE_FAILED = STATE_FAILED;
  Hammer2.Manager = Manager;
  Hammer2.Input = Input;
  Hammer2.TouchAction = TouchAction;
  Hammer2.TouchInput = TouchInput;
  Hammer2.MouseInput = MouseInput;
  Hammer2.PointerEventInput = PointerEventInput;
  Hammer2.TouchMouseInput = TouchMouseInput;
  Hammer2.SingleTouchInput = SingleTouchInput;
  Hammer2.Recognizer = Recognizer;
  Hammer2.AttrRecognizer = AttrRecognizer;
  Hammer2.Tap = TapRecognizer;
  Hammer2.Pan = PanRecognizer;
  Hammer2.Swipe = SwipeRecognizer;
  Hammer2.Pinch = PinchRecognizer;
  Hammer2.Rotate = RotateRecognizer;
  Hammer2.Press = PressRecognizer;
  Hammer2.on = addEventListeners;
  Hammer2.off = removeEventListeners;
  Hammer2.each = each;
  Hammer2.merge = merge;
  Hammer2.extend = extend;
  Hammer2.bindFn = bindFn;
  Hammer2.assign = assign$1$1;
  Hammer2.inherit = inherit;
  Hammer2.bindFn = bindFn;
  Hammer2.prefixed = prefixed;
  Hammer2.toArray = toArray;
  Hammer2.inArray = inArray;
  Hammer2.uniqueArray = uniqueArray;
  Hammer2.splitStr = splitStr;
  Hammer2.boolOrFn = boolOrFn;
  Hammer2.hasParent = hasParent;
  Hammer2.addEventListeners = addEventListeners;
  Hammer2.removeEventListeners = removeEventListeners;
  Hammer2.defaults = assign$1$1({}, defaults, {
    preset
  });
  return Hammer2;
})();
var DELETE = Symbol("DELETE");
function pureDeepObjectAssign(base, ...updates) {
  return deepObjectAssign({}, base, ...updates);
}
function deepObjectAssign(...values2) {
  const merged = deepObjectAssignNonentry(...values2);
  stripDelete(merged);
  return merged;
}
function deepObjectAssignNonentry(...values2) {
  if (values2.length < 2) {
    return values2[0];
  } else if (values2.length > 2) {
    return deepObjectAssignNonentry(deepObjectAssign(values2[0], values2[1]), ...values2.slice(2));
  }
  const a = values2[0];
  const b = values2[1];
  if (a instanceof Date && b instanceof Date) {
    a.setTime(b.getTime());
    return a;
  }
  for (const prop of Reflect.ownKeys(b)) {
    if (!Object.prototype.propertyIsEnumerable.call(b, prop)) ;
    else if (b[prop] === DELETE) {
      delete a[prop];
    } else if (a[prop] !== null && b[prop] !== null && typeof a[prop] === "object" && typeof b[prop] === "object" && !Array.isArray(a[prop]) && !Array.isArray(b[prop])) {
      a[prop] = deepObjectAssignNonentry(a[prop], b[prop]);
    } else {
      a[prop] = clone(b[prop]);
    }
  }
  return a;
}
function clone(a) {
  if (Array.isArray(a)) {
    return a.map((value) => clone(value));
  } else if (typeof a === "object" && a !== null) {
    if (a instanceof Date) {
      return new Date(a.getTime());
    }
    return deepObjectAssignNonentry({}, a);
  } else {
    return a;
  }
}
function stripDelete(a) {
  for (const prop of Object.keys(a)) {
    if (a[prop] === DELETE) {
      delete a[prop];
    } else if (typeof a[prop] === "object" && a[prop] !== null) {
      stripDelete(a[prop]);
    }
  }
}
function hammerMock() {
  const noop = () => {
  };
  return {
    on: noop,
    off: noop,
    destroy: noop,
    emit: noop,
    get() {
      return {
        set: noop
      };
    }
  };
}
var Hammer$1 = typeof window !== "undefined" ? window.Hammer || Hammer : function() {
  return hammerMock();
};
function Activator$1(container) {
  this._cleanupQueue = [];
  this.active = false;
  this._dom = {
    container,
    overlay: document.createElement("div")
  };
  this._dom.overlay.classList.add("vis-overlay");
  this._dom.container.appendChild(this._dom.overlay);
  this._cleanupQueue.push(() => {
    this._dom.overlay.parentNode.removeChild(this._dom.overlay);
  });
  const hammer = Hammer$1(this._dom.overlay);
  hammer.on("tap", this._onTapOverlay.bind(this));
  this._cleanupQueue.push(() => {
    hammer.destroy();
  });
  const events = [
    "tap",
    "doubletap",
    "press",
    "pinch",
    "pan",
    "panstart",
    "panmove",
    "panend"
  ];
  events.forEach((event) => {
    hammer.on(event, (event2) => {
      event2.srcEvent.stopPropagation();
    });
  });
  if (document && document.body) {
    this._onClick = (event) => {
      if (!_hasParent(event.target, container)) {
        this.deactivate();
      }
    };
    document.body.addEventListener("click", this._onClick);
    this._cleanupQueue.push(() => {
      document.body.removeEventListener("click", this._onClick);
    });
  }
  this._escListener = (event) => {
    if ("key" in event ? event.key === "Escape" : event.keyCode === 27) {
      this.deactivate();
    }
  };
}
Emitter(Activator$1.prototype);
Activator$1.current = null;
Activator$1.prototype.destroy = function() {
  this.deactivate();
  for (const callback of this._cleanupQueue.splice(0).reverse()) {
    callback();
  }
};
Activator$1.prototype.activate = function() {
  if (Activator$1.current) {
    Activator$1.current.deactivate();
  }
  Activator$1.current = this;
  this.active = true;
  this._dom.overlay.style.display = "none";
  this._dom.container.classList.add("vis-active");
  this.emit("change");
  this.emit("activate");
  document.body.addEventListener("keydown", this._escListener);
};
Activator$1.prototype.deactivate = function() {
  this.active = false;
  this._dom.overlay.style.display = "block";
  this._dom.container.classList.remove("vis-active");
  document.body.removeEventListener("keydown", this._escListener);
  this.emit("change");
  this.emit("deactivate");
};
Activator$1.prototype._onTapOverlay = function(event) {
  this.activate();
  event.srcEvent.stopPropagation();
};
function _hasParent(element, parent) {
  while (element) {
    if (element === parent) {
      return true;
    }
    element = element.parentNode;
  }
  return false;
}
var getOwnPropertySymbols$2;
var hasRequiredGetOwnPropertySymbols$2;
function requireGetOwnPropertySymbols$2() {
  if (hasRequiredGetOwnPropertySymbols$2) return getOwnPropertySymbols$2;
  hasRequiredGetOwnPropertySymbols$2 = 1;
  requireEs_symbol();
  var path2 = requirePath();
  getOwnPropertySymbols$2 = path2.Object.getOwnPropertySymbols;
  return getOwnPropertySymbols$2;
}
var getOwnPropertySymbols$1;
var hasRequiredGetOwnPropertySymbols$1;
function requireGetOwnPropertySymbols$1() {
  if (hasRequiredGetOwnPropertySymbols$1) return getOwnPropertySymbols$1;
  hasRequiredGetOwnPropertySymbols$1 = 1;
  var parent = requireGetOwnPropertySymbols$2();
  getOwnPropertySymbols$1 = parent;
  return getOwnPropertySymbols$1;
}
var getOwnPropertySymbols;
var hasRequiredGetOwnPropertySymbols;
function requireGetOwnPropertySymbols() {
  if (hasRequiredGetOwnPropertySymbols) return getOwnPropertySymbols;
  hasRequiredGetOwnPropertySymbols = 1;
  getOwnPropertySymbols = requireGetOwnPropertySymbols$1();
  return getOwnPropertySymbols;
}
var getOwnPropertySymbolsExports = requireGetOwnPropertySymbols();
var _Object$getOwnPropertySymbols = getDefaultExportFromCjs(getOwnPropertySymbolsExports);
var getOwnPropertyDescriptor$2 = { exports: {} };
var es_object_getOwnPropertyDescriptor = {};
var hasRequiredEs_object_getOwnPropertyDescriptor;
function requireEs_object_getOwnPropertyDescriptor() {
  if (hasRequiredEs_object_getOwnPropertyDescriptor) return es_object_getOwnPropertyDescriptor;
  hasRequiredEs_object_getOwnPropertyDescriptor = 1;
  var $ = require_export();
  var fails2 = requireFails();
  var toIndexedObject2 = requireToIndexedObject();
  var nativeGetOwnPropertyDescriptor = requireObjectGetOwnPropertyDescriptor().f;
  var DESCRIPTORS = requireDescriptors();
  var FORCED = !DESCRIPTORS || fails2(function() {
    nativeGetOwnPropertyDescriptor(1);
  });
  $({ target: "Object", stat: true, forced: FORCED, sham: !DESCRIPTORS }, {
    getOwnPropertyDescriptor: function getOwnPropertyDescriptor2(it, key) {
      return nativeGetOwnPropertyDescriptor(toIndexedObject2(it), key);
    }
  });
  return es_object_getOwnPropertyDescriptor;
}
var hasRequiredGetOwnPropertyDescriptor$2;
function requireGetOwnPropertyDescriptor$2() {
  if (hasRequiredGetOwnPropertyDescriptor$2) return getOwnPropertyDescriptor$2.exports;
  hasRequiredGetOwnPropertyDescriptor$2 = 1;
  requireEs_object_getOwnPropertyDescriptor();
  var path2 = requirePath();
  var Object2 = path2.Object;
  var getOwnPropertyDescriptor2 = getOwnPropertyDescriptor$2.exports = function getOwnPropertyDescriptor3(it, key) {
    return Object2.getOwnPropertyDescriptor(it, key);
  };
  if (Object2.getOwnPropertyDescriptor.sham) getOwnPropertyDescriptor2.sham = true;
  return getOwnPropertyDescriptor$2.exports;
}
var getOwnPropertyDescriptor$1;
var hasRequiredGetOwnPropertyDescriptor$1;
function requireGetOwnPropertyDescriptor$1() {
  if (hasRequiredGetOwnPropertyDescriptor$1) return getOwnPropertyDescriptor$1;
  hasRequiredGetOwnPropertyDescriptor$1 = 1;
  var parent = requireGetOwnPropertyDescriptor$2();
  getOwnPropertyDescriptor$1 = parent;
  return getOwnPropertyDescriptor$1;
}
var getOwnPropertyDescriptor;
var hasRequiredGetOwnPropertyDescriptor;
function requireGetOwnPropertyDescriptor() {
  if (hasRequiredGetOwnPropertyDescriptor) return getOwnPropertyDescriptor;
  hasRequiredGetOwnPropertyDescriptor = 1;
  getOwnPropertyDescriptor = requireGetOwnPropertyDescriptor$1();
  return getOwnPropertyDescriptor;
}
var getOwnPropertyDescriptorExports = requireGetOwnPropertyDescriptor();
var _Object$getOwnPropertyDescriptor = getDefaultExportFromCjs(getOwnPropertyDescriptorExports);
var es_array_forEach = {};
var arrayForEach;
var hasRequiredArrayForEach;
function requireArrayForEach() {
  if (hasRequiredArrayForEach) return arrayForEach;
  hasRequiredArrayForEach = 1;
  var $forEach = requireArrayIteration().forEach;
  var arrayMethodIsStrict2 = requireArrayMethodIsStrict();
  var STRICT_METHOD = arrayMethodIsStrict2("forEach");
  arrayForEach = !STRICT_METHOD ? function forEach2(callbackfn) {
    return $forEach(this, callbackfn, arguments.length > 1 ? arguments[1] : void 0);
  } : [].forEach;
  return arrayForEach;
}
var hasRequiredEs_array_forEach;
function requireEs_array_forEach() {
  if (hasRequiredEs_array_forEach) return es_array_forEach;
  hasRequiredEs_array_forEach = 1;
  var $ = require_export();
  var forEach2 = requireArrayForEach();
  $({ target: "Array", proto: true, forced: [].forEach !== forEach2 }, {
    forEach: forEach2
  });
  return es_array_forEach;
}
var forEach$3;
var hasRequiredForEach$3;
function requireForEach$3() {
  if (hasRequiredForEach$3) return forEach$3;
  hasRequiredForEach$3 = 1;
  requireEs_array_forEach();
  var getBuiltInPrototypeMethod2 = requireGetBuiltInPrototypeMethod();
  forEach$3 = getBuiltInPrototypeMethod2("Array", "forEach");
  return forEach$3;
}
var forEach$2;
var hasRequiredForEach$2;
function requireForEach$2() {
  if (hasRequiredForEach$2) return forEach$2;
  hasRequiredForEach$2 = 1;
  var parent = requireForEach$3();
  forEach$2 = parent;
  return forEach$2;
}
var forEach$1;
var hasRequiredForEach$1;
function requireForEach$1() {
  if (hasRequiredForEach$1) return forEach$1;
  hasRequiredForEach$1 = 1;
  var classof2 = requireClassof();
  var hasOwn = requireHasOwnProperty();
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var method = requireForEach$2();
  var ArrayPrototype = Array.prototype;
  var DOMIterables = {
    DOMTokenList: true,
    NodeList: true
  };
  forEach$1 = function(it) {
    var own = it.forEach;
    return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.forEach || hasOwn(DOMIterables, classof2(it)) ? method : own;
  };
  return forEach$1;
}
var forEach;
var hasRequiredForEach;
function requireForEach() {
  if (hasRequiredForEach) return forEach;
  hasRequiredForEach = 1;
  forEach = requireForEach$1();
  return forEach;
}
var forEachExports = requireForEach();
var _forEachInstanceProperty = getDefaultExportFromCjs(forEachExports);
var es_object_getOwnPropertyDescriptors = {};
var ownKeys$4;
var hasRequiredOwnKeys$3;
function requireOwnKeys$3() {
  if (hasRequiredOwnKeys$3) return ownKeys$4;
  hasRequiredOwnKeys$3 = 1;
  var getBuiltIn2 = requireGetBuiltIn();
  var uncurryThis = requireFunctionUncurryThis();
  var getOwnPropertyNamesModule = requireObjectGetOwnPropertyNames();
  var getOwnPropertySymbolsModule = requireObjectGetOwnPropertySymbols();
  var anObject2 = requireAnObject();
  var concat2 = uncurryThis([].concat);
  ownKeys$4 = getBuiltIn2("Reflect", "ownKeys") || function ownKeys2(it) {
    var keys2 = getOwnPropertyNamesModule.f(anObject2(it));
    var getOwnPropertySymbols2 = getOwnPropertySymbolsModule.f;
    return getOwnPropertySymbols2 ? concat2(keys2, getOwnPropertySymbols2(it)) : keys2;
  };
  return ownKeys$4;
}
var hasRequiredEs_object_getOwnPropertyDescriptors;
function requireEs_object_getOwnPropertyDescriptors() {
  if (hasRequiredEs_object_getOwnPropertyDescriptors) return es_object_getOwnPropertyDescriptors;
  hasRequiredEs_object_getOwnPropertyDescriptors = 1;
  var $ = require_export();
  var DESCRIPTORS = requireDescriptors();
  var ownKeys2 = requireOwnKeys$3();
  var toIndexedObject2 = requireToIndexedObject();
  var getOwnPropertyDescriptorModule = requireObjectGetOwnPropertyDescriptor();
  var createProperty2 = requireCreateProperty();
  $({ target: "Object", stat: true, sham: !DESCRIPTORS }, {
    getOwnPropertyDescriptors: function getOwnPropertyDescriptors2(object) {
      var O = toIndexedObject2(object);
      var getOwnPropertyDescriptor2 = getOwnPropertyDescriptorModule.f;
      var keys2 = ownKeys2(O);
      var result = {};
      var index = 0;
      var key, descriptor;
      while (keys2.length > index) {
        descriptor = getOwnPropertyDescriptor2(O, key = keys2[index++]);
        if (descriptor !== void 0) createProperty2(result, key, descriptor);
      }
      return result;
    }
  });
  return es_object_getOwnPropertyDescriptors;
}
var getOwnPropertyDescriptors$2;
var hasRequiredGetOwnPropertyDescriptors$2;
function requireGetOwnPropertyDescriptors$2() {
  if (hasRequiredGetOwnPropertyDescriptors$2) return getOwnPropertyDescriptors$2;
  hasRequiredGetOwnPropertyDescriptors$2 = 1;
  requireEs_object_getOwnPropertyDescriptors();
  var path2 = requirePath();
  getOwnPropertyDescriptors$2 = path2.Object.getOwnPropertyDescriptors;
  return getOwnPropertyDescriptors$2;
}
var getOwnPropertyDescriptors$1;
var hasRequiredGetOwnPropertyDescriptors$1;
function requireGetOwnPropertyDescriptors$1() {
  if (hasRequiredGetOwnPropertyDescriptors$1) return getOwnPropertyDescriptors$1;
  hasRequiredGetOwnPropertyDescriptors$1 = 1;
  var parent = requireGetOwnPropertyDescriptors$2();
  getOwnPropertyDescriptors$1 = parent;
  return getOwnPropertyDescriptors$1;
}
var getOwnPropertyDescriptors;
var hasRequiredGetOwnPropertyDescriptors;
function requireGetOwnPropertyDescriptors() {
  if (hasRequiredGetOwnPropertyDescriptors) return getOwnPropertyDescriptors;
  hasRequiredGetOwnPropertyDescriptors = 1;
  getOwnPropertyDescriptors = requireGetOwnPropertyDescriptors$1();
  return getOwnPropertyDescriptors;
}
var getOwnPropertyDescriptorsExports = requireGetOwnPropertyDescriptors();
var _Object$getOwnPropertyDescriptors = getDefaultExportFromCjs(getOwnPropertyDescriptorsExports);
var defineProperties$2 = { exports: {} };
var es_object_defineProperties = {};
var hasRequiredEs_object_defineProperties;
function requireEs_object_defineProperties() {
  if (hasRequiredEs_object_defineProperties) return es_object_defineProperties;
  hasRequiredEs_object_defineProperties = 1;
  var $ = require_export();
  var DESCRIPTORS = requireDescriptors();
  var defineProperties2 = requireObjectDefineProperties().f;
  $({ target: "Object", stat: true, forced: Object.defineProperties !== defineProperties2, sham: !DESCRIPTORS }, {
    defineProperties: defineProperties2
  });
  return es_object_defineProperties;
}
var hasRequiredDefineProperties$2;
function requireDefineProperties$2() {
  if (hasRequiredDefineProperties$2) return defineProperties$2.exports;
  hasRequiredDefineProperties$2 = 1;
  requireEs_object_defineProperties();
  var path2 = requirePath();
  var Object2 = path2.Object;
  var defineProperties2 = defineProperties$2.exports = function defineProperties3(T, D) {
    return Object2.defineProperties(T, D);
  };
  if (Object2.defineProperties.sham) defineProperties2.sham = true;
  return defineProperties$2.exports;
}
var defineProperties$1;
var hasRequiredDefineProperties$1;
function requireDefineProperties$1() {
  if (hasRequiredDefineProperties$1) return defineProperties$1;
  hasRequiredDefineProperties$1 = 1;
  var parent = requireDefineProperties$2();
  defineProperties$1 = parent;
  return defineProperties$1;
}
var defineProperties;
var hasRequiredDefineProperties;
function requireDefineProperties() {
  if (hasRequiredDefineProperties) return defineProperties;
  hasRequiredDefineProperties = 1;
  defineProperties = requireDefineProperties$1();
  return defineProperties;
}
var definePropertiesExports = requireDefineProperties();
var _Object$defineProperties = getDefaultExportFromCjs(definePropertiesExports);
var defineProperty;
var hasRequiredDefineProperty;
function requireDefineProperty() {
  if (hasRequiredDefineProperty) return defineProperty;
  hasRequiredDefineProperty = 1;
  defineProperty = requireDefineProperty$4();
  return defineProperty;
}
var definePropertyExports = requireDefineProperty();
var _Object$defineProperty = getDefaultExportFromCjs(definePropertyExports);
var es_array_isArray = {};
var hasRequiredEs_array_isArray;
function requireEs_array_isArray() {
  if (hasRequiredEs_array_isArray) return es_array_isArray;
  hasRequiredEs_array_isArray = 1;
  var $ = require_export();
  var isArray2 = requireIsArray$3();
  $({ target: "Array", stat: true }, {
    isArray: isArray2
  });
  return es_array_isArray;
}
var isArray$2;
var hasRequiredIsArray$2;
function requireIsArray$2() {
  if (hasRequiredIsArray$2) return isArray$2;
  hasRequiredIsArray$2 = 1;
  requireEs_array_isArray();
  var path2 = requirePath();
  isArray$2 = path2.Array.isArray;
  return isArray$2;
}
var isArray$1;
var hasRequiredIsArray$1;
function requireIsArray$1() {
  if (hasRequiredIsArray$1) return isArray$1;
  hasRequiredIsArray$1 = 1;
  var parent = requireIsArray$2();
  isArray$1 = parent;
  return isArray$1;
}
var isArray;
var hasRequiredIsArray;
function requireIsArray() {
  if (hasRequiredIsArray) return isArray;
  hasRequiredIsArray = 1;
  isArray = requireIsArray$1();
  return isArray;
}
var isArrayExports = requireIsArray();
var _Array$isArray = getDefaultExportFromCjs(isArrayExports);
var es_map = {};
var es_map_constructor = {};
var internalMetadata = { exports: {} };
var arrayBufferNonExtensible;
var hasRequiredArrayBufferNonExtensible;
function requireArrayBufferNonExtensible() {
  if (hasRequiredArrayBufferNonExtensible) return arrayBufferNonExtensible;
  hasRequiredArrayBufferNonExtensible = 1;
  var fails2 = requireFails();
  arrayBufferNonExtensible = fails2(function() {
    if (typeof ArrayBuffer == "function") {
      var buffer = new ArrayBuffer(8);
      if (Object.isExtensible(buffer)) Object.defineProperty(buffer, "a", { value: 8 });
    }
  });
  return arrayBufferNonExtensible;
}
var objectIsExtensible;
var hasRequiredObjectIsExtensible;
function requireObjectIsExtensible() {
  if (hasRequiredObjectIsExtensible) return objectIsExtensible;
  hasRequiredObjectIsExtensible = 1;
  var fails2 = requireFails();
  var isObject2 = requireIsObject();
  var classof2 = requireClassofRaw();
  var ARRAY_BUFFER_NON_EXTENSIBLE = requireArrayBufferNonExtensible();
  var $isExtensible = Object.isExtensible;
  var FAILS_ON_PRIMITIVES = fails2(function() {
  });
  objectIsExtensible = FAILS_ON_PRIMITIVES || ARRAY_BUFFER_NON_EXTENSIBLE ? function isExtensible(it) {
    if (!isObject2(it)) return false;
    if (ARRAY_BUFFER_NON_EXTENSIBLE && classof2(it) === "ArrayBuffer") return false;
    return $isExtensible ? $isExtensible(it) : true;
  } : $isExtensible;
  return objectIsExtensible;
}
var freezing;
var hasRequiredFreezing;
function requireFreezing() {
  if (hasRequiredFreezing) return freezing;
  hasRequiredFreezing = 1;
  var fails2 = requireFails();
  freezing = !fails2(function() {
    return Object.isExtensible(Object.preventExtensions({}));
  });
  return freezing;
}
var hasRequiredInternalMetadata;
function requireInternalMetadata() {
  if (hasRequiredInternalMetadata) return internalMetadata.exports;
  hasRequiredInternalMetadata = 1;
  var $ = require_export();
  var uncurryThis = requireFunctionUncurryThis();
  var hiddenKeys2 = requireHiddenKeys();
  var isObject2 = requireIsObject();
  var hasOwn = requireHasOwnProperty();
  var defineProperty2 = requireObjectDefineProperty().f;
  var getOwnPropertyNamesModule = requireObjectGetOwnPropertyNames();
  var getOwnPropertyNamesExternalModule = requireObjectGetOwnPropertyNamesExternal();
  var isExtensible = requireObjectIsExtensible();
  var uid2 = requireUid();
  var FREEZING = requireFreezing();
  var REQUIRED = false;
  var METADATA = uid2("meta");
  var id = 0;
  var setMetadata = function(it) {
    defineProperty2(it, METADATA, { value: {
      objectID: "O" + id++,
      // object ID
      weakData: {}
      // weak collections IDs
    } });
  };
  var fastKey = function(it, create2) {
    if (!isObject2(it)) return typeof it == "symbol" ? it : (typeof it == "string" ? "S" : "P") + it;
    if (!hasOwn(it, METADATA)) {
      if (!isExtensible(it)) return "F";
      if (!create2) return "E";
      setMetadata(it);
    }
    return it[METADATA].objectID;
  };
  var getWeakData = function(it, create2) {
    if (!hasOwn(it, METADATA)) {
      if (!isExtensible(it)) return true;
      if (!create2) return false;
      setMetadata(it);
    }
    return it[METADATA].weakData;
  };
  var onFreeze = function(it) {
    if (FREEZING && REQUIRED && isExtensible(it) && !hasOwn(it, METADATA)) setMetadata(it);
    return it;
  };
  var enable = function() {
    meta.enable = function() {
    };
    REQUIRED = true;
    var getOwnPropertyNames = getOwnPropertyNamesModule.f;
    var splice2 = uncurryThis([].splice);
    var test = {};
    test[METADATA] = 1;
    if (getOwnPropertyNames(test).length) {
      getOwnPropertyNamesModule.f = function(it) {
        var result = getOwnPropertyNames(it);
        for (var i = 0, length = result.length; i < length; i++) {
          if (result[i] === METADATA) {
            splice2(result, i, 1);
            break;
          }
        }
        return result;
      };
      $({ target: "Object", stat: true, forced: true }, {
        getOwnPropertyNames: getOwnPropertyNamesExternalModule.f
      });
    }
  };
  var meta = internalMetadata.exports = {
    enable,
    fastKey,
    getWeakData,
    onFreeze
  };
  hiddenKeys2[METADATA] = true;
  return internalMetadata.exports;
}
var isArrayIteratorMethod;
var hasRequiredIsArrayIteratorMethod;
function requireIsArrayIteratorMethod() {
  if (hasRequiredIsArrayIteratorMethod) return isArrayIteratorMethod;
  hasRequiredIsArrayIteratorMethod = 1;
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var Iterators = requireIterators();
  var ITERATOR = wellKnownSymbol2("iterator");
  var ArrayPrototype = Array.prototype;
  isArrayIteratorMethod = function(it) {
    return it !== void 0 && (Iterators.Array === it || ArrayPrototype[ITERATOR] === it);
  };
  return isArrayIteratorMethod;
}
var getIteratorMethod;
var hasRequiredGetIteratorMethod;
function requireGetIteratorMethod() {
  if (hasRequiredGetIteratorMethod) return getIteratorMethod;
  hasRequiredGetIteratorMethod = 1;
  var classof2 = requireClassof();
  var getMethod2 = requireGetMethod();
  var isNullOrUndefined2 = requireIsNullOrUndefined();
  var Iterators = requireIterators();
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var ITERATOR = wellKnownSymbol2("iterator");
  getIteratorMethod = function(it) {
    if (!isNullOrUndefined2(it)) return getMethod2(it, ITERATOR) || getMethod2(it, "@@iterator") || Iterators[classof2(it)];
  };
  return getIteratorMethod;
}
var getIterator$5;
var hasRequiredGetIterator$6;
function requireGetIterator$6() {
  if (hasRequiredGetIterator$6) return getIterator$5;
  hasRequiredGetIterator$6 = 1;
  var call = requireFunctionCall();
  var aCallable2 = requireACallable();
  var anObject2 = requireAnObject();
  var tryToString2 = requireTryToString();
  var getIteratorMethod2 = requireGetIteratorMethod();
  var $TypeError = TypeError;
  getIterator$5 = function(argument, usingIterator) {
    var iteratorMethod = arguments.length < 2 ? getIteratorMethod2(argument) : usingIterator;
    if (aCallable2(iteratorMethod)) return anObject2(call(iteratorMethod, argument));
    throw new $TypeError(tryToString2(argument) + " is not iterable");
  };
  return getIterator$5;
}
var iteratorClose;
var hasRequiredIteratorClose;
function requireIteratorClose() {
  if (hasRequiredIteratorClose) return iteratorClose;
  hasRequiredIteratorClose = 1;
  var call = requireFunctionCall();
  var anObject2 = requireAnObject();
  var getMethod2 = requireGetMethod();
  iteratorClose = function(iterator2, kind, value) {
    var innerResult, innerError;
    anObject2(iterator2);
    try {
      innerResult = getMethod2(iterator2, "return");
      if (!innerResult) {
        if (kind === "throw") throw value;
        return value;
      }
      innerResult = call(innerResult, iterator2);
    } catch (error) {
      innerError = true;
      innerResult = error;
    }
    if (kind === "throw") throw value;
    if (innerError) throw innerResult;
    anObject2(innerResult);
    return value;
  };
  return iteratorClose;
}
var iterate;
var hasRequiredIterate;
function requireIterate() {
  if (hasRequiredIterate) return iterate;
  hasRequiredIterate = 1;
  var bind2 = requireFunctionBindContext();
  var call = requireFunctionCall();
  var anObject2 = requireAnObject();
  var tryToString2 = requireTryToString();
  var isArrayIteratorMethod2 = requireIsArrayIteratorMethod();
  var lengthOfArrayLike2 = requireLengthOfArrayLike();
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var getIterator2 = requireGetIterator$6();
  var getIteratorMethod2 = requireGetIteratorMethod();
  var iteratorClose2 = requireIteratorClose();
  var $TypeError = TypeError;
  var Result = function(stopped, result) {
    this.stopped = stopped;
    this.result = result;
  };
  var ResultPrototype = Result.prototype;
  iterate = function(iterable, unboundFunction, options) {
    var that = options && options.that;
    var AS_ENTRIES = !!(options && options.AS_ENTRIES);
    var IS_RECORD = !!(options && options.IS_RECORD);
    var IS_ITERATOR = !!(options && options.IS_ITERATOR);
    var INTERRUPTED = !!(options && options.INTERRUPTED);
    var fn = bind2(unboundFunction, that);
    var iterator2, iterFn, index, length, result, next, step;
    var stop = function(condition) {
      if (iterator2) iteratorClose2(iterator2, "normal");
      return new Result(true, condition);
    };
    var callFn = function(value) {
      if (AS_ENTRIES) {
        anObject2(value);
        return INTERRUPTED ? fn(value[0], value[1], stop) : fn(value[0], value[1]);
      }
      return INTERRUPTED ? fn(value, stop) : fn(value);
    };
    if (IS_RECORD) {
      iterator2 = iterable.iterator;
    } else if (IS_ITERATOR) {
      iterator2 = iterable;
    } else {
      iterFn = getIteratorMethod2(iterable);
      if (!iterFn) throw new $TypeError(tryToString2(iterable) + " is not iterable");
      if (isArrayIteratorMethod2(iterFn)) {
        for (index = 0, length = lengthOfArrayLike2(iterable); length > index; index++) {
          result = callFn(iterable[index]);
          if (result && isPrototypeOf(ResultPrototype, result)) return result;
        }
        return new Result(false);
      }
      iterator2 = getIterator2(iterable, iterFn);
    }
    next = IS_RECORD ? iterable.next : iterator2.next;
    while (!(step = call(next, iterator2)).done) {
      try {
        result = callFn(step.value);
      } catch (error) {
        iteratorClose2(iterator2, "throw", error);
      }
      if (typeof result == "object" && result && isPrototypeOf(ResultPrototype, result)) return result;
    }
    return new Result(false);
  };
  return iterate;
}
var anInstance;
var hasRequiredAnInstance;
function requireAnInstance() {
  if (hasRequiredAnInstance) return anInstance;
  hasRequiredAnInstance = 1;
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var $TypeError = TypeError;
  anInstance = function(it, Prototype) {
    if (isPrototypeOf(Prototype, it)) return it;
    throw new $TypeError("Incorrect invocation");
  };
  return anInstance;
}
var collection;
var hasRequiredCollection;
function requireCollection() {
  if (hasRequiredCollection) return collection;
  hasRequiredCollection = 1;
  var $ = require_export();
  var globalThis2 = requireGlobalThis();
  var InternalMetadataModule = requireInternalMetadata();
  var fails2 = requireFails();
  var createNonEnumerableProperty2 = requireCreateNonEnumerableProperty();
  var iterate2 = requireIterate();
  var anInstance2 = requireAnInstance();
  var isCallable2 = requireIsCallable();
  var isObject2 = requireIsObject();
  var isNullOrUndefined2 = requireIsNullOrUndefined();
  var setToStringTag2 = requireSetToStringTag();
  var defineProperty2 = requireObjectDefineProperty().f;
  var forEach2 = requireArrayIteration().forEach;
  var DESCRIPTORS = requireDescriptors();
  var InternalStateModule = requireInternalState();
  var setInternalState = InternalStateModule.set;
  var internalStateGetterFor = InternalStateModule.getterFor;
  collection = function(CONSTRUCTOR_NAME, wrapper, common) {
    var IS_MAP = CONSTRUCTOR_NAME.indexOf("Map") !== -1;
    var IS_WEAK = CONSTRUCTOR_NAME.indexOf("Weak") !== -1;
    var ADDER = IS_MAP ? "set" : "add";
    var NativeConstructor = globalThis2[CONSTRUCTOR_NAME];
    var NativePrototype = NativeConstructor && NativeConstructor.prototype;
    var exported = {};
    var Constructor;
    if (!DESCRIPTORS || !isCallable2(NativeConstructor) || !(IS_WEAK || NativePrototype.forEach && !fails2(function() {
      new NativeConstructor().entries().next();
    }))) {
      Constructor = common.getConstructor(wrapper, CONSTRUCTOR_NAME, IS_MAP, ADDER);
      InternalMetadataModule.enable();
    } else {
      Constructor = wrapper(function(target, iterable) {
        setInternalState(anInstance2(target, Prototype), {
          type: CONSTRUCTOR_NAME,
          collection: new NativeConstructor()
        });
        if (!isNullOrUndefined2(iterable)) iterate2(iterable, target[ADDER], { that: target, AS_ENTRIES: IS_MAP });
      });
      var Prototype = Constructor.prototype;
      var getInternalState = internalStateGetterFor(CONSTRUCTOR_NAME);
      forEach2(["add", "clear", "delete", "forEach", "get", "has", "set", "keys", "values", "entries"], function(KEY) {
        var IS_ADDER = KEY === "add" || KEY === "set";
        if (KEY in NativePrototype && !(IS_WEAK && KEY === "clear")) {
          createNonEnumerableProperty2(Prototype, KEY, function(a, b) {
            var collection2 = getInternalState(this).collection;
            if (!IS_ADDER && IS_WEAK && !isObject2(a)) return KEY === "get" ? void 0 : false;
            var result = collection2[KEY](a === 0 ? 0 : a, b);
            return IS_ADDER ? this : result;
          });
        }
      });
      IS_WEAK || defineProperty2(Prototype, "size", {
        configurable: true,
        get: function() {
          return getInternalState(this).collection.size;
        }
      });
    }
    setToStringTag2(Constructor, CONSTRUCTOR_NAME, false, true);
    exported[CONSTRUCTOR_NAME] = Constructor;
    $({ global: true, forced: true }, exported);
    if (!IS_WEAK) common.setStrong(Constructor, CONSTRUCTOR_NAME, IS_MAP);
    return Constructor;
  };
  return collection;
}
var defineBuiltIns;
var hasRequiredDefineBuiltIns;
function requireDefineBuiltIns() {
  if (hasRequiredDefineBuiltIns) return defineBuiltIns;
  hasRequiredDefineBuiltIns = 1;
  var defineBuiltIn2 = requireDefineBuiltIn();
  defineBuiltIns = function(target, src, options) {
    for (var key in src) {
      if (options && options.unsafe && target[key]) target[key] = src[key];
      else defineBuiltIn2(target, key, src[key], options);
    }
    return target;
  };
  return defineBuiltIns;
}
var setSpecies;
var hasRequiredSetSpecies;
function requireSetSpecies() {
  if (hasRequiredSetSpecies) return setSpecies;
  hasRequiredSetSpecies = 1;
  var getBuiltIn2 = requireGetBuiltIn();
  var defineBuiltInAccessor2 = requireDefineBuiltInAccessor();
  var wellKnownSymbol2 = requireWellKnownSymbol();
  var DESCRIPTORS = requireDescriptors();
  var SPECIES = wellKnownSymbol2("species");
  setSpecies = function(CONSTRUCTOR_NAME) {
    var Constructor = getBuiltIn2(CONSTRUCTOR_NAME);
    if (DESCRIPTORS && Constructor && !Constructor[SPECIES]) {
      defineBuiltInAccessor2(Constructor, SPECIES, {
        configurable: true,
        get: function() {
          return this;
        }
      });
    }
  };
  return setSpecies;
}
var collectionStrong;
var hasRequiredCollectionStrong;
function requireCollectionStrong() {
  if (hasRequiredCollectionStrong) return collectionStrong;
  hasRequiredCollectionStrong = 1;
  var create2 = requireObjectCreate();
  var defineBuiltInAccessor2 = requireDefineBuiltInAccessor();
  var defineBuiltIns2 = requireDefineBuiltIns();
  var bind2 = requireFunctionBindContext();
  var anInstance2 = requireAnInstance();
  var isNullOrUndefined2 = requireIsNullOrUndefined();
  var iterate2 = requireIterate();
  var defineIterator = requireIteratorDefine();
  var createIterResultObject2 = requireCreateIterResultObject();
  var setSpecies2 = requireSetSpecies();
  var DESCRIPTORS = requireDescriptors();
  var fastKey = requireInternalMetadata().fastKey;
  var InternalStateModule = requireInternalState();
  var setInternalState = InternalStateModule.set;
  var internalStateGetterFor = InternalStateModule.getterFor;
  collectionStrong = {
    getConstructor: function(wrapper, CONSTRUCTOR_NAME, IS_MAP, ADDER) {
      var Constructor = wrapper(function(that, iterable) {
        anInstance2(that, Prototype);
        setInternalState(that, {
          type: CONSTRUCTOR_NAME,
          index: create2(null),
          first: null,
          last: null,
          size: 0
        });
        if (!DESCRIPTORS) that.size = 0;
        if (!isNullOrUndefined2(iterable)) iterate2(iterable, that[ADDER], { that, AS_ENTRIES: IS_MAP });
      });
      var Prototype = Constructor.prototype;
      var getInternalState = internalStateGetterFor(CONSTRUCTOR_NAME);
      var define = function(that, key, value) {
        var state = getInternalState(that);
        var entry = getEntry(that, key);
        var previous, index;
        if (entry) {
          entry.value = value;
        } else {
          state.last = entry = {
            index: index = fastKey(key, true),
            key,
            value,
            previous: previous = state.last,
            next: null,
            removed: false
          };
          if (!state.first) state.first = entry;
          if (previous) previous.next = entry;
          if (DESCRIPTORS) state.size++;
          else that.size++;
          if (index !== "F") state.index[index] = entry;
        }
        return that;
      };
      var getEntry = function(that, key) {
        var state = getInternalState(that);
        var index = fastKey(key);
        var entry;
        if (index !== "F") return state.index[index];
        for (entry = state.first; entry; entry = entry.next) {
          if (entry.key === key) return entry;
        }
      };
      defineBuiltIns2(Prototype, {
        // `{ Map, Set }.prototype.clear()` methods
        // https://tc39.es/ecma262/#sec-map.prototype.clear
        // https://tc39.es/ecma262/#sec-set.prototype.clear
        clear: function clear() {
          var that = this;
          var state = getInternalState(that);
          var entry = state.first;
          while (entry) {
            entry.removed = true;
            if (entry.previous) entry.previous = entry.previous.next = null;
            entry = entry.next;
          }
          state.first = state.last = null;
          state.index = create2(null);
          if (DESCRIPTORS) state.size = 0;
          else that.size = 0;
        },
        // `{ Map, Set }.prototype.delete(key)` methods
        // https://tc39.es/ecma262/#sec-map.prototype.delete
        // https://tc39.es/ecma262/#sec-set.prototype.delete
        "delete": function(key) {
          var that = this;
          var state = getInternalState(that);
          var entry = getEntry(that, key);
          if (entry) {
            var next = entry.next;
            var prev = entry.previous;
            delete state.index[entry.index];
            entry.removed = true;
            if (prev) prev.next = next;
            if (next) next.previous = prev;
            if (state.first === entry) state.first = next;
            if (state.last === entry) state.last = prev;
            if (DESCRIPTORS) state.size--;
            else that.size--;
          }
          return !!entry;
        },
        // `{ Map, Set }.prototype.forEach(callbackfn, thisArg = undefined)` methods
        // https://tc39.es/ecma262/#sec-map.prototype.foreach
        // https://tc39.es/ecma262/#sec-set.prototype.foreach
        forEach: function forEach2(callbackfn) {
          var state = getInternalState(this);
          var boundFunction = bind2(callbackfn, arguments.length > 1 ? arguments[1] : void 0);
          var entry;
          while (entry = entry ? entry.next : state.first) {
            boundFunction(entry.value, entry.key, this);
            while (entry && entry.removed) entry = entry.previous;
          }
        },
        // `{ Map, Set}.prototype.has(key)` methods
        // https://tc39.es/ecma262/#sec-map.prototype.has
        // https://tc39.es/ecma262/#sec-set.prototype.has
        has: function has(key) {
          return !!getEntry(this, key);
        }
      });
      defineBuiltIns2(Prototype, IS_MAP ? {
        // `Map.prototype.get(key)` method
        // https://tc39.es/ecma262/#sec-map.prototype.get
        get: function get(key) {
          var entry = getEntry(this, key);
          return entry && entry.value;
        },
        // `Map.prototype.set(key, value)` method
        // https://tc39.es/ecma262/#sec-map.prototype.set
        set: function set2(key, value) {
          return define(this, key === 0 ? 0 : key, value);
        }
      } : {
        // `Set.prototype.add(value)` method
        // https://tc39.es/ecma262/#sec-set.prototype.add
        add: function add(value) {
          return define(this, value = value === 0 ? 0 : value, value);
        }
      });
      if (DESCRIPTORS) defineBuiltInAccessor2(Prototype, "size", {
        configurable: true,
        get: function() {
          return getInternalState(this).size;
        }
      });
      return Constructor;
    },
    setStrong: function(Constructor, CONSTRUCTOR_NAME, IS_MAP) {
      var ITERATOR_NAME = CONSTRUCTOR_NAME + " Iterator";
      var getInternalCollectionState = internalStateGetterFor(CONSTRUCTOR_NAME);
      var getInternalIteratorState = internalStateGetterFor(ITERATOR_NAME);
      defineIterator(Constructor, CONSTRUCTOR_NAME, function(iterated, kind) {
        setInternalState(this, {
          type: ITERATOR_NAME,
          target: iterated,
          state: getInternalCollectionState(iterated),
          kind,
          last: null
        });
      }, function() {
        var state = getInternalIteratorState(this);
        var kind = state.kind;
        var entry = state.last;
        while (entry && entry.removed) entry = entry.previous;
        if (!state.target || !(state.last = entry = entry ? entry.next : state.state.first)) {
          state.target = null;
          return createIterResultObject2(void 0, true);
        }
        if (kind === "keys") return createIterResultObject2(entry.key, false);
        if (kind === "values") return createIterResultObject2(entry.value, false);
        return createIterResultObject2([entry.key, entry.value], false);
      }, IS_MAP ? "entries" : "values", !IS_MAP, true);
      setSpecies2(CONSTRUCTOR_NAME);
    }
  };
  return collectionStrong;
}
var hasRequiredEs_map_constructor;
function requireEs_map_constructor() {
  if (hasRequiredEs_map_constructor) return es_map_constructor;
  hasRequiredEs_map_constructor = 1;
  var collection2 = requireCollection();
  var collectionStrong2 = requireCollectionStrong();
  collection2("Map", function(init) {
    return function Map2() {
      return init(this, arguments.length ? arguments[0] : void 0);
    };
  }, collectionStrong2);
  return es_map_constructor;
}
var hasRequiredEs_map;
function requireEs_map() {
  if (hasRequiredEs_map) return es_map;
  hasRequiredEs_map = 1;
  requireEs_map_constructor();
  return es_map;
}
var es_map_groupBy = {};
var caller;
var hasRequiredCaller;
function requireCaller() {
  if (hasRequiredCaller) return caller;
  hasRequiredCaller = 1;
  caller = function(methodName, numArgs) {
    return numArgs === 1 ? function(object, arg) {
      return object[methodName](arg);
    } : function(object, arg1, arg2) {
      return object[methodName](arg1, arg2);
    };
  };
  return caller;
}
var mapHelpers;
var hasRequiredMapHelpers;
function requireMapHelpers() {
  if (hasRequiredMapHelpers) return mapHelpers;
  hasRequiredMapHelpers = 1;
  var getBuiltIn2 = requireGetBuiltIn();
  var caller2 = requireCaller();
  var Map2 = getBuiltIn2("Map");
  mapHelpers = {
    Map: Map2,
    set: caller2("set", 2),
    get: caller2("get", 1),
    has: caller2("has", 1),
    remove: caller2("delete", 1),
    proto: Map2.prototype
  };
  return mapHelpers;
}
var hasRequiredEs_map_groupBy;
function requireEs_map_groupBy() {
  if (hasRequiredEs_map_groupBy) return es_map_groupBy;
  hasRequiredEs_map_groupBy = 1;
  var $ = require_export();
  var uncurryThis = requireFunctionUncurryThis();
  var aCallable2 = requireACallable();
  var requireObjectCoercible2 = requireRequireObjectCoercible();
  var iterate2 = requireIterate();
  var MapHelpers = requireMapHelpers();
  var IS_PURE = requireIsPure();
  var fails2 = requireFails();
  var Map2 = MapHelpers.Map;
  var has = MapHelpers.has;
  var get = MapHelpers.get;
  var set2 = MapHelpers.set;
  var push = uncurryThis([].push);
  var DOES_NOT_WORK_WITH_PRIMITIVES = IS_PURE || fails2(function() {
    return Map2.groupBy("ab", function(it) {
      return it;
    }).get("a").length !== 1;
  });
  $({ target: "Map", stat: true, forced: IS_PURE || DOES_NOT_WORK_WITH_PRIMITIVES }, {
    groupBy: function groupBy(items, callbackfn) {
      requireObjectCoercible2(items);
      aCallable2(callbackfn);
      var map2 = new Map2();
      var k = 0;
      iterate2(items, function(value) {
        var key = callbackfn(value, k++);
        if (!has(map2, key)) set2(map2, key, [value]);
        else push(get(map2, key), value);
      });
      return map2;
    }
  });
  return es_map_groupBy;
}
var map$2;
var hasRequiredMap$2;
function requireMap$2() {
  if (hasRequiredMap$2) return map$2;
  hasRequiredMap$2 = 1;
  requireEs_array_iterator();
  requireEs_map();
  requireEs_map_groupBy();
  requireEs_string_iterator();
  var path2 = requirePath();
  map$2 = path2.Map;
  return map$2;
}
var map$1;
var hasRequiredMap$1;
function requireMap$1() {
  if (hasRequiredMap$1) return map$1;
  hasRequiredMap$1 = 1;
  var parent = requireMap$2();
  requireWeb_domCollections_iterator();
  map$1 = parent;
  return map$1;
}
var map;
var hasRequiredMap;
function requireMap() {
  if (hasRequiredMap) return map;
  hasRequiredMap = 1;
  map = requireMap$1();
  return map;
}
var mapExports = requireMap();
var _Map = getDefaultExportFromCjs(mapExports);
var es_array_some = {};
var hasRequiredEs_array_some;
function requireEs_array_some() {
  if (hasRequiredEs_array_some) return es_array_some;
  hasRequiredEs_array_some = 1;
  var $ = require_export();
  var $some = requireArrayIteration().some;
  var arrayMethodIsStrict2 = requireArrayMethodIsStrict();
  var STRICT_METHOD = arrayMethodIsStrict2("some");
  $({ target: "Array", proto: true, forced: !STRICT_METHOD }, {
    some: function some2(callbackfn) {
      return $some(this, callbackfn, arguments.length > 1 ? arguments[1] : void 0);
    }
  });
  return es_array_some;
}
var some$3;
var hasRequiredSome$3;
function requireSome$3() {
  if (hasRequiredSome$3) return some$3;
  hasRequiredSome$3 = 1;
  requireEs_array_some();
  var getBuiltInPrototypeMethod2 = requireGetBuiltInPrototypeMethod();
  some$3 = getBuiltInPrototypeMethod2("Array", "some");
  return some$3;
}
var some$2;
var hasRequiredSome$2;
function requireSome$2() {
  if (hasRequiredSome$2) return some$2;
  hasRequiredSome$2 = 1;
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var method = requireSome$3();
  var ArrayPrototype = Array.prototype;
  some$2 = function(it) {
    var own = it.some;
    return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.some ? method : own;
  };
  return some$2;
}
var some$1;
var hasRequiredSome$1;
function requireSome$1() {
  if (hasRequiredSome$1) return some$1;
  hasRequiredSome$1 = 1;
  var parent = requireSome$2();
  some$1 = parent;
  return some$1;
}
var some;
var hasRequiredSome;
function requireSome() {
  if (hasRequiredSome) return some;
  hasRequiredSome = 1;
  some = requireSome$1();
  return some;
}
var someExports = requireSome();
var _someInstanceProperty = getDefaultExportFromCjs(someExports);
var es_object_assign = {};
var objectAssign;
var hasRequiredObjectAssign;
function requireObjectAssign() {
  if (hasRequiredObjectAssign) return objectAssign;
  hasRequiredObjectAssign = 1;
  var DESCRIPTORS = requireDescriptors();
  var uncurryThis = requireFunctionUncurryThis();
  var call = requireFunctionCall();
  var fails2 = requireFails();
  var objectKeys2 = requireObjectKeys();
  var getOwnPropertySymbolsModule = requireObjectGetOwnPropertySymbols();
  var propertyIsEnumerableModule = requireObjectPropertyIsEnumerable();
  var toObject2 = requireToObject();
  var IndexedObject = requireIndexedObject();
  var $assign = Object.assign;
  var defineProperty2 = Object.defineProperty;
  var concat2 = uncurryThis([].concat);
  objectAssign = !$assign || fails2(function() {
    if (DESCRIPTORS && $assign({ b: 1 }, $assign(defineProperty2({}, "a", {
      enumerable: true,
      get: function() {
        defineProperty2(this, "b", {
          value: 3,
          enumerable: false
        });
      }
    }), { b: 2 })).b !== 1) return true;
    var A = {};
    var B = {};
    var symbol2 = Symbol("assign detection");
    var alphabet = "abcdefghijklmnopqrst";
    A[symbol2] = 7;
    alphabet.split("").forEach(function(chr) {
      B[chr] = chr;
    });
    return $assign({}, A)[symbol2] !== 7 || objectKeys2($assign({}, B)).join("") !== alphabet;
  }) ? function assign2(target, source) {
    var T = toObject2(target);
    var argumentsLength = arguments.length;
    var index = 1;
    var getOwnPropertySymbols2 = getOwnPropertySymbolsModule.f;
    var propertyIsEnumerable = propertyIsEnumerableModule.f;
    while (argumentsLength > index) {
      var S = IndexedObject(arguments[index++]);
      var keys2 = getOwnPropertySymbols2 ? concat2(objectKeys2(S), getOwnPropertySymbols2(S)) : objectKeys2(S);
      var length = keys2.length;
      var j = 0;
      var key;
      while (length > j) {
        key = keys2[j++];
        if (!DESCRIPTORS || call(propertyIsEnumerable, S, key)) T[key] = S[key];
      }
    }
    return T;
  } : $assign;
  return objectAssign;
}
var hasRequiredEs_object_assign;
function requireEs_object_assign() {
  if (hasRequiredEs_object_assign) return es_object_assign;
  hasRequiredEs_object_assign = 1;
  var $ = require_export();
  var assign2 = requireObjectAssign();
  $({ target: "Object", stat: true, arity: 2, forced: Object.assign !== assign2 }, {
    assign: assign2
  });
  return es_object_assign;
}
var assign$2;
var hasRequiredAssign$2;
function requireAssign$2() {
  if (hasRequiredAssign$2) return assign$2;
  hasRequiredAssign$2 = 1;
  requireEs_object_assign();
  var path2 = requirePath();
  assign$2 = path2.Object.assign;
  return assign$2;
}
var assign$1;
var hasRequiredAssign$1;
function requireAssign$1() {
  if (hasRequiredAssign$1) return assign$1;
  hasRequiredAssign$1 = 1;
  var parent = requireAssign$2();
  assign$1 = parent;
  return assign$1;
}
var assign;
var hasRequiredAssign;
function requireAssign() {
  if (hasRequiredAssign) return assign;
  hasRequiredAssign = 1;
  assign = requireAssign$1();
  return assign;
}
var assignExports = requireAssign();
var _Object$assign = getDefaultExportFromCjs(assignExports);
var concat$3;
var hasRequiredConcat$3;
function requireConcat$3() {
  if (hasRequiredConcat$3) return concat$3;
  hasRequiredConcat$3 = 1;
  requireEs_array_concat();
  var getBuiltInPrototypeMethod2 = requireGetBuiltInPrototypeMethod();
  concat$3 = getBuiltInPrototypeMethod2("Array", "concat");
  return concat$3;
}
var concat$2;
var hasRequiredConcat$2;
function requireConcat$2() {
  if (hasRequiredConcat$2) return concat$2;
  hasRequiredConcat$2 = 1;
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var method = requireConcat$3();
  var ArrayPrototype = Array.prototype;
  concat$2 = function(it) {
    var own = it.concat;
    return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.concat ? method : own;
  };
  return concat$2;
}
var concat$1;
var hasRequiredConcat$1;
function requireConcat$1() {
  if (hasRequiredConcat$1) return concat$1;
  hasRequiredConcat$1 = 1;
  var parent = requireConcat$2();
  concat$1 = parent;
  return concat$1;
}
var concat;
var hasRequiredConcat;
function requireConcat() {
  if (hasRequiredConcat) return concat;
  hasRequiredConcat = 1;
  concat = requireConcat$1();
  return concat;
}
var concatExports = requireConcat();
var _concatInstanceProperty = getDefaultExportFromCjs(concatExports);
var keys$6;
var hasRequiredKeys$6;
function requireKeys$6() {
  if (hasRequiredKeys$6) return keys$6;
  hasRequiredKeys$6 = 1;
  requireEs_array_iterator();
  var getBuiltInPrototypeMethod2 = requireGetBuiltInPrototypeMethod();
  keys$6 = getBuiltInPrototypeMethod2("Array", "keys");
  return keys$6;
}
var keys$5;
var hasRequiredKeys$5;
function requireKeys$5() {
  if (hasRequiredKeys$5) return keys$5;
  hasRequiredKeys$5 = 1;
  var parent = requireKeys$6();
  keys$5 = parent;
  return keys$5;
}
var keys$4;
var hasRequiredKeys$4;
function requireKeys$4() {
  if (hasRequiredKeys$4) return keys$4;
  hasRequiredKeys$4 = 1;
  requireWeb_domCollections_iterator();
  var classof2 = requireClassof();
  var hasOwn = requireHasOwnProperty();
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var method = requireKeys$5();
  var ArrayPrototype = Array.prototype;
  var DOMIterables = {
    DOMTokenList: true,
    NodeList: true
  };
  keys$4 = function(it) {
    var own = it.keys;
    return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.keys || hasOwn(DOMIterables, classof2(it)) ? method : own;
  };
  return keys$4;
}
var keys$3;
var hasRequiredKeys$3;
function requireKeys$3() {
  if (hasRequiredKeys$3) return keys$3;
  hasRequiredKeys$3 = 1;
  keys$3 = requireKeys$4();
  return keys$3;
}
var keysExports$1 = requireKeys$3();
var _keysInstanceProperty = getDefaultExportFromCjs(keysExports$1);
var es_object_keys = {};
var hasRequiredEs_object_keys;
function requireEs_object_keys() {
  if (hasRequiredEs_object_keys) return es_object_keys;
  hasRequiredEs_object_keys = 1;
  var $ = require_export();
  var toObject2 = requireToObject();
  var nativeKeys = requireObjectKeys();
  var fails2 = requireFails();
  var FAILS_ON_PRIMITIVES = fails2(function() {
    nativeKeys(1);
  });
  $({ target: "Object", stat: true, forced: FAILS_ON_PRIMITIVES }, {
    keys: function keys2(it) {
      return nativeKeys(toObject2(it));
    }
  });
  return es_object_keys;
}
var keys$2;
var hasRequiredKeys$2;
function requireKeys$2() {
  if (hasRequiredKeys$2) return keys$2;
  hasRequiredKeys$2 = 1;
  requireEs_object_keys();
  var path2 = requirePath();
  keys$2 = path2.Object.keys;
  return keys$2;
}
var keys$1;
var hasRequiredKeys$1;
function requireKeys$1() {
  if (hasRequiredKeys$1) return keys$1;
  hasRequiredKeys$1 = 1;
  var parent = requireKeys$2();
  keys$1 = parent;
  return keys$1;
}
var keys;
var hasRequiredKeys;
function requireKeys() {
  if (hasRequiredKeys) return keys;
  hasRequiredKeys = 1;
  keys = requireKeys$1();
  return keys;
}
var keysExports = requireKeys();
var _Object$keys = getDefaultExportFromCjs(keysExports);
var es_array_sort = {};
var deletePropertyOrThrow;
var hasRequiredDeletePropertyOrThrow;
function requireDeletePropertyOrThrow() {
  if (hasRequiredDeletePropertyOrThrow) return deletePropertyOrThrow;
  hasRequiredDeletePropertyOrThrow = 1;
  var tryToString2 = requireTryToString();
  var $TypeError = TypeError;
  deletePropertyOrThrow = function(O, P) {
    if (!delete O[P]) throw new $TypeError("Cannot delete property " + tryToString2(P) + " of " + tryToString2(O));
  };
  return deletePropertyOrThrow;
}
var arraySort;
var hasRequiredArraySort;
function requireArraySort() {
  if (hasRequiredArraySort) return arraySort;
  hasRequiredArraySort = 1;
  var arraySlice2 = requireArraySlice();
  var floor = Math.floor;
  var sort2 = function(array, comparefn) {
    var length = array.length;
    if (length < 8) {
      var i = 1;
      var element, j;
      while (i < length) {
        j = i;
        element = array[i];
        while (j && comparefn(array[j - 1], element) > 0) {
          array[j] = array[--j];
        }
        if (j !== i++) array[j] = element;
      }
    } else {
      var middle = floor(length / 2);
      var left = sort2(arraySlice2(array, 0, middle), comparefn);
      var right = sort2(arraySlice2(array, middle), comparefn);
      var llength = left.length;
      var rlength = right.length;
      var lindex = 0;
      var rindex = 0;
      while (lindex < llength || rindex < rlength) {
        array[lindex + rindex] = lindex < llength && rindex < rlength ? comparefn(left[lindex], right[rindex]) <= 0 ? left[lindex++] : right[rindex++] : lindex < llength ? left[lindex++] : right[rindex++];
      }
    }
    return array;
  };
  arraySort = sort2;
  return arraySort;
}
var environmentFfVersion;
var hasRequiredEnvironmentFfVersion;
function requireEnvironmentFfVersion() {
  if (hasRequiredEnvironmentFfVersion) return environmentFfVersion;
  hasRequiredEnvironmentFfVersion = 1;
  var userAgent = requireEnvironmentUserAgent();
  var firefox = userAgent.match(/firefox\/(\d+)/i);
  environmentFfVersion = !!firefox && +firefox[1];
  return environmentFfVersion;
}
var environmentIsIeOrEdge;
var hasRequiredEnvironmentIsIeOrEdge;
function requireEnvironmentIsIeOrEdge() {
  if (hasRequiredEnvironmentIsIeOrEdge) return environmentIsIeOrEdge;
  hasRequiredEnvironmentIsIeOrEdge = 1;
  var UA = requireEnvironmentUserAgent();
  environmentIsIeOrEdge = /MSIE|Trident/.test(UA);
  return environmentIsIeOrEdge;
}
var environmentWebkitVersion;
var hasRequiredEnvironmentWebkitVersion;
function requireEnvironmentWebkitVersion() {
  if (hasRequiredEnvironmentWebkitVersion) return environmentWebkitVersion;
  hasRequiredEnvironmentWebkitVersion = 1;
  var userAgent = requireEnvironmentUserAgent();
  var webkit = userAgent.match(/AppleWebKit\/(\d+)\./);
  environmentWebkitVersion = !!webkit && +webkit[1];
  return environmentWebkitVersion;
}
var hasRequiredEs_array_sort;
function requireEs_array_sort() {
  if (hasRequiredEs_array_sort) return es_array_sort;
  hasRequiredEs_array_sort = 1;
  var $ = require_export();
  var uncurryThis = requireFunctionUncurryThis();
  var aCallable2 = requireACallable();
  var toObject2 = requireToObject();
  var lengthOfArrayLike2 = requireLengthOfArrayLike();
  var deletePropertyOrThrow2 = requireDeletePropertyOrThrow();
  var toString2 = requireToString();
  var fails2 = requireFails();
  var internalSort = requireArraySort();
  var arrayMethodIsStrict2 = requireArrayMethodIsStrict();
  var FF = requireEnvironmentFfVersion();
  var IE_OR_EDGE = requireEnvironmentIsIeOrEdge();
  var V8 = requireEnvironmentV8Version();
  var WEBKIT = requireEnvironmentWebkitVersion();
  var test = [];
  var nativeSort = uncurryThis(test.sort);
  var push = uncurryThis(test.push);
  var FAILS_ON_UNDEFINED = fails2(function() {
    test.sort(void 0);
  });
  var FAILS_ON_NULL = fails2(function() {
    test.sort(null);
  });
  var STRICT_METHOD = arrayMethodIsStrict2("sort");
  var STABLE_SORT = !fails2(function() {
    if (V8) return V8 < 70;
    if (FF && FF > 3) return;
    if (IE_OR_EDGE) return true;
    if (WEBKIT) return WEBKIT < 603;
    var result = "";
    var code, chr, value, index;
    for (code = 65; code < 76; code++) {
      chr = String.fromCharCode(code);
      switch (code) {
        case 66:
        case 69:
        case 70:
        case 72:
          value = 3;
          break;
        case 68:
        case 71:
          value = 4;
          break;
        default:
          value = 2;
      }
      for (index = 0; index < 47; index++) {
        test.push({ k: chr + index, v: value });
      }
    }
    test.sort(function(a, b) {
      return b.v - a.v;
    });
    for (index = 0; index < test.length; index++) {
      chr = test[index].k.charAt(0);
      if (result.charAt(result.length - 1) !== chr) result += chr;
    }
    return result !== "DGBEFHACIJK";
  });
  var FORCED = FAILS_ON_UNDEFINED || !FAILS_ON_NULL || !STRICT_METHOD || !STABLE_SORT;
  var getSortCompare = function(comparefn) {
    return function(x, y) {
      if (y === void 0) return -1;
      if (x === void 0) return 1;
      if (comparefn !== void 0) return +comparefn(x, y) || 0;
      return toString2(x) > toString2(y) ? 1 : -1;
    };
  };
  $({ target: "Array", proto: true, forced: FORCED }, {
    sort: function sort2(comparefn) {
      if (comparefn !== void 0) aCallable2(comparefn);
      var array = toObject2(this);
      if (STABLE_SORT) return comparefn === void 0 ? nativeSort(array) : nativeSort(array, comparefn);
      var items = [];
      var arrayLength = lengthOfArrayLike2(array);
      var itemsLength, index;
      for (index = 0; index < arrayLength; index++) {
        if (index in array) push(items, array[index]);
      }
      internalSort(items, getSortCompare(comparefn));
      itemsLength = lengthOfArrayLike2(items);
      index = 0;
      while (index < itemsLength) array[index] = items[index++];
      while (index < arrayLength) deletePropertyOrThrow2(array, index++);
      return array;
    }
  });
  return es_array_sort;
}
var sort$3;
var hasRequiredSort$3;
function requireSort$3() {
  if (hasRequiredSort$3) return sort$3;
  hasRequiredSort$3 = 1;
  requireEs_array_sort();
  var getBuiltInPrototypeMethod2 = requireGetBuiltInPrototypeMethod();
  sort$3 = getBuiltInPrototypeMethod2("Array", "sort");
  return sort$3;
}
var sort$2;
var hasRequiredSort$2;
function requireSort$2() {
  if (hasRequiredSort$2) return sort$2;
  hasRequiredSort$2 = 1;
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var method = requireSort$3();
  var ArrayPrototype = Array.prototype;
  sort$2 = function(it) {
    var own = it.sort;
    return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.sort ? method : own;
  };
  return sort$2;
}
var sort$1;
var hasRequiredSort$1;
function requireSort$1() {
  if (hasRequiredSort$1) return sort$1;
  hasRequiredSort$1 = 1;
  var parent = requireSort$2();
  sort$1 = parent;
  return sort$1;
}
var sort;
var hasRequiredSort;
function requireSort() {
  if (hasRequiredSort) return sort;
  hasRequiredSort = 1;
  sort = requireSort$1();
  return sort;
}
var sortExports = requireSort();
var _sortInstanceProperty = getDefaultExportFromCjs(sortExports);
var values$3;
var hasRequiredValues$3;
function requireValues$3() {
  if (hasRequiredValues$3) return values$3;
  hasRequiredValues$3 = 1;
  requireEs_array_iterator();
  var getBuiltInPrototypeMethod2 = requireGetBuiltInPrototypeMethod();
  values$3 = getBuiltInPrototypeMethod2("Array", "values");
  return values$3;
}
var values$2;
var hasRequiredValues$2;
function requireValues$2() {
  if (hasRequiredValues$2) return values$2;
  hasRequiredValues$2 = 1;
  var parent = requireValues$3();
  values$2 = parent;
  return values$2;
}
var values$1;
var hasRequiredValues$1;
function requireValues$1() {
  if (hasRequiredValues$1) return values$1;
  hasRequiredValues$1 = 1;
  requireWeb_domCollections_iterator();
  var classof2 = requireClassof();
  var hasOwn = requireHasOwnProperty();
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var method = requireValues$2();
  var ArrayPrototype = Array.prototype;
  var DOMIterables = {
    DOMTokenList: true,
    NodeList: true
  };
  values$1 = function(it) {
    var own = it.values;
    return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.values || hasOwn(DOMIterables, classof2(it)) ? method : own;
  };
  return values$1;
}
var values;
var hasRequiredValues;
function requireValues() {
  if (hasRequiredValues) return values;
  hasRequiredValues = 1;
  values = requireValues$1();
  return values;
}
var valuesExports = requireValues();
var _valuesInstanceProperty = getDefaultExportFromCjs(valuesExports);
var es_date_toJson = {};
var stringRepeat;
var hasRequiredStringRepeat;
function requireStringRepeat() {
  if (hasRequiredStringRepeat) return stringRepeat;
  hasRequiredStringRepeat = 1;
  var toIntegerOrInfinity2 = requireToIntegerOrInfinity();
  var toString2 = requireToString();
  var requireObjectCoercible2 = requireRequireObjectCoercible();
  var $RangeError = RangeError;
  stringRepeat = function repeat(count) {
    var str = toString2(requireObjectCoercible2(this));
    var result = "";
    var n = toIntegerOrInfinity2(count);
    if (n < 0 || n === Infinity) throw new $RangeError("Wrong number of repetitions");
    for (; n > 0; (n >>>= 1) && (str += str)) if (n & 1) result += str;
    return result;
  };
  return stringRepeat;
}
var stringPad;
var hasRequiredStringPad;
function requireStringPad() {
  if (hasRequiredStringPad) return stringPad;
  hasRequiredStringPad = 1;
  var uncurryThis = requireFunctionUncurryThis();
  var toLength2 = requireToLength();
  var toString2 = requireToString();
  var $repeat = requireStringRepeat();
  var requireObjectCoercible2 = requireRequireObjectCoercible();
  var repeat = uncurryThis($repeat);
  var stringSlice = uncurryThis("".slice);
  var ceil = Math.ceil;
  var createMethod = function(IS_END) {
    return function($this, maxLength, fillString) {
      var S = toString2(requireObjectCoercible2($this));
      var intMaxLength = toLength2(maxLength);
      var stringLength = S.length;
      var fillStr = fillString === void 0 ? " " : toString2(fillString);
      var fillLen, stringFiller;
      if (intMaxLength <= stringLength || fillStr === "") return S;
      fillLen = intMaxLength - stringLength;
      stringFiller = repeat(fillStr, ceil(fillLen / fillStr.length));
      if (stringFiller.length > fillLen) stringFiller = stringSlice(stringFiller, 0, fillLen);
      return IS_END ? S + stringFiller : stringFiller + S;
    };
  };
  stringPad = {
    // `String.prototype.padStart` method
    // https://tc39.es/ecma262/#sec-string.prototype.padstart
    start: createMethod(false),
    // `String.prototype.padEnd` method
    // https://tc39.es/ecma262/#sec-string.prototype.padend
    end: createMethod(true)
  };
  return stringPad;
}
var dateToIsoString;
var hasRequiredDateToIsoString;
function requireDateToIsoString() {
  if (hasRequiredDateToIsoString) return dateToIsoString;
  hasRequiredDateToIsoString = 1;
  var uncurryThis = requireFunctionUncurryThis();
  var fails2 = requireFails();
  var padStart = requireStringPad().start;
  var $RangeError = RangeError;
  var $isFinite = isFinite;
  var abs2 = Math.abs;
  var DatePrototype = Date.prototype;
  var nativeDateToISOString = DatePrototype.toISOString;
  var thisTimeValue = uncurryThis(DatePrototype.getTime);
  var getUTCDate = uncurryThis(DatePrototype.getUTCDate);
  var getUTCFullYear = uncurryThis(DatePrototype.getUTCFullYear);
  var getUTCHours = uncurryThis(DatePrototype.getUTCHours);
  var getUTCMilliseconds = uncurryThis(DatePrototype.getUTCMilliseconds);
  var getUTCMinutes = uncurryThis(DatePrototype.getUTCMinutes);
  var getUTCMonth = uncurryThis(DatePrototype.getUTCMonth);
  var getUTCSeconds = uncurryThis(DatePrototype.getUTCSeconds);
  dateToIsoString = fails2(function() {
    return nativeDateToISOString.call(new Date(-5e13 - 1)) !== "0385-07-25T07:06:39.999Z";
  }) || !fails2(function() {
    nativeDateToISOString.call(/* @__PURE__ */ new Date(NaN));
  }) ? function toISOString() {
    if (!$isFinite(thisTimeValue(this))) throw new $RangeError("Invalid time value");
    var date = this;
    var year = getUTCFullYear(date);
    var milliseconds = getUTCMilliseconds(date);
    var sign = year < 0 ? "-" : year > 9999 ? "+" : "";
    return sign + padStart(abs2(year), sign ? 6 : 4, 0) + "-" + padStart(getUTCMonth(date) + 1, 2, 0) + "-" + padStart(getUTCDate(date), 2, 0) + "T" + padStart(getUTCHours(date), 2, 0) + ":" + padStart(getUTCMinutes(date), 2, 0) + ":" + padStart(getUTCSeconds(date), 2, 0) + "." + padStart(milliseconds, 3, 0) + "Z";
  } : nativeDateToISOString;
  return dateToIsoString;
}
var hasRequiredEs_date_toJson;
function requireEs_date_toJson() {
  if (hasRequiredEs_date_toJson) return es_date_toJson;
  hasRequiredEs_date_toJson = 1;
  var $ = require_export();
  var call = requireFunctionCall();
  var toObject2 = requireToObject();
  var toPrimitive2 = requireToPrimitive$5();
  var toISOString = requireDateToIsoString();
  var classof2 = requireClassofRaw();
  var fails2 = requireFails();
  var FORCED = fails2(function() {
    return (/* @__PURE__ */ new Date(NaN)).toJSON() !== null || call(Date.prototype.toJSON, { toISOString: function() {
      return 1;
    } }) !== 1;
  });
  $({ target: "Date", proto: true, forced: FORCED }, {
    // eslint-disable-next-line no-unused-vars -- required for `.length`
    toJSON: function toJSON(key) {
      var O = toObject2(this);
      var pv = toPrimitive2(O, "number");
      return typeof pv == "number" && !isFinite(pv) ? null : !("toISOString" in O) && classof2(O) === "Date" ? call(toISOString, O) : O.toISOString();
    }
  });
  return es_date_toJson;
}
var stringify$2;
var hasRequiredStringify$2;
function requireStringify$2() {
  if (hasRequiredStringify$2) return stringify$2;
  hasRequiredStringify$2 = 1;
  requireEs_date_toJson();
  requireEs_json_stringify();
  var path2 = requirePath();
  var apply = requireFunctionApply();
  if (!path2.JSON) path2.JSON = { stringify: JSON.stringify };
  stringify$2 = function stringify2(it, replacer, space) {
    return apply(path2.JSON.stringify, null, arguments);
  };
  return stringify$2;
}
var stringify$1;
var hasRequiredStringify$1;
function requireStringify$1() {
  if (hasRequiredStringify$1) return stringify$1;
  hasRequiredStringify$1 = 1;
  var parent = requireStringify$2();
  stringify$1 = parent;
  return stringify$1;
}
var stringify;
var hasRequiredStringify;
function requireStringify() {
  if (hasRequiredStringify) return stringify;
  hasRequiredStringify = 1;
  stringify = requireStringify$1();
  return stringify;
}
var stringifyExports = requireStringify();
var _JSON$stringify = getDefaultExportFromCjs(stringifyExports);
var iterator;
var hasRequiredIterator;
function requireIterator() {
  if (hasRequiredIterator) return iterator;
  hasRequiredIterator = 1;
  iterator = requireIterator$4();
  return iterator;
}
var iteratorExports = requireIterator();
var _Symbol$iterator = getDefaultExportFromCjs(iteratorExports);
var entries$3;
var hasRequiredEntries$3;
function requireEntries$3() {
  if (hasRequiredEntries$3) return entries$3;
  hasRequiredEntries$3 = 1;
  requireEs_array_iterator();
  var getBuiltInPrototypeMethod2 = requireGetBuiltInPrototypeMethod();
  entries$3 = getBuiltInPrototypeMethod2("Array", "entries");
  return entries$3;
}
var entries$2;
var hasRequiredEntries$2;
function requireEntries$2() {
  if (hasRequiredEntries$2) return entries$2;
  hasRequiredEntries$2 = 1;
  var parent = requireEntries$3();
  entries$2 = parent;
  return entries$2;
}
var entries$1;
var hasRequiredEntries$1;
function requireEntries$1() {
  if (hasRequiredEntries$1) return entries$1;
  hasRequiredEntries$1 = 1;
  requireWeb_domCollections_iterator();
  var classof2 = requireClassof();
  var hasOwn = requireHasOwnProperty();
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var method = requireEntries$2();
  var ArrayPrototype = Array.prototype;
  var DOMIterables = {
    DOMTokenList: true,
    NodeList: true
  };
  entries$1 = function(it) {
    var own = it.entries;
    return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.entries || hasOwn(DOMIterables, classof2(it)) ? method : own;
  };
  return entries$1;
}
var entries;
var hasRequiredEntries;
function requireEntries() {
  if (hasRequiredEntries) return entries;
  hasRequiredEntries = 1;
  entries = requireEntries$1();
  return entries;
}
var entriesExports = requireEntries();
var _entriesInstanceProperty = getDefaultExportFromCjs(entriesExports);
var byteToHex = [];
for (let i = 0; i < 256; ++i) {
  byteToHex.push((i + 256).toString(16).slice(1));
}
function unsafeStringify(arr, offset = 0) {
  return (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + "-" + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + "-" + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + "-" + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + "-" + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase();
}
var getRandomValues;
var rnds8 = new Uint8Array(16);
function rng() {
  if (!getRandomValues) {
    if (typeof crypto === "undefined" || !crypto.getRandomValues) {
      throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");
    }
    getRandomValues = crypto.getRandomValues.bind(crypto);
  }
  return getRandomValues(rnds8);
}
var randomUUID = typeof crypto !== "undefined" && crypto.randomUUID && crypto.randomUUID.bind(crypto);
var native = { randomUUID };
function v4(options, buf, offset) {
  if (native.randomUUID && true && !options) {
    return native.randomUUID();
  }
  options = options || {};
  const rnds = options.random ?? options.rng?.() ?? rng();
  if (rnds.length < 16) {
    throw new Error("Random bytes length must be >= 16");
  }
  rnds[6] = rnds[6] & 15 | 64;
  rnds[8] = rnds[8] & 63 | 128;
  return unsafeStringify(rnds);
}
function isId(value) {
  return typeof value === "string" || typeof value === "number";
}
var web_timers = {};
var web_setInterval = {};
var validateArgumentsLength;
var hasRequiredValidateArgumentsLength;
function requireValidateArgumentsLength() {
  if (hasRequiredValidateArgumentsLength) return validateArgumentsLength;
  hasRequiredValidateArgumentsLength = 1;
  var $TypeError = TypeError;
  validateArgumentsLength = function(passed, required) {
    if (passed < required) throw new $TypeError("Not enough arguments");
    return passed;
  };
  return validateArgumentsLength;
}
var schedulersFix;
var hasRequiredSchedulersFix;
function requireSchedulersFix() {
  if (hasRequiredSchedulersFix) return schedulersFix;
  hasRequiredSchedulersFix = 1;
  var globalThis2 = requireGlobalThis();
  var apply = requireFunctionApply();
  var isCallable2 = requireIsCallable();
  var ENVIRONMENT = requireEnvironment();
  var USER_AGENT = requireEnvironmentUserAgent();
  var arraySlice2 = requireArraySlice();
  var validateArgumentsLength2 = requireValidateArgumentsLength();
  var Function2 = globalThis2.Function;
  var WRAP = /MSIE .\./.test(USER_AGENT) || ENVIRONMENT === "BUN" && (function() {
    var version = globalThis2.Bun.version.split(".");
    return version.length < 3 || version[0] === "0" && (version[1] < 3 || version[1] === "3" && version[2] === "0");
  })();
  schedulersFix = function(scheduler, hasTimeArg) {
    var firstParamIndex = hasTimeArg ? 2 : 1;
    return WRAP ? function(handler, timeout) {
      var boundArgs = validateArgumentsLength2(arguments.length, 1) > firstParamIndex;
      var fn = isCallable2(handler) ? handler : Function2(handler);
      var params = boundArgs ? arraySlice2(arguments, firstParamIndex) : [];
      var callback = boundArgs ? function() {
        apply(fn, this, params);
      } : fn;
      return hasTimeArg ? scheduler(callback, timeout) : scheduler(callback);
    } : scheduler;
  };
  return schedulersFix;
}
var hasRequiredWeb_setInterval;
function requireWeb_setInterval() {
  if (hasRequiredWeb_setInterval) return web_setInterval;
  hasRequiredWeb_setInterval = 1;
  var $ = require_export();
  var globalThis2 = requireGlobalThis();
  var schedulersFix2 = requireSchedulersFix();
  var setInterval = schedulersFix2(globalThis2.setInterval, true);
  $({ global: true, bind: true, forced: globalThis2.setInterval !== setInterval }, {
    setInterval
  });
  return web_setInterval;
}
var web_setTimeout = {};
var hasRequiredWeb_setTimeout;
function requireWeb_setTimeout() {
  if (hasRequiredWeb_setTimeout) return web_setTimeout;
  hasRequiredWeb_setTimeout = 1;
  var $ = require_export();
  var globalThis2 = requireGlobalThis();
  var schedulersFix2 = requireSchedulersFix();
  var setTimeout2 = schedulersFix2(globalThis2.setTimeout, true);
  $({ global: true, bind: true, forced: globalThis2.setTimeout !== setTimeout2 }, {
    setTimeout: setTimeout2
  });
  return web_setTimeout;
}
var hasRequiredWeb_timers;
function requireWeb_timers() {
  if (hasRequiredWeb_timers) return web_timers;
  hasRequiredWeb_timers = 1;
  requireWeb_setInterval();
  requireWeb_setTimeout();
  return web_timers;
}
var setTimeout$2;
var hasRequiredSetTimeout$1;
function requireSetTimeout$1() {
  if (hasRequiredSetTimeout$1) return setTimeout$2;
  hasRequiredSetTimeout$1 = 1;
  requireWeb_timers();
  var path2 = requirePath();
  setTimeout$2 = path2.setTimeout;
  return setTimeout$2;
}
var setTimeout$1;
var hasRequiredSetTimeout;
function requireSetTimeout() {
  if (hasRequiredSetTimeout) return setTimeout$1;
  hasRequiredSetTimeout = 1;
  setTimeout$1 = requireSetTimeout$1();
  return setTimeout$1;
}
var setTimeoutExports = requireSetTimeout();
var _setTimeout = getDefaultExportFromCjs(setTimeoutExports);
var es_array_splice = {};
var arraySetLength;
var hasRequiredArraySetLength;
function requireArraySetLength() {
  if (hasRequiredArraySetLength) return arraySetLength;
  hasRequiredArraySetLength = 1;
  var DESCRIPTORS = requireDescriptors();
  var isArray2 = requireIsArray$3();
  var $TypeError = TypeError;
  var getOwnPropertyDescriptor2 = Object.getOwnPropertyDescriptor;
  var SILENT_ON_NON_WRITABLE_LENGTH_SET = DESCRIPTORS && !(function() {
    if (this !== void 0) return true;
    try {
      Object.defineProperty([], "length", { writable: false }).length = 1;
    } catch (error) {
      return error instanceof TypeError;
    }
  })();
  arraySetLength = SILENT_ON_NON_WRITABLE_LENGTH_SET ? function(O, length) {
    if (isArray2(O) && !getOwnPropertyDescriptor2(O, "length").writable) {
      throw new $TypeError("Cannot set read only .length");
    }
    return O.length = length;
  } : function(O, length) {
    return O.length = length;
  };
  return arraySetLength;
}
var hasRequiredEs_array_splice;
function requireEs_array_splice() {
  if (hasRequiredEs_array_splice) return es_array_splice;
  hasRequiredEs_array_splice = 1;
  var $ = require_export();
  var toObject2 = requireToObject();
  var toAbsoluteIndex2 = requireToAbsoluteIndex();
  var toIntegerOrInfinity2 = requireToIntegerOrInfinity();
  var lengthOfArrayLike2 = requireLengthOfArrayLike();
  var setArrayLength = requireArraySetLength();
  var doesNotExceedSafeInteger2 = requireDoesNotExceedSafeInteger();
  var arraySpeciesCreate2 = requireArraySpeciesCreate();
  var createProperty2 = requireCreateProperty();
  var deletePropertyOrThrow2 = requireDeletePropertyOrThrow();
  var arrayMethodHasSpeciesSupport2 = requireArrayMethodHasSpeciesSupport();
  var HAS_SPECIES_SUPPORT = arrayMethodHasSpeciesSupport2("splice");
  var max = Math.max;
  var min = Math.min;
  $({ target: "Array", proto: true, forced: !HAS_SPECIES_SUPPORT }, {
    splice: function splice2(start, deleteCount) {
      var O = toObject2(this);
      var len = lengthOfArrayLike2(O);
      var actualStart = toAbsoluteIndex2(start, len);
      var argumentsLength = arguments.length;
      var insertCount, actualDeleteCount, A, k, from, to;
      if (argumentsLength === 0) {
        insertCount = actualDeleteCount = 0;
      } else if (argumentsLength === 1) {
        insertCount = 0;
        actualDeleteCount = len - actualStart;
      } else {
        insertCount = argumentsLength - 2;
        actualDeleteCount = min(max(toIntegerOrInfinity2(deleteCount), 0), len - actualStart);
      }
      doesNotExceedSafeInteger2(len + insertCount - actualDeleteCount);
      A = arraySpeciesCreate2(O, actualDeleteCount);
      for (k = 0; k < actualDeleteCount; k++) {
        from = actualStart + k;
        if (from in O) createProperty2(A, k, O[from]);
      }
      A.length = actualDeleteCount;
      if (insertCount < actualDeleteCount) {
        for (k = actualStart; k < len - actualDeleteCount; k++) {
          from = k + actualDeleteCount;
          to = k + insertCount;
          if (from in O) O[to] = O[from];
          else deletePropertyOrThrow2(O, to);
        }
        for (k = len; k > len - actualDeleteCount + insertCount; k--) deletePropertyOrThrow2(O, k - 1);
      } else if (insertCount > actualDeleteCount) {
        for (k = len - actualDeleteCount; k > actualStart; k--) {
          from = k + actualDeleteCount - 1;
          to = k + insertCount - 1;
          if (from in O) O[to] = O[from];
          else deletePropertyOrThrow2(O, to);
        }
      }
      for (k = 0; k < insertCount; k++) {
        O[k + actualStart] = arguments[k + 2];
      }
      setArrayLength(O, len - actualDeleteCount + insertCount);
      return A;
    }
  });
  return es_array_splice;
}
var splice$3;
var hasRequiredSplice$3;
function requireSplice$3() {
  if (hasRequiredSplice$3) return splice$3;
  hasRequiredSplice$3 = 1;
  requireEs_array_splice();
  var getBuiltInPrototypeMethod2 = requireGetBuiltInPrototypeMethod();
  splice$3 = getBuiltInPrototypeMethod2("Array", "splice");
  return splice$3;
}
var splice$2;
var hasRequiredSplice$2;
function requireSplice$2() {
  if (hasRequiredSplice$2) return splice$2;
  hasRequiredSplice$2 = 1;
  var isPrototypeOf = requireObjectIsPrototypeOf();
  var method = requireSplice$3();
  var ArrayPrototype = Array.prototype;
  splice$2 = function(it) {
    var own = it.splice;
    return it === ArrayPrototype || isPrototypeOf(ArrayPrototype, it) && own === ArrayPrototype.splice ? method : own;
  };
  return splice$2;
}
var splice$1;
var hasRequiredSplice$1;
function requireSplice$1() {
  if (hasRequiredSplice$1) return splice$1;
  hasRequiredSplice$1 = 1;
  var parent = requireSplice$2();
  splice$1 = parent;
  return splice$1;
}
var splice;
var hasRequiredSplice;
function requireSplice() {
  if (hasRequiredSplice) return splice;
  hasRequiredSplice = 1;
  splice = requireSplice$1();
  return splice;
}
var spliceExports = requireSplice();
var _spliceInstanceProperty = getDefaultExportFromCjs(spliceExports);
var Queue = class _Queue {
  /**
   * Construct a new Queue.
   * @param options - Queue configuration.
   */
  constructor(options) {
    _defineProperty(this, "_queue", []);
    _defineProperty(this, "_timeout", null);
    _defineProperty(this, "_extended", null);
    this.delay = null;
    this.max = Infinity;
    this.setOptions(options);
  }
  /**
   * Update the configuration of the queue.
   * @param options - Queue configuration.
   */
  setOptions(options) {
    if (options && typeof options.delay !== "undefined") {
      this.delay = options.delay;
    }
    if (options && typeof options.max !== "undefined") {
      this.max = options.max;
    }
    this._flushIfNeeded();
  }
  /**
   * Extend an object with queuing functionality.
   * The object will be extended with a function flush, and the methods provided in options.replace will be replaced with queued ones.
   * @param object - The object to be extended.
   * @param options - Additional options.
   * @returns The created queue.
   */
  static extend(object, options) {
    const queue = new _Queue(options);
    if (object.flush !== void 0) {
      throw new Error("Target object already has a property flush");
    }
    object.flush = () => {
      queue.flush();
    };
    const methods = [{
      name: "flush",
      original: void 0
    }];
    if (options && options.replace) {
      for (let i = 0; i < options.replace.length; i++) {
        const name = options.replace[i];
        methods.push({
          name,
          // @TODO: better solution?
          original: object[name]
        });
        queue.replace(object, name);
      }
    }
    queue._extended = {
      object,
      methods
    };
    return queue;
  }
  /**
   * Destroy the queue. The queue will first flush all queued actions, and in case it has extended an object, will restore the original object.
   */
  destroy() {
    this.flush();
    if (this._extended) {
      const object = this._extended.object;
      const methods = this._extended.methods;
      for (let i = 0; i < methods.length; i++) {
        const method = methods[i];
        if (method.original) {
          object[method.name] = method.original;
        } else {
          delete object[method.name];
        }
      }
      this._extended = null;
    }
  }
  /**
   * Replace a method on an object with a queued version.
   * @param object - Object having the method.
   * @param method - The method name.
   */
  replace(object, method) {
    const me = this;
    const original = object[method];
    if (!original) {
      throw new Error("Method " + method + " undefined");
    }
    object[method] = function() {
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      me.queue({
        args,
        fn: original,
        context: this
      });
    };
  }
  /**
   * Queue a call.
   * @param entry - The function or entry to be queued.
   */
  queue(entry) {
    if (typeof entry === "function") {
      this._queue.push({
        fn: entry
      });
    } else {
      this._queue.push(entry);
    }
    this._flushIfNeeded();
  }
  /**
   * Check whether the queue needs to be flushed.
   */
  _flushIfNeeded() {
    if (this._queue.length > this.max) {
      this.flush();
    }
    if (this._timeout != null) {
      clearTimeout(this._timeout);
      this._timeout = null;
    }
    if (this.queue.length > 0 && typeof this.delay === "number") {
      this._timeout = _setTimeout(() => {
        this.flush();
      }, this.delay);
    }
  }
  /**
   * Flush all queued calls
   */
  flush() {
    var _context, _context2;
    _forEachInstanceProperty(_context = _spliceInstanceProperty(_context2 = this._queue).call(_context2, 0)).call(_context, (entry) => {
      entry.fn.apply(entry.context || entry.fn, entry.args || []);
    });
  }
};
var DataSetPart = class _DataSetPart {
  constructor() {
    _defineProperty(this, "_subscribers", {
      "*": [],
      add: [],
      remove: [],
      update: []
    });
    _defineProperty(this, "subscribe", _DataSetPart.prototype.on);
    _defineProperty(this, "unsubscribe", _DataSetPart.prototype.off);
  }
  /**
   * Trigger an event
   * @param event - Event name.
   * @param payload - Event payload.
   * @param senderId - Id of the sender.
   */
  _trigger(event, payload, senderId) {
    var _context;
    if (event === "*") {
      throw new Error("Cannot trigger event *");
    }
    _forEachInstanceProperty(_context = [...this._subscribers[event], ...this._subscribers["*"]]).call(_context, (subscriber) => {
      subscriber(event, payload, senderId != null ? senderId : null);
    });
  }
  /**
   * Subscribe to an event, add an event listener.
   * @remarks Non-function callbacks are ignored.
   * @param event - Event name.
   * @param callback - Callback method.
   */
  on(event, callback) {
    if (typeof callback === "function") {
      this._subscribers[event].push(callback);
    }
  }
  /**
   * Unsubscribe from an event, remove an event listener.
   * @remarks If the same callback was subscribed more than once **all** occurences will be removed.
   * @param event - Event name.
   * @param callback - Callback method.
   */
  off(event, callback) {
    var _context2;
    this._subscribers[event] = _filterInstanceProperty(_context2 = this._subscribers[event]).call(_context2, (subscriber) => subscriber !== callback);
  }
  /* develblock:start */
  get testLeakSubscribers() {
    return this._subscribers;
  }
};
var es_object_create = {};
var hasRequiredEs_object_create;
function requireEs_object_create() {
  if (hasRequiredEs_object_create) return es_object_create;
  hasRequiredEs_object_create = 1;
  var $ = require_export();
  var DESCRIPTORS = requireDescriptors();
  var create2 = requireObjectCreate();
  $({ target: "Object", stat: true, sham: !DESCRIPTORS }, {
    create: create2
  });
  return es_object_create;
}
var create$2;
var hasRequiredCreate$2;
function requireCreate$2() {
  if (hasRequiredCreate$2) return create$2;
  hasRequiredCreate$2 = 1;
  requireEs_object_create();
  var path2 = requirePath();
  var Object2 = path2.Object;
  create$2 = function create2(P, D) {
    return Object2.create(P, D);
  };
  return create$2;
}
var create$1;
var hasRequiredCreate$1;
function requireCreate$1() {
  if (hasRequiredCreate$1) return create$1;
  hasRequiredCreate$1 = 1;
  var parent = requireCreate$2();
  create$1 = parent;
  return create$1;
}
var create;
var hasRequiredCreate;
function requireCreate() {
  if (hasRequiredCreate) return create;
  hasRequiredCreate = 1;
  create = requireCreate$1();
  return create;
}
var createExports = requireCreate();
var _Object$create = getDefaultExportFromCjs(createExports);
var es_set = {};
var es_set_constructor = {};
var hasRequiredEs_set_constructor;
function requireEs_set_constructor() {
  if (hasRequiredEs_set_constructor) return es_set_constructor;
  hasRequiredEs_set_constructor = 1;
  var collection2 = requireCollection();
  var collectionStrong2 = requireCollectionStrong();
  collection2("Set", function(init) {
    return function Set2() {
      return init(this, arguments.length ? arguments[0] : void 0);
    };
  }, collectionStrong2);
  return es_set_constructor;
}
var hasRequiredEs_set;
function requireEs_set() {
  if (hasRequiredEs_set) return es_set;
  hasRequiredEs_set = 1;
  requireEs_set_constructor();
  return es_set;
}
var es_set_difference_v2 = {};
var aSet;
var hasRequiredASet;
function requireASet() {
  if (hasRequiredASet) return aSet;
  hasRequiredASet = 1;
  var tryToString2 = requireTryToString();
  var $TypeError = TypeError;
  aSet = function(it) {
    if (typeof it == "object" && "size" in it && "has" in it && "add" in it && "delete" in it && "keys" in it) return it;
    throw new $TypeError(tryToString2(it) + " is not a set");
  };
  return aSet;
}
var setHelpers;
var hasRequiredSetHelpers;
function requireSetHelpers() {
  if (hasRequiredSetHelpers) return setHelpers;
  hasRequiredSetHelpers = 1;
  var getBuiltIn2 = requireGetBuiltIn();
  var caller2 = requireCaller();
  var Set2 = getBuiltIn2("Set");
  var SetPrototype = Set2.prototype;
  setHelpers = {
    Set: Set2,
    add: caller2("add", 1),
    has: caller2("has", 1),
    remove: caller2("delete", 1),
    proto: SetPrototype
  };
  return setHelpers;
}
var iterateSimple;
var hasRequiredIterateSimple;
function requireIterateSimple() {
  if (hasRequiredIterateSimple) return iterateSimple;
  hasRequiredIterateSimple = 1;
  var call = requireFunctionCall();
  iterateSimple = function(record, fn, ITERATOR_INSTEAD_OF_RECORD) {
    var iterator2 = ITERATOR_INSTEAD_OF_RECORD ? record : record.iterator;
    var next = record.next;
    var step, result;
    while (!(step = call(next, iterator2)).done) {
      result = fn(step.value);
      if (result !== void 0) return result;
    }
  };
  return iterateSimple;
}
var setIterate;
var hasRequiredSetIterate;
function requireSetIterate() {
  if (hasRequiredSetIterate) return setIterate;
  hasRequiredSetIterate = 1;
  var iterateSimple2 = requireIterateSimple();
  setIterate = function(set2, fn, interruptible) {
    return interruptible ? iterateSimple2(set2.keys(), fn, true) : set2.forEach(fn);
  };
  return setIterate;
}
var setClone;
var hasRequiredSetClone;
function requireSetClone() {
  if (hasRequiredSetClone) return setClone;
  hasRequiredSetClone = 1;
  var SetHelpers = requireSetHelpers();
  var iterate2 = requireSetIterate();
  var Set2 = SetHelpers.Set;
  var add = SetHelpers.add;
  setClone = function(set2) {
    var result = new Set2();
    iterate2(set2, function(it) {
      add(result, it);
    });
    return result;
  };
  return setClone;
}
var setSize;
var hasRequiredSetSize;
function requireSetSize() {
  if (hasRequiredSetSize) return setSize;
  hasRequiredSetSize = 1;
  setSize = function(set2) {
    return set2.size;
  };
  return setSize;
}
var getIteratorDirect;
var hasRequiredGetIteratorDirect;
function requireGetIteratorDirect() {
  if (hasRequiredGetIteratorDirect) return getIteratorDirect;
  hasRequiredGetIteratorDirect = 1;
  getIteratorDirect = function(obj) {
    return {
      iterator: obj,
      next: obj.next,
      done: false
    };
  };
  return getIteratorDirect;
}
var getSetRecord;
var hasRequiredGetSetRecord;
function requireGetSetRecord() {
  if (hasRequiredGetSetRecord) return getSetRecord;
  hasRequiredGetSetRecord = 1;
  var aCallable2 = requireACallable();
  var anObject2 = requireAnObject();
  var call = requireFunctionCall();
  var toIntegerOrInfinity2 = requireToIntegerOrInfinity();
  var getIteratorDirect2 = requireGetIteratorDirect();
  var INVALID_SIZE = "Invalid size";
  var $RangeError = RangeError;
  var $TypeError = TypeError;
  var max = Math.max;
  var SetRecord = function(set2, intSize) {
    this.set = set2;
    this.size = max(intSize, 0);
    this.has = aCallable2(set2.has);
    this.keys = aCallable2(set2.keys);
  };
  SetRecord.prototype = {
    getIterator: function() {
      return getIteratorDirect2(anObject2(call(this.keys, this.set)));
    },
    includes: function(it) {
      return call(this.has, this.set, it);
    }
  };
  getSetRecord = function(obj) {
    anObject2(obj);
    var numSize = +obj.size;
    if (numSize !== numSize) throw new $TypeError(INVALID_SIZE);
    var intSize = toIntegerOrInfinity2(numSize);
    if (intSize < 0) throw new $RangeError(INVALID_SIZE);
    return new SetRecord(obj, intSize);
  };
  return getSetRecord;
}
var setDifference;
var hasRequiredSetDifference;
function requireSetDifference() {
  if (hasRequiredSetDifference) return setDifference;
  hasRequiredSetDifference = 1;
  var aSet2 = requireASet();
  var SetHelpers = requireSetHelpers();
  var clone2 = requireSetClone();
  var size = requireSetSize();
  var getSetRecord2 = requireGetSetRecord();
  var iterateSet = requireSetIterate();
  var iterateSimple2 = requireIterateSimple();
  var has = SetHelpers.has;
  var remove = SetHelpers.remove;
  setDifference = function difference(other) {
    var O = aSet2(this);
    var otherRec = getSetRecord2(other);
    var result = clone2(O);
    if (size(O) <= otherRec.size) iterateSet(O, function(e) {
      if (otherRec.includes(e)) remove(result, e);
    });
    else iterateSimple2(otherRec.getIterator(), function(e) {
      if (has(result, e)) remove(result, e);
    });
    return result;
  };
  return setDifference;
}
var setMethodAcceptSetLike;
var hasRequiredSetMethodAcceptSetLike;
function requireSetMethodAcceptSetLike() {
  if (hasRequiredSetMethodAcceptSetLike) return setMethodAcceptSetLike;
  hasRequiredSetMethodAcceptSetLike = 1;
  setMethodAcceptSetLike = function() {
    return false;
  };
  return setMethodAcceptSetLike;
}
var hasRequiredEs_set_difference_v2;
function requireEs_set_difference_v2() {
  if (hasRequiredEs_set_difference_v2) return es_set_difference_v2;
  hasRequiredEs_set_difference_v2 = 1;
  var $ = require_export();
  var difference = requireSetDifference();
  var fails2 = requireFails();
  var setMethodAcceptSetLike2 = requireSetMethodAcceptSetLike();
  var SET_LIKE_INCORRECT_BEHAVIOR = !setMethodAcceptSetLike2("difference", function(result) {
    return result.size === 0;
  });
  var FORCED = SET_LIKE_INCORRECT_BEHAVIOR || fails2(function() {
    var setLike = {
      size: 1,
      has: function() {
        return true;
      },
      keys: function() {
        var index = 0;
        return {
          next: function() {
            var done = index++ > 1;
            if (baseSet.has(1)) baseSet.clear();
            return { done, value: 2 };
          }
        };
      }
    };
    var baseSet = /* @__PURE__ */ new Set([1, 2, 3, 4]);
    return baseSet.difference(setLike).size !== 3;
  });
  $({ target: "Set", proto: true, real: true, forced: FORCED }, {
    difference
  });
  return es_set_difference_v2;
}
var es_set_intersection_v2 = {};
var setIntersection;
var hasRequiredSetIntersection;
function requireSetIntersection() {
  if (hasRequiredSetIntersection) return setIntersection;
  hasRequiredSetIntersection = 1;
  var aSet2 = requireASet();
  var SetHelpers = requireSetHelpers();
  var size = requireSetSize();
  var getSetRecord2 = requireGetSetRecord();
  var iterateSet = requireSetIterate();
  var iterateSimple2 = requireIterateSimple();
  var Set2 = SetHelpers.Set;
  var add = SetHelpers.add;
  var has = SetHelpers.has;
  setIntersection = function intersection(other) {
    var O = aSet2(this);
    var otherRec = getSetRecord2(other);
    var result = new Set2();
    if (size(O) > otherRec.size) {
      iterateSimple2(otherRec.getIterator(), function(e) {
        if (has(O, e)) add(result, e);
      });
    } else {
      iterateSet(O, function(e) {
        if (otherRec.includes(e)) add(result, e);
      });
    }
    return result;
  };
  return setIntersection;
}
var hasRequiredEs_set_intersection_v2;
function requireEs_set_intersection_v2() {
  if (hasRequiredEs_set_intersection_v2) return es_set_intersection_v2;
  hasRequiredEs_set_intersection_v2 = 1;
  var $ = require_export();
  var fails2 = requireFails();
  var intersection = requireSetIntersection();
  var setMethodAcceptSetLike2 = requireSetMethodAcceptSetLike();
  var INCORRECT = !setMethodAcceptSetLike2("intersection", function(result) {
    return result.size === 2 && result.has(1) && result.has(2);
  }) || fails2(function() {
    return String(Array.from((/* @__PURE__ */ new Set([1, 2, 3])).intersection(/* @__PURE__ */ new Set([3, 2])))) !== "3,2";
  });
  $({ target: "Set", proto: true, real: true, forced: INCORRECT }, {
    intersection
  });
  return es_set_intersection_v2;
}
var es_set_isDisjointFrom_v2 = {};
var setIsDisjointFrom;
var hasRequiredSetIsDisjointFrom;
function requireSetIsDisjointFrom() {
  if (hasRequiredSetIsDisjointFrom) return setIsDisjointFrom;
  hasRequiredSetIsDisjointFrom = 1;
  var aSet2 = requireASet();
  var has = requireSetHelpers().has;
  var size = requireSetSize();
  var getSetRecord2 = requireGetSetRecord();
  var iterateSet = requireSetIterate();
  var iterateSimple2 = requireIterateSimple();
  var iteratorClose2 = requireIteratorClose();
  setIsDisjointFrom = function isDisjointFrom(other) {
    var O = aSet2(this);
    var otherRec = getSetRecord2(other);
    if (size(O) <= otherRec.size) return iterateSet(O, function(e) {
      if (otherRec.includes(e)) return false;
    }, true) !== false;
    var iterator2 = otherRec.getIterator();
    return iterateSimple2(iterator2, function(e) {
      if (has(O, e)) return iteratorClose2(iterator2, "normal", false);
    }) !== false;
  };
  return setIsDisjointFrom;
}
var hasRequiredEs_set_isDisjointFrom_v2;
function requireEs_set_isDisjointFrom_v2() {
  if (hasRequiredEs_set_isDisjointFrom_v2) return es_set_isDisjointFrom_v2;
  hasRequiredEs_set_isDisjointFrom_v2 = 1;
  var $ = require_export();
  var isDisjointFrom = requireSetIsDisjointFrom();
  var setMethodAcceptSetLike2 = requireSetMethodAcceptSetLike();
  var INCORRECT = !setMethodAcceptSetLike2("isDisjointFrom", function(result) {
    return !result;
  });
  $({ target: "Set", proto: true, real: true, forced: INCORRECT }, {
    isDisjointFrom
  });
  return es_set_isDisjointFrom_v2;
}
var es_set_isSubsetOf_v2 = {};
var setIsSubsetOf;
var hasRequiredSetIsSubsetOf;
function requireSetIsSubsetOf() {
  if (hasRequiredSetIsSubsetOf) return setIsSubsetOf;
  hasRequiredSetIsSubsetOf = 1;
  var aSet2 = requireASet();
  var size = requireSetSize();
  var iterate2 = requireSetIterate();
  var getSetRecord2 = requireGetSetRecord();
  setIsSubsetOf = function isSubsetOf(other) {
    var O = aSet2(this);
    var otherRec = getSetRecord2(other);
    if (size(O) > otherRec.size) return false;
    return iterate2(O, function(e) {
      if (!otherRec.includes(e)) return false;
    }, true) !== false;
  };
  return setIsSubsetOf;
}
var hasRequiredEs_set_isSubsetOf_v2;
function requireEs_set_isSubsetOf_v2() {
  if (hasRequiredEs_set_isSubsetOf_v2) return es_set_isSubsetOf_v2;
  hasRequiredEs_set_isSubsetOf_v2 = 1;
  var $ = require_export();
  var isSubsetOf = requireSetIsSubsetOf();
  var setMethodAcceptSetLike2 = requireSetMethodAcceptSetLike();
  var INCORRECT = !setMethodAcceptSetLike2("isSubsetOf", function(result) {
    return result;
  });
  $({ target: "Set", proto: true, real: true, forced: INCORRECT }, {
    isSubsetOf
  });
  return es_set_isSubsetOf_v2;
}
var es_set_isSupersetOf_v2 = {};
var setIsSupersetOf;
var hasRequiredSetIsSupersetOf;
function requireSetIsSupersetOf() {
  if (hasRequiredSetIsSupersetOf) return setIsSupersetOf;
  hasRequiredSetIsSupersetOf = 1;
  var aSet2 = requireASet();
  var has = requireSetHelpers().has;
  var size = requireSetSize();
  var getSetRecord2 = requireGetSetRecord();
  var iterateSimple2 = requireIterateSimple();
  var iteratorClose2 = requireIteratorClose();
  setIsSupersetOf = function isSupersetOf(other) {
    var O = aSet2(this);
    var otherRec = getSetRecord2(other);
    if (size(O) < otherRec.size) return false;
    var iterator2 = otherRec.getIterator();
    return iterateSimple2(iterator2, function(e) {
      if (!has(O, e)) return iteratorClose2(iterator2, "normal", false);
    }) !== false;
  };
  return setIsSupersetOf;
}
var hasRequiredEs_set_isSupersetOf_v2;
function requireEs_set_isSupersetOf_v2() {
  if (hasRequiredEs_set_isSupersetOf_v2) return es_set_isSupersetOf_v2;
  hasRequiredEs_set_isSupersetOf_v2 = 1;
  var $ = require_export();
  var isSupersetOf = requireSetIsSupersetOf();
  var setMethodAcceptSetLike2 = requireSetMethodAcceptSetLike();
  var INCORRECT = !setMethodAcceptSetLike2("isSupersetOf", function(result) {
    return !result;
  });
  $({ target: "Set", proto: true, real: true, forced: INCORRECT }, {
    isSupersetOf
  });
  return es_set_isSupersetOf_v2;
}
var es_set_symmetricDifference_v2 = {};
var setSymmetricDifference;
var hasRequiredSetSymmetricDifference;
function requireSetSymmetricDifference() {
  if (hasRequiredSetSymmetricDifference) return setSymmetricDifference;
  hasRequiredSetSymmetricDifference = 1;
  var aSet2 = requireASet();
  var SetHelpers = requireSetHelpers();
  var clone2 = requireSetClone();
  var getSetRecord2 = requireGetSetRecord();
  var iterateSimple2 = requireIterateSimple();
  var add = SetHelpers.add;
  var has = SetHelpers.has;
  var remove = SetHelpers.remove;
  setSymmetricDifference = function symmetricDifference(other) {
    var O = aSet2(this);
    var keysIter = getSetRecord2(other).getIterator();
    var result = clone2(O);
    iterateSimple2(keysIter, function(e) {
      if (has(O, e)) remove(result, e);
      else add(result, e);
    });
    return result;
  };
  return setSymmetricDifference;
}
var setMethodGetKeysBeforeCloningDetection;
var hasRequiredSetMethodGetKeysBeforeCloningDetection;
function requireSetMethodGetKeysBeforeCloningDetection() {
  if (hasRequiredSetMethodGetKeysBeforeCloningDetection) return setMethodGetKeysBeforeCloningDetection;
  hasRequiredSetMethodGetKeysBeforeCloningDetection = 1;
  setMethodGetKeysBeforeCloningDetection = function(METHOD_NAME) {
    try {
      var baseSet = /* @__PURE__ */ new Set();
      var setLike = {
        size: 0,
        has: function() {
          return true;
        },
        keys: function() {
          return Object.defineProperty({}, "next", {
            get: function() {
              baseSet.clear();
              baseSet.add(4);
              return function() {
                return { done: true };
              };
            }
          });
        }
      };
      var result = baseSet[METHOD_NAME](setLike);
      return result.size === 1 && result.values().next().value === 4;
    } catch (error) {
      return false;
    }
  };
  return setMethodGetKeysBeforeCloningDetection;
}
var hasRequiredEs_set_symmetricDifference_v2;
function requireEs_set_symmetricDifference_v2() {
  if (hasRequiredEs_set_symmetricDifference_v2) return es_set_symmetricDifference_v2;
  hasRequiredEs_set_symmetricDifference_v2 = 1;
  var $ = require_export();
  var symmetricDifference = requireSetSymmetricDifference();
  var setMethodGetKeysBeforeCloning = requireSetMethodGetKeysBeforeCloningDetection();
  var setMethodAcceptSetLike2 = requireSetMethodAcceptSetLike();
  var FORCED = !setMethodAcceptSetLike2("symmetricDifference") || !setMethodGetKeysBeforeCloning("symmetricDifference");
  $({ target: "Set", proto: true, real: true, forced: FORCED }, {
    symmetricDifference
  });
  return es_set_symmetricDifference_v2;
}
var es_set_union_v2 = {};
var setUnion;
var hasRequiredSetUnion;
function requireSetUnion() {
  if (hasRequiredSetUnion) return setUnion;
  hasRequiredSetUnion = 1;
  var aSet2 = requireASet();
  var add = requireSetHelpers().add;
  var clone2 = requireSetClone();
  var getSetRecord2 = requireGetSetRecord();
  var iterateSimple2 = requireIterateSimple();
  setUnion = function union(other) {
    var O = aSet2(this);
    var keysIter = getSetRecord2(other).getIterator();
    var result = clone2(O);
    iterateSimple2(keysIter, function(it) {
      add(result, it);
    });
    return result;
  };
  return setUnion;
}
var hasRequiredEs_set_union_v2;
function requireEs_set_union_v2() {
  if (hasRequiredEs_set_union_v2) return es_set_union_v2;
  hasRequiredEs_set_union_v2 = 1;
  var $ = require_export();
  var union = requireSetUnion();
  var setMethodGetKeysBeforeCloning = requireSetMethodGetKeysBeforeCloningDetection();
  var setMethodAcceptSetLike2 = requireSetMethodAcceptSetLike();
  var FORCED = !setMethodAcceptSetLike2("union") || !setMethodGetKeysBeforeCloning("union");
  $({ target: "Set", proto: true, real: true, forced: FORCED }, {
    union
  });
  return es_set_union_v2;
}
var set$2;
var hasRequiredSet$2;
function requireSet$2() {
  if (hasRequiredSet$2) return set$2;
  hasRequiredSet$2 = 1;
  requireEs_array_iterator();
  requireEs_set();
  requireEs_set_difference_v2();
  requireEs_set_intersection_v2();
  requireEs_set_isDisjointFrom_v2();
  requireEs_set_isSubsetOf_v2();
  requireEs_set_isSupersetOf_v2();
  requireEs_set_symmetricDifference_v2();
  requireEs_set_union_v2();
  requireEs_string_iterator();
  var path2 = requirePath();
  set$2 = path2.Set;
  return set$2;
}
var set$1;
var hasRequiredSet$1;
function requireSet$1() {
  if (hasRequiredSet$1) return set$1;
  hasRequiredSet$1 = 1;
  var parent = requireSet$2();
  requireWeb_domCollections_iterator();
  set$1 = parent;
  return set$1;
}
var set;
var hasRequiredSet;
function requireSet() {
  if (hasRequiredSet) return set;
  hasRequiredSet = 1;
  set = requireSet$1();
  return set;
}
var setExports = requireSet();
var _Set = getDefaultExportFromCjs(setExports);
var getIterator_1;
var hasRequiredGetIterator$5;
function requireGetIterator$5() {
  if (hasRequiredGetIterator$5) return getIterator_1;
  hasRequiredGetIterator$5 = 1;
  requireEs_array_iterator();
  requireEs_string_iterator();
  var getIterator2 = requireGetIterator$6();
  getIterator_1 = getIterator2;
  return getIterator_1;
}
var getIterator$4;
var hasRequiredGetIterator$4;
function requireGetIterator$4() {
  if (hasRequiredGetIterator$4) return getIterator$4;
  hasRequiredGetIterator$4 = 1;
  var parent = requireGetIterator$5();
  requireWeb_domCollections_iterator();
  getIterator$4 = parent;
  return getIterator$4;
}
var getIterator$3;
var hasRequiredGetIterator$3;
function requireGetIterator$3() {
  if (hasRequiredGetIterator$3) return getIterator$3;
  hasRequiredGetIterator$3 = 1;
  var parent = requireGetIterator$4();
  getIterator$3 = parent;
  return getIterator$3;
}
var getIterator$2;
var hasRequiredGetIterator$2;
function requireGetIterator$2() {
  if (hasRequiredGetIterator$2) return getIterator$2;
  hasRequiredGetIterator$2 = 1;
  var parent = requireGetIterator$3();
  getIterator$2 = parent;
  return getIterator$2;
}
var getIterator$1;
var hasRequiredGetIterator$1;
function requireGetIterator$1() {
  if (hasRequiredGetIterator$1) return getIterator$1;
  hasRequiredGetIterator$1 = 1;
  getIterator$1 = requireGetIterator$2();
  return getIterator$1;
}
var getIterator;
var hasRequiredGetIterator;
function requireGetIterator() {
  if (hasRequiredGetIterator) return getIterator;
  hasRequiredGetIterator = 1;
  getIterator = requireGetIterator$1();
  return getIterator;
}
var getIteratorExports = requireGetIterator();
var _getIterator = getDefaultExportFromCjs(getIteratorExports);
var DataStream = class _DataStream {
  /**
   * Create a new data stream.
   * @param pairs - The id, item pairs.
   */
  constructor(pairs) {
    this._pairs = pairs;
  }
  /**
   * Return an iterable of key, value pairs for every entry in the stream.
   */
  *[_Symbol$iterator]() {
    for (const [id, item] of this._pairs) {
      yield [id, item];
    }
  }
  /**
   * Return an iterable of key, value pairs for every entry in the stream.
   */
  *entries() {
    for (const [id, item] of this._pairs) {
      yield [id, item];
    }
  }
  /**
   * Return an iterable of keys in the stream.
   */
  *keys() {
    for (const [id] of this._pairs) {
      yield id;
    }
  }
  /**
   * Return an iterable of values in the stream.
   */
  *values() {
    for (const [, item] of this._pairs) {
      yield item;
    }
  }
  /**
   * Return an array containing all the ids in this stream.
   * @remarks
   * The array may contain duplicities.
   * @returns The array with all ids from this stream.
   */
  toIdArray() {
    var _context;
    return _mapInstanceProperty(_context = [...this._pairs]).call(_context, (pair) => pair[0]);
  }
  /**
   * Return an array containing all the items in this stream.
   * @remarks
   * The array may contain duplicities.
   * @returns The array with all items from this stream.
   */
  toItemArray() {
    var _context2;
    return _mapInstanceProperty(_context2 = [...this._pairs]).call(_context2, (pair) => pair[1]);
  }
  /**
   * Return an array containing all the entries in this stream.
   * @remarks
   * The array may contain duplicities.
   * @returns The array with all entries from this stream.
   */
  toEntryArray() {
    return [...this._pairs];
  }
  /**
   * Return an object map containing all the items in this stream accessible by ids.
   * @remarks
   * In case of duplicate ids (coerced to string so `7 == '7'`) the last encoutered appears in the returned object.
   * @returns The object map of all id → item pairs from this stream.
   */
  toObjectMap() {
    const map2 = _Object$create(null);
    for (const [id, item] of this._pairs) {
      map2[id] = item;
    }
    return map2;
  }
  /**
   * Return a map containing all the items in this stream accessible by ids.
   * @returns The map of all id → item pairs from this stream.
   */
  toMap() {
    return new _Map(this._pairs);
  }
  /**
   * Return a set containing all the (unique) ids in this stream.
   * @returns The set of all ids from this stream.
   */
  toIdSet() {
    return new _Set(this.toIdArray());
  }
  /**
   * Return a set containing all the (unique) items in this stream.
   * @returns The set of all items from this stream.
   */
  toItemSet() {
    return new _Set(this.toItemArray());
  }
  /**
   * Cache the items from this stream.
   * @remarks
   * This method allows for items to be fetched immediatelly and used (possibly multiple times) later.
   * It can also be used to optimize performance as {@link DataStream} would otherwise reevaluate everything upon each iteration.
   *
   * ## Example
   * ```javascript
   * const ds = new DataSet([…])
   *
   * const cachedStream = ds.stream()
   *   .filter(…)
   *   .sort(…)
   *   .map(…)
   *   .cached(…) // Data are fetched, processed and cached here.
   *
   * ds.clear()
   * chachedStream // Still has all the items.
   * ```
   * @returns A new {@link DataStream} with cached items (detached from the original {@link DataSet}).
   */
  cache() {
    return new _DataStream([...this._pairs]);
  }
  /**
   * Get the distinct values of given property.
   * @param callback - The function that picks and possibly converts the property.
   * @typeParam T - The type of the distinct value.
   * @returns A set of all distinct properties.
   */
  distinct(callback) {
    const set2 = new _Set();
    for (const [id, item] of this._pairs) {
      set2.add(callback(item, id));
    }
    return set2;
  }
  /**
   * Filter the items of the stream.
   * @param callback - The function that decides whether an item will be included.
   * @returns A new data stream with the filtered items.
   */
  filter(callback) {
    const pairs = this._pairs;
    return new _DataStream({
      *[_Symbol$iterator]() {
        for (const [id, item] of pairs) {
          if (callback(item, id)) {
            yield [id, item];
          }
        }
      }
    });
  }
  /**
   * Execute a callback for each item of the stream.
   * @param callback - The function that will be invoked for each item.
   */
  forEach(callback) {
    for (const [id, item] of this._pairs) {
      callback(item, id);
    }
  }
  /**
   * Map the items into a different type.
   * @param callback - The function that does the conversion.
   * @typeParam Mapped - The type of the item after mapping.
   * @returns A new data stream with the mapped items.
   */
  map(callback) {
    const pairs = this._pairs;
    return new _DataStream({
      *[_Symbol$iterator]() {
        for (const [id, item] of pairs) {
          yield [id, callback(item, id)];
        }
      }
    });
  }
  /**
   * Get the item with the maximum value of given property.
   * @param callback - The function that picks and possibly converts the property.
   * @returns The item with the maximum if found otherwise null.
   */
  max(callback) {
    const iter = _getIterator(this._pairs);
    let curr = iter.next();
    if (curr.done) {
      return null;
    }
    let maxItem = curr.value[1];
    let maxValue = callback(curr.value[1], curr.value[0]);
    while (!(curr = iter.next()).done) {
      const [id, item] = curr.value;
      const value = callback(item, id);
      if (value > maxValue) {
        maxValue = value;
        maxItem = item;
      }
    }
    return maxItem;
  }
  /**
   * Get the item with the minimum value of given property.
   * @param callback - The function that picks and possibly converts the property.
   * @returns The item with the minimum if found otherwise null.
   */
  min(callback) {
    const iter = _getIterator(this._pairs);
    let curr = iter.next();
    if (curr.done) {
      return null;
    }
    let minItem = curr.value[1];
    let minValue = callback(curr.value[1], curr.value[0]);
    while (!(curr = iter.next()).done) {
      const [id, item] = curr.value;
      const value = callback(item, id);
      if (value < minValue) {
        minValue = value;
        minItem = item;
      }
    }
    return minItem;
  }
  /**
   * Reduce the items into a single value.
   * @param callback - The function that does the reduction.
   * @param accumulator - The initial value of the accumulator.
   * @typeParam T - The type of the accumulated value.
   * @returns The reduced value.
   */
  reduce(callback, accumulator) {
    for (const [id, item] of this._pairs) {
      accumulator = callback(accumulator, item, id);
    }
    return accumulator;
  }
  /**
   * Sort the items.
   * @param callback - Item comparator.
   * @returns A new stream with sorted items.
   */
  sort(callback) {
    return new _DataStream({
      [_Symbol$iterator]: () => {
        var _context3;
        return _getIterator(_sortInstanceProperty(_context3 = [...this._pairs]).call(_context3, (_ref, _ref2) => {
          let [idA, itemA] = _ref;
          let [idB, itemB] = _ref2;
          return callback(itemA, itemB, idA, idB);
        }));
      }
    });
  }
};
function ownKeys$3(e, r) {
  var t = _Object$keys(e);
  if (_Object$getOwnPropertySymbols) {
    var o = _Object$getOwnPropertySymbols(e);
    r && (o = _filterInstanceProperty(o).call(o, function(r2) {
      return _Object$getOwnPropertyDescriptor(e, r2).enumerable;
    })), t.push.apply(t, o);
  }
  return t;
}
function _objectSpread(e) {
  for (var r = 1; r < arguments.length; r++) {
    var _context8, _context9;
    var t = null != arguments[r] ? arguments[r] : {};
    r % 2 ? _forEachInstanceProperty(_context8 = ownKeys$3(Object(t), true)).call(_context8, function(r2) {
      _defineProperty(e, r2, t[r2]);
    }) : _Object$getOwnPropertyDescriptors ? _Object$defineProperties(e, _Object$getOwnPropertyDescriptors(t)) : _forEachInstanceProperty(_context9 = ownKeys$3(Object(t))).call(_context9, function(r2) {
      _Object$defineProperty(e, r2, _Object$getOwnPropertyDescriptor(t, r2));
    });
  }
  return e;
}
function ensureFullItem(item, idProp) {
  if (item[idProp] == null) {
    item[idProp] = v4();
  }
  return item;
}
var DataSet = class extends DataSetPart {
  /** Flush all queued calls. */
  /** @inheritDoc */
  /** @inheritDoc */
  get idProp() {
    return this._idProp;
  }
  /**
   * Construct a new DataSet.
   * @param data - Initial data or options.
   * @param options - Options (type error if data is also options).
   */
  constructor(data, options) {
    super();
    _defineProperty(this, "_queue", null);
    if (data && !_Array$isArray(data)) {
      options = data;
      data = [];
    }
    this._options = options || {};
    this._data = new _Map();
    this.length = 0;
    this._idProp = this._options.fieldId || "id";
    if (data && data.length) {
      this.add(data);
    }
    this.setOptions(options);
  }
  /**
   * Set new options.
   * @param options - The new options.
   */
  setOptions(options) {
    if (options && options.queue !== void 0) {
      if (options.queue === false) {
        if (this._queue) {
          this._queue.destroy();
          this._queue = null;
        }
      } else {
        if (!this._queue) {
          this._queue = Queue.extend(this, {
            replace: ["add", "update", "remove"]
          });
        }
        if (options.queue && typeof options.queue === "object") {
          this._queue.setOptions(options.queue);
        }
      }
    }
  }
  /**
   * Add a data item or an array with items.
   *
   * After the items are added to the DataSet, the DataSet will trigger an event `add`. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.
   *
   * ## Example
   *
   * ```javascript
   * // create a DataSet
   * const data = new vis.DataSet()
   *
   * // add items
   * const ids = data.add([
   * { id: 1, text: 'item 1' },
   * { id: 2, text: 'item 2' },
   * { text: 'item without an id' }
   * ])
   *
   * console.log(ids) // [1, 2, '<UUIDv4>']
   * ```
   * @param data - Items to be added (ids will be generated if missing).
   * @param senderId - Sender id.
   * @returns addedIds - Array with the ids (generated if not present) of the added items.
   * @throws When an item with the same id as any of the added items already exists.
   */
  add(data, senderId) {
    const addedIds = [];
    let id;
    if (_Array$isArray(data)) {
      const idsToAdd = _mapInstanceProperty(data).call(data, (d) => d[this._idProp]);
      if (_someInstanceProperty(idsToAdd).call(idsToAdd, (id2) => this._data.has(id2))) {
        throw new Error("A duplicate id was found in the parameter array.");
      }
      for (let i = 0, len = data.length; i < len; i++) {
        id = this._addItem(data[i]);
        addedIds.push(id);
      }
    } else if (data && typeof data === "object") {
      id = this._addItem(data);
      addedIds.push(id);
    } else {
      throw new Error("Unknown dataType");
    }
    if (addedIds.length) {
      this._trigger("add", {
        items: addedIds
      }, senderId);
    }
    return addedIds;
  }
  /**
   * Update existing items. When an item does not exist, it will be created.
   * @remarks
   * The provided properties will be merged in the existing item. When an item does not exist, it will be created.
   *
   * After the items are updated, the DataSet will trigger an event `add` for the added items, and an event `update`. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.
   *
   * ## Example
   *
   * ```javascript
   * // create a DataSet
   * const data = new vis.DataSet([
   *   { id: 1, text: 'item 1' },
   *   { id: 2, text: 'item 2' },
   *   { id: 3, text: 'item 3' }
   * ])
   *
   * // update items
   * const ids = data.update([
   *   { id: 2, text: 'item 2 (updated)' },
   *   { id: 4, text: 'item 4 (new)' }
   * ])
   *
   * console.log(ids) // [2, 4]
   * ```
   *
   * ## Warning for TypeScript users
   * This method may introduce partial items into the data set. Use add or updateOnly instead for better type safety.
   * @param data - Items to be updated (if the id is already present) or added (if the id is missing).
   * @param senderId - Sender id.
   * @returns updatedIds - The ids of the added (these may be newly generated if there was no id in the item from the data) or updated items.
   * @throws When the supplied data is neither an item nor an array of items.
   */
  update(data, senderId) {
    const addedIds = [];
    const updatedIds = [];
    const oldData = [];
    const updatedData = [];
    const idProp = this._idProp;
    const addOrUpdate = (item) => {
      const origId = item[idProp];
      if (origId != null && this._data.has(origId)) {
        const fullItem = item;
        const oldItem = _Object$assign({}, this._data.get(origId));
        const id = this._updateItem(fullItem);
        updatedIds.push(id);
        updatedData.push(fullItem);
        oldData.push(oldItem);
      } else {
        const id = this._addItem(item);
        addedIds.push(id);
      }
    };
    if (_Array$isArray(data)) {
      for (let i = 0, len = data.length; i < len; i++) {
        if (data[i] && typeof data[i] === "object") {
          addOrUpdate(data[i]);
        } else {
          console.warn("Ignoring input item, which is not an object at index " + i);
        }
      }
    } else if (data && typeof data === "object") {
      addOrUpdate(data);
    } else {
      throw new Error("Unknown dataType");
    }
    if (addedIds.length) {
      this._trigger("add", {
        items: addedIds
      }, senderId);
    }
    if (updatedIds.length) {
      const props = {
        items: updatedIds,
        oldData,
        data: updatedData
      };
      this._trigger("update", props, senderId);
    }
    return _concatInstanceProperty(addedIds).call(addedIds, updatedIds);
  }
  /**
   * Update existing items. When an item does not exist, an error will be thrown.
   * @remarks
   * The provided properties will be deeply merged into the existing item.
   * When an item does not exist (id not present in the data set or absent), an error will be thrown and nothing will be changed.
   *
   * After the items are updated, the DataSet will trigger an event `update`.
   * When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.
   *
   * ## Example
   *
   * ```javascript
   * // create a DataSet
   * const data = new vis.DataSet([
   *   { id: 1, text: 'item 1' },
   *   { id: 2, text: 'item 2' },
   *   { id: 3, text: 'item 3' },
   * ])
   *
   * // update items
   * const ids = data.update([
   *   { id: 2, text: 'item 2 (updated)' }, // works
   *   // { id: 4, text: 'item 4 (new)' }, // would throw
   *   // { text: 'item 4 (new)' }, // would also throw
   * ])
   *
   * console.log(ids) // [2]
   * ```
   * @param data - Updates (the id and optionally other props) to the items in this data set.
   * @param senderId - Sender id.
   * @returns updatedIds - The ids of the updated items.
   * @throws When the supplied data is neither an item nor an array of items, when the ids are missing.
   */
  updateOnly(data, senderId) {
    var _context;
    if (!_Array$isArray(data)) {
      data = [data];
    }
    const updateEventData = _mapInstanceProperty(_context = _mapInstanceProperty(data).call(data, (update) => {
      const oldData = this._data.get(update[this._idProp]);
      if (oldData == null) {
        throw new Error("Updating non-existent items is not allowed.");
      }
      return {
        oldData,
        update
      };
    })).call(_context, (_ref) => {
      let {
        oldData,
        update
      } = _ref;
      const id = oldData[this._idProp];
      const updatedData = pureDeepObjectAssign(oldData, update);
      this._data.set(id, updatedData);
      return {
        id,
        oldData,
        updatedData
      };
    });
    if (updateEventData.length) {
      const props = {
        items: _mapInstanceProperty(updateEventData).call(updateEventData, (value) => value.id),
        oldData: _mapInstanceProperty(updateEventData).call(updateEventData, (value) => value.oldData),
        data: _mapInstanceProperty(updateEventData).call(updateEventData, (value) => value.updatedData)
      };
      this._trigger("update", props, senderId);
      return props.items;
    } else {
      return [];
    }
  }
  /** @inheritDoc */
  get(first, second) {
    let id = void 0;
    let ids = void 0;
    let options = void 0;
    if (isId(first)) {
      id = first;
      options = second;
    } else if (_Array$isArray(first)) {
      ids = first;
      options = second;
    } else {
      options = first;
    }
    const returnType = options && options.returnType === "Object" ? "Object" : "Array";
    const filter2 = options && _filterInstanceProperty(options);
    const items = [];
    let item = void 0;
    let itemIds = void 0;
    let itemId = void 0;
    if (id != null) {
      item = this._data.get(id);
      if (item && filter2 && !filter2(item)) {
        item = void 0;
      }
    } else if (ids != null) {
      for (let i = 0, len = ids.length; i < len; i++) {
        item = this._data.get(ids[i]);
        if (item != null && (!filter2 || filter2(item))) {
          items.push(item);
        }
      }
    } else {
      var _context2;
      itemIds = [..._keysInstanceProperty(_context2 = this._data).call(_context2)];
      for (let i = 0, len = itemIds.length; i < len; i++) {
        itemId = itemIds[i];
        item = this._data.get(itemId);
        if (item != null && (!filter2 || filter2(item))) {
          items.push(item);
        }
      }
    }
    if (options && options.order && id == void 0) {
      this._sort(items, options.order);
    }
    if (options && options.fields) {
      const fields = options.fields;
      if (id != void 0 && item != null) {
        item = this._filterFields(item, fields);
      } else {
        for (let i = 0, len = items.length; i < len; i++) {
          items[i] = this._filterFields(items[i], fields);
        }
      }
    }
    if (returnType == "Object") {
      const result = {};
      for (let i = 0, len = items.length; i < len; i++) {
        const resultant = items[i];
        const id2 = resultant[this._idProp];
        result[id2] = resultant;
      }
      return result;
    } else {
      if (id != null) {
        return item !== null && item !== void 0 ? item : null;
      } else {
        return items;
      }
    }
  }
  /** @inheritDoc */
  getIds(options) {
    const data = this._data;
    const filter2 = options && _filterInstanceProperty(options);
    const order = options && options.order;
    const itemIds = [..._keysInstanceProperty(data).call(data)];
    const ids = [];
    if (filter2) {
      if (order) {
        const items = [];
        for (let i = 0, len = itemIds.length; i < len; i++) {
          const id = itemIds[i];
          const item = this._data.get(id);
          if (item != null && filter2(item)) {
            items.push(item);
          }
        }
        this._sort(items, order);
        for (let i = 0, len = items.length; i < len; i++) {
          ids.push(items[i][this._idProp]);
        }
      } else {
        for (let i = 0, len = itemIds.length; i < len; i++) {
          const id = itemIds[i];
          const item = this._data.get(id);
          if (item != null && filter2(item)) {
            ids.push(item[this._idProp]);
          }
        }
      }
    } else {
      if (order) {
        const items = [];
        for (let i = 0, len = itemIds.length; i < len; i++) {
          const id = itemIds[i];
          items.push(data.get(id));
        }
        this._sort(items, order);
        for (let i = 0, len = items.length; i < len; i++) {
          ids.push(items[i][this._idProp]);
        }
      } else {
        for (let i = 0, len = itemIds.length; i < len; i++) {
          const id = itemIds[i];
          const item = data.get(id);
          if (item != null) {
            ids.push(item[this._idProp]);
          }
        }
      }
    }
    return ids;
  }
  /** @inheritDoc */
  getDataSet() {
    return this;
  }
  /** @inheritDoc */
  forEach(callback, options) {
    const filter2 = options && _filterInstanceProperty(options);
    const data = this._data;
    const itemIds = [..._keysInstanceProperty(data).call(data)];
    if (options && options.order) {
      const items = this.get(options);
      for (let i = 0, len = items.length; i < len; i++) {
        const item = items[i];
        const id = item[this._idProp];
        callback(item, id);
      }
    } else {
      for (let i = 0, len = itemIds.length; i < len; i++) {
        const id = itemIds[i];
        const item = this._data.get(id);
        if (item != null && (!filter2 || filter2(item))) {
          callback(item, id);
        }
      }
    }
  }
  /** @inheritDoc */
  map(callback, options) {
    const filter2 = options && _filterInstanceProperty(options);
    const mappedItems = [];
    const data = this._data;
    const itemIds = [..._keysInstanceProperty(data).call(data)];
    for (let i = 0, len = itemIds.length; i < len; i++) {
      const id = itemIds[i];
      const item = this._data.get(id);
      if (item != null && (!filter2 || filter2(item))) {
        mappedItems.push(callback(item, id));
      }
    }
    if (options && options.order) {
      this._sort(mappedItems, options.order);
    }
    return mappedItems;
  }
  /**
   * Filter the fields of an item.
   * @param item - The item whose fields should be filtered.
   * @param fields - The names of the fields that will be kept.
   * @typeParam K - Field name type.
   * @returns The item without any additional fields.
   */
  _filterFields(item, fields) {
    var _context3;
    if (!item) {
      return item;
    }
    return _reduceInstanceProperty(_context3 = _Array$isArray(fields) ? (
      // Use the supplied array
      fields
    ) : (
      // Use the keys of the supplied object
      _Object$keys(fields)
    )).call(_context3, (filteredItem, field) => {
      filteredItem[field] = item[field];
      return filteredItem;
    }, {});
  }
  /**
   * Sort the provided array with items.
   * @param items - Items to be sorted in place.
   * @param order - A field name or custom sort function.
   * @typeParam T - The type of the items in the items array.
   */
  _sort(items, order) {
    if (typeof order === "string") {
      const name = order;
      _sortInstanceProperty(items).call(items, (a, b) => {
        const av = a[name];
        const bv = b[name];
        return av > bv ? 1 : av < bv ? -1 : 0;
      });
    } else if (typeof order === "function") {
      _sortInstanceProperty(items).call(items, order);
    } else {
      throw new TypeError("Order must be a function or a string");
    }
  }
  /**
   * Remove an item or multiple items by “reference” (only the id is used) or by id.
   *
   * The method ignores removal of non-existing items, and returns an array containing the ids of the items which are actually removed from the DataSet.
   *
   * After the items are removed, the DataSet will trigger an event `remove` for the removed items. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.
   *
   * ## Example
   * ```javascript
   * // create a DataSet
   * const data = new vis.DataSet([
   * { id: 1, text: 'item 1' },
   * { id: 2, text: 'item 2' },
   * { id: 3, text: 'item 3' }
   * ])
   *
   * // remove items
   * const ids = data.remove([2, { id: 3 }, 4])
   *
   * console.log(ids) // [2, 3]
   * ```
   * @param id - One or more items or ids of items to be removed.
   * @param senderId - Sender id.
   * @returns The ids of the removed items.
   */
  remove(id, senderId) {
    const removedIds = [];
    const removedItems = [];
    const ids = _Array$isArray(id) ? id : [id];
    for (let i = 0, len = ids.length; i < len; i++) {
      const item = this._remove(ids[i]);
      if (item) {
        const itemId = item[this._idProp];
        if (itemId != null) {
          removedIds.push(itemId);
          removedItems.push(item);
        }
      }
    }
    if (removedIds.length) {
      this._trigger("remove", {
        items: removedIds,
        oldData: removedItems
      }, senderId);
    }
    return removedIds;
  }
  /**
   * Remove an item by its id or reference.
   * @param id - Id of an item or the item itself.
   * @returns The removed item if removed, null otherwise.
   */
  _remove(id) {
    let ident;
    if (isId(id)) {
      ident = id;
    } else if (id && typeof id === "object") {
      ident = id[this._idProp];
    }
    if (ident != null && this._data.has(ident)) {
      const item = this._data.get(ident) || null;
      this._data.delete(ident);
      --this.length;
      return item;
    }
    return null;
  }
  /**
   * Clear the entire data set.
   *
   * After the items are removed, the {@link DataSet} will trigger an event `remove` for all removed items. When a `senderId` is provided, this id will be passed with the triggered event to all subscribers.
   * @param senderId - Sender id.
   * @returns removedIds - The ids of all removed items.
   */
  clear(senderId) {
    var _context4;
    const ids = [..._keysInstanceProperty(_context4 = this._data).call(_context4)];
    const items = [];
    for (let i = 0, len = ids.length; i < len; i++) {
      items.push(this._data.get(ids[i]));
    }
    this._data.clear();
    this.length = 0;
    this._trigger("remove", {
      items: ids,
      oldData: items
    }, senderId);
    return ids;
  }
  /**
   * Find the item with maximum value of a specified field.
   * @param field - Name of the property that should be searched for max value.
   * @returns Item containing max value, or null if no items.
   */
  max(field) {
    let max = null;
    let maxField = null;
    for (const item of _valuesInstanceProperty(_context5 = this._data).call(_context5)) {
      var _context5;
      const itemField = item[field];
      if (typeof itemField === "number" && (maxField == null || itemField > maxField)) {
        max = item;
        maxField = itemField;
      }
    }
    return max || null;
  }
  /**
   * Find the item with minimum value of a specified field.
   * @param field - Name of the property that should be searched for min value.
   * @returns Item containing min value, or null if no items.
   */
  min(field) {
    let min = null;
    let minField = null;
    for (const item of _valuesInstanceProperty(_context6 = this._data).call(_context6)) {
      var _context6;
      const itemField = item[field];
      if (typeof itemField === "number" && (minField == null || itemField < minField)) {
        min = item;
        minField = itemField;
      }
    }
    return min || null;
  }
  /**
   * Find all distinct values of a specified field
   * @param prop - The property name whose distinct values should be returned.
   * @returns Unordered array containing all distinct values. Items without specified property are ignored.
   */
  distinct(prop) {
    const data = this._data;
    const itemIds = [..._keysInstanceProperty(data).call(data)];
    const values2 = [];
    let count = 0;
    for (let i = 0, len = itemIds.length; i < len; i++) {
      const id = itemIds[i];
      const item = data.get(id);
      const value = item[prop];
      let exists = false;
      for (let j = 0; j < count; j++) {
        if (values2[j] == value) {
          exists = true;
          break;
        }
      }
      if (!exists && value !== void 0) {
        values2[count] = value;
        count++;
      }
    }
    return values2;
  }
  /**
   * Add a single item. Will fail when an item with the same id already exists.
   * @param item - A new item to be added.
   * @returns Added item's id. An id is generated when it is not present in the item.
   */
  _addItem(item) {
    const fullItem = ensureFullItem(item, this._idProp);
    const id = fullItem[this._idProp];
    if (this._data.has(id)) {
      throw new Error("Cannot add item: item with id " + id + " already exists");
    }
    this._data.set(id, fullItem);
    ++this.length;
    return id;
  }
  /**
   * Update a single item: merge with existing item.
   * Will fail when the item has no id, or when there does not exist an item with the same id.
   * @param update - The new item
   * @returns The id of the updated item.
   */
  _updateItem(update) {
    const id = update[this._idProp];
    if (id == null) {
      throw new Error("Cannot update item: item has no id (item: " + _JSON$stringify(update) + ")");
    }
    const item = this._data.get(id);
    if (!item) {
      throw new Error("Cannot update item: no item with id " + id + " found");
    }
    this._data.set(id, _objectSpread(_objectSpread({}, item), update));
    return id;
  }
  /** @inheritDoc */
  stream(ids) {
    if (ids) {
      const data = this._data;
      return new DataStream({
        *[_Symbol$iterator]() {
          for (const id of ids) {
            const item = data.get(id);
            if (item != null) {
              yield [id, item];
            }
          }
        }
      });
    } else {
      var _context7;
      return new DataStream({
        [_Symbol$iterator]: _bindInstanceProperty(_context7 = _entriesInstanceProperty(this._data)).call(_context7, this._data)
      });
    }
  }
  /* develblock:start */
  get testLeakData() {
    return this._data;
  }
  get testLeakIdProp() {
    return this._idProp;
  }
  get testLeakOptions() {
    return this._options;
  }
  get testLeakQueue() {
    return this._queue;
  }
  set testLeakQueue(v) {
    this._queue = v;
  }
};
var es_reflect_ownKeys = {};
var hasRequiredEs_reflect_ownKeys;
function requireEs_reflect_ownKeys() {
  if (hasRequiredEs_reflect_ownKeys) return es_reflect_ownKeys;
  hasRequiredEs_reflect_ownKeys = 1;
  var $ = require_export();
  var ownKeys2 = requireOwnKeys$3();
  $({ target: "Reflect", stat: true }, {
    ownKeys: ownKeys2
  });
  return es_reflect_ownKeys;
}
var ownKeys$2;
var hasRequiredOwnKeys$2;
function requireOwnKeys$2() {
  if (hasRequiredOwnKeys$2) return ownKeys$2;
  hasRequiredOwnKeys$2 = 1;
  requireEs_reflect_ownKeys();
  var path2 = requirePath();
  ownKeys$2 = path2.Reflect.ownKeys;
  return ownKeys$2;
}
var ownKeys$1;
var hasRequiredOwnKeys$1;
function requireOwnKeys$1() {
  if (hasRequiredOwnKeys$1) return ownKeys$1;
  hasRequiredOwnKeys$1 = 1;
  var parent = requireOwnKeys$2();
  ownKeys$1 = parent;
  return ownKeys$1;
}
var ownKeys;
var hasRequiredOwnKeys;
function requireOwnKeys() {
  if (hasRequiredOwnKeys) return ownKeys;
  hasRequiredOwnKeys = 1;
  ownKeys = requireOwnKeys$1();
  return ownKeys;
}
var ownKeysExports = requireOwnKeys();
var _Reflect$ownKeys = getDefaultExportFromCjs(ownKeysExports);
var DataView = class _DataView extends DataSetPart {
  /** @inheritDoc */
  get idProp() {
    return this.getDataSet().idProp;
  }
  // ids of the items currently in memory (just contains a boolean true)
  /**
   * Create a DataView.
   * @param data - The instance containing data (directly or indirectly).
   * @param options - Options to configure this data view.
   */
  constructor(data, options) {
    var _context;
    super();
    _defineProperty(this, "length", 0);
    _defineProperty(this, "_ids", new _Set());
    this._options = options || {};
    this._listener = _bindInstanceProperty(_context = this._onEvent).call(_context, this);
    this.setData(data);
  }
  // TODO: implement a function .config() to dynamically update things like configured filter
  // and trigger changes accordingly
  /**
   * Set a data source for the view.
   * @param data - The instance containing data (directly or indirectly).
   * @remarks
   * Note that when the data view is bound to a data set it won't be garbage
   * collected unless the data set is too. Use `dataView.setData(null)` or
   * `dataView.dispose()` to enable garbage collection before you lose the last
   * reference.
   */
  setData(data) {
    if (this._data) {
      if (this._data.off) {
        this._data.off("*", this._listener);
      }
      const ids = this._data.getIds({
        filter: _filterInstanceProperty(this._options)
      });
      const items = this._data.get(ids);
      this._ids.clear();
      this.length = 0;
      this._trigger("remove", {
        items: ids,
        oldData: items
      });
    }
    if (data != null) {
      this._data = data;
      const ids = this._data.getIds({
        filter: _filterInstanceProperty(this._options)
      });
      for (let i = 0, len = ids.length; i < len; i++) {
        const id = ids[i];
        this._ids.add(id);
      }
      this.length = ids.length;
      this._trigger("add", {
        items: ids
      });
    } else {
      this._data = new DataSet();
    }
    if (this._data.on) {
      this._data.on("*", this._listener);
    }
  }
  /**
   * Refresh the DataView.
   * Useful when the DataView has a filter function containing a variable parameter.
   */
  refresh() {
    const ids = this._data.getIds({
      filter: _filterInstanceProperty(this._options)
    });
    const oldIds = [...this._ids];
    const newIds = {};
    const addedIds = [];
    const removedIds = [];
    const removedItems = [];
    for (let i = 0, len = ids.length; i < len; i++) {
      const id = ids[i];
      newIds[id] = true;
      if (!this._ids.has(id)) {
        addedIds.push(id);
        this._ids.add(id);
      }
    }
    for (let i = 0, len = oldIds.length; i < len; i++) {
      const id = oldIds[i];
      const item = this._data.get(id);
      if (item == null) {
        console.error("If you see this, report it please.");
      } else if (!newIds[id]) {
        removedIds.push(id);
        removedItems.push(item);
        this._ids.delete(id);
      }
    }
    this.length += addedIds.length - removedIds.length;
    if (addedIds.length) {
      this._trigger("add", {
        items: addedIds
      });
    }
    if (removedIds.length) {
      this._trigger("remove", {
        items: removedIds,
        oldData: removedItems
      });
    }
  }
  /** @inheritDoc */
  get(first, second) {
    if (this._data == null) {
      return null;
    }
    let ids = null;
    let options;
    if (isId(first) || _Array$isArray(first)) {
      ids = first;
      options = second;
    } else {
      options = first;
    }
    const viewOptions = _Object$assign({}, this._options, options);
    const thisFilter = _filterInstanceProperty(this._options);
    const optionsFilter = options && _filterInstanceProperty(options);
    if (thisFilter && optionsFilter) {
      viewOptions.filter = (item) => {
        return thisFilter(item) && optionsFilter(item);
      };
    }
    if (ids == null) {
      return this._data.get(viewOptions);
    } else {
      return this._data.get(ids, viewOptions);
    }
  }
  /** @inheritDoc */
  getIds(options) {
    if (this._data.length) {
      const defaultFilter = _filterInstanceProperty(this._options);
      const optionsFilter = options != null ? _filterInstanceProperty(options) : null;
      let filter2;
      if (optionsFilter) {
        if (defaultFilter) {
          filter2 = (item) => {
            return defaultFilter(item) && optionsFilter(item);
          };
        } else {
          filter2 = optionsFilter;
        }
      } else {
        filter2 = defaultFilter;
      }
      return this._data.getIds({
        filter: filter2,
        order: options && options.order
      });
    } else {
      return [];
    }
  }
  /** @inheritDoc */
  forEach(callback, options) {
    if (this._data) {
      var _context2;
      const defaultFilter = _filterInstanceProperty(this._options);
      const optionsFilter = options && _filterInstanceProperty(options);
      let filter2;
      if (optionsFilter) {
        if (defaultFilter) {
          filter2 = function(item) {
            return defaultFilter(item) && optionsFilter(item);
          };
        } else {
          filter2 = optionsFilter;
        }
      } else {
        filter2 = defaultFilter;
      }
      _forEachInstanceProperty(_context2 = this._data).call(_context2, callback, {
        filter: filter2,
        order: options && options.order
      });
    }
  }
  /** @inheritDoc */
  map(callback, options) {
    if (this._data) {
      var _context3;
      const defaultFilter = _filterInstanceProperty(this._options);
      const optionsFilter = options && _filterInstanceProperty(options);
      let filter2;
      if (optionsFilter) {
        if (defaultFilter) {
          filter2 = (item) => {
            return defaultFilter(item) && optionsFilter(item);
          };
        } else {
          filter2 = optionsFilter;
        }
      } else {
        filter2 = defaultFilter;
      }
      return _mapInstanceProperty(_context3 = this._data).call(_context3, callback, {
        filter: filter2,
        order: options && options.order
      });
    } else {
      return [];
    }
  }
  /** @inheritDoc */
  getDataSet() {
    return this._data.getDataSet();
  }
  /** @inheritDoc */
  stream(ids) {
    var _context4;
    return this._data.stream(ids || {
      [_Symbol$iterator]: _bindInstanceProperty(_context4 = _keysInstanceProperty(this._ids)).call(_context4, this._ids)
    });
  }
  /**
   * Render the instance unusable prior to garbage collection.
   * @remarks
   * The intention of this method is to help discover scenarios where the data
   * view is being used when the programmer thinks it has been garbage collected
   * already. It's stricter version of `dataView.setData(null)`.
   */
  dispose() {
    var _this$_data;
    if ((_this$_data = this._data) !== null && _this$_data !== void 0 && _this$_data.off) {
      this._data.off("*", this._listener);
    }
    const message = "This data view has already been disposed of.";
    const replacement = {
      get: () => {
        throw new Error(message);
      },
      set: () => {
        throw new Error(message);
      },
      configurable: false
    };
    for (const key of _Reflect$ownKeys(_DataView.prototype)) {
      _Object$defineProperty(this, key, replacement);
    }
  }
  /**
   * Event listener. Will propagate all events from the connected data set to the subscribers of the DataView, but will filter the items and only trigger when there are changes in the filtered data set.
   * @param event - The name of the event.
   * @param params - Parameters of the event.
   * @param senderId - Id supplied by the sender.
   */
  _onEvent(event, params, senderId) {
    if (!params || !params.items || !this._data) {
      return;
    }
    const ids = params.items;
    const addedIds = [];
    const updatedIds = [];
    const removedIds = [];
    const oldItems = [];
    const updatedItems = [];
    const removedItems = [];
    switch (event) {
      case "add":
        for (let i = 0, len = ids.length; i < len; i++) {
          const id = ids[i];
          const item = this.get(id);
          if (item) {
            this._ids.add(id);
            addedIds.push(id);
          }
        }
        break;
      case "update":
        for (let i = 0, len = ids.length; i < len; i++) {
          const id = ids[i];
          const item = this.get(id);
          if (item) {
            if (this._ids.has(id)) {
              updatedIds.push(id);
              updatedItems.push(params.data[i]);
              oldItems.push(params.oldData[i]);
            } else {
              this._ids.add(id);
              addedIds.push(id);
            }
          } else {
            if (this._ids.has(id)) {
              this._ids.delete(id);
              removedIds.push(id);
              removedItems.push(params.oldData[i]);
            }
          }
        }
        break;
      case "remove":
        for (let i = 0, len = ids.length; i < len; i++) {
          const id = ids[i];
          if (this._ids.has(id)) {
            this._ids.delete(id);
            removedIds.push(id);
            removedItems.push(params.oldData[i]);
          }
        }
        break;
    }
    this.length += addedIds.length - removedIds.length;
    if (addedIds.length) {
      this._trigger("add", {
        items: addedIds
      }, senderId);
    }
    if (updatedIds.length) {
      this._trigger("update", {
        items: updatedIds,
        oldData: oldItems,
        data: updatedItems
      }, senderId);
    }
    if (removedIds.length) {
      this._trigger("remove", {
        items: removedIds,
        oldData: removedItems
      }, senderId);
    }
  }
};
function isDataSetLike(idProp, v) {
  return typeof v === "object" && v !== null && idProp === v.idProp && typeof v.add === "function" && typeof v.clear === "function" && typeof v.distinct === "function" && typeof _forEachInstanceProperty(v) === "function" && typeof v.get === "function" && typeof v.getDataSet === "function" && typeof v.getIds === "function" && typeof v.length === "number" && typeof _mapInstanceProperty(v) === "function" && typeof v.max === "function" && typeof v.min === "function" && typeof v.off === "function" && typeof v.on === "function" && typeof v.remove === "function" && typeof v.setOptions === "function" && typeof v.stream === "function" && typeof v.update === "function" && typeof v.updateOnly === "function";
}
function isDataViewLike(idProp, v) {
  return typeof v === "object" && v !== null && idProp === v.idProp && typeof _forEachInstanceProperty(v) === "function" && typeof v.get === "function" && typeof v.getDataSet === "function" && typeof v.getIds === "function" && typeof v.length === "number" && typeof _mapInstanceProperty(v) === "function" && typeof v.off === "function" && typeof v.on === "function" && typeof v.stream === "function" && isDataSetLike(idProp, v.getDataSet());
}
console.warn("You're running a development build.");

export {
  createNewDataPipeFrom,
  DELETE,
  Queue,
  DataStream,
  DataSet,
  DataView,
  isDataSetLike,
  isDataViewLike
};
/*! Bundled license information:

vis-data/peer/esm/vis-data.mjs:
  (**
   * vis-data
   * http://visjs.org/
   *
   * Manage unstructured data using DataSet. Add, update, and remove data, and listen for changes in the data.
   *
   * @version 8.0.1
   * @date    2025-07-13T02:52:37.151Z
   *
   * @copyright (c) 2011-2017 Almende B.V, http://almende.com
   * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
   *
   * @license
   * vis.js is dual licensed under both
   *
   *   1. The Apache 2.0 License
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   *   and
   *
   *   2. The MIT License
   *      http://opensource.org/licenses/MIT
   *
   * vis.js may be distributed under either license.
   *)
  (*! Hammer.JS - v2.0.17-rc - 2019-12-16
   * http://naver.github.io/egjs
   *
   * Forked By Naver egjs
   * Copyright (c) hammerjs
   * Licensed under the MIT license *)
  (**
   * vis-util
   * https://github.com/visjs/vis-util
   *
   * utilitie collection for visjs
   *
   * @version 6.0.0
   * @date    2025-07-12T18:02:43.836Z
   *
   * @copyright (c) 2011-2017 Almende B.V, http://almende.com
   * @copyright (c) 2017-2019 visjs contributors, https://github.com/visjs
   *
   * @license
   * vis.js is dual licensed under both
   *
   *   1. The Apache 2.0 License
   *      http://www.apache.org/licenses/LICENSE-2.0
   *
   *   and
   *
   *   2. The MIT License
   *      http://opensource.org/licenses/MIT
   *
   * vis.js may be distributed under either license.
   *)
*/
//# sourceMappingURL=chunk-JP4TMPI2.js.map
