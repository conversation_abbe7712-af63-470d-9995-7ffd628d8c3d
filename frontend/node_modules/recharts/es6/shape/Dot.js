function _extends() { return _extends = Object.assign ? Object.assign.bind() : function (n) { for (var e = 1; e < arguments.length; e++) { var t = arguments[e]; for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]); } return n; }, _extends.apply(null, arguments); }
/**
 * @fileOverview Dot
 */
import * as React from 'react';
import { clsx } from 'clsx';
import { adaptEventHandlers } from '../util/types';
import { svgPropertiesNoEvents } from '../util/svgPropertiesNoEvents';
export var Dot = props => {
  var {
    cx,
    cy,
    r,
    className
  } = props;
  var layerClass = clsx('recharts-dot', className);
  if (cx === +cx && cy === +cy && r === +r) {
    return /*#__PURE__*/React.createElement("circle", _extends({}, svgPropertiesNoEvents(props), adaptEventHandlers(props), {
      className: layerClass,
      cx: cx,
      cy: cy,
      r: r
    }));
  }
  return null;
};